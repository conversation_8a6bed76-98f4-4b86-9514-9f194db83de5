import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Grid,
  TextField,
  Typography,
  InputAdornment,
  FormControlLabel,
  RadioGroup,
  Radio,
  Autocomplete,
  Checkbox
} from '@mui/material';
import { LocalizationProvider, MobileDatePicker, MobileTimePicker } from '@mui/x-date-pickers';
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';
import dayjs from 'dayjs';
import { useEffect, useState } from 'react';
import { toast } from 'react-toastify';
import { useNavigate } from 'react-router-dom';
import { addCampaign } from 'api/campaigns.service';
import useAuth from 'hooks/useAuth';

const EventFormDialog = ({ open, onClose, event, MAX_LENGTH = 500, categories, fetchCampaigns }) => {
  const { user } = useAuth();
  const navigate = useNavigate();
  const [eventFormat, setEventFormat] = useState('physical');
  const [volunteerType, setVolunteerType] = useState('self');
  const [fullDay, setFullDay] = useState('no');

  const [formData, setFormData] = useState({ ...event });

  useEffect(() => {
    if (event) {
      setFormData({ ...event });
      if (event?.event_type) {
        setEventFormat(event.event_type);
      }
      if (event?.volunteer_type) {
        setVolunteerType(event.volunteer_type);
      }
      if (event?.fullday_event) {
        setFullDay(event.fullday_event);
      }
    }
  }, [event]);

  const handleChange = (field, value) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value
    }));
  };

  const handleCampaignDuplicate = async () => {
    const confirmApprove = window.confirm('Are you sure you want to duplicate this Event?');
    if (confirmApprove) {
      try {
        const { id, ...campaignWithoutId } = formData;
        const campaignData = await addCampaign(
          {
            ...campaignWithoutId,
            fullday_event: fullDay,
            event_type: eventFormat,
            volunteer_type: volunteerType,
            ngo_id: user?.ngo_id ? user?.ngo_id : null,
            event_start_time: formData.event_start_time,
            event_end_time: formData.event_end_time,
            imagesUpdated: null
          },
          'CampaignsTable'
        );
        toast.success('Event has been Duplicated!');
        onClose();
        fetchCampaigns();
      } catch (error) {
        console.error('Failed to duplicate:', error);
        toast.error('Failed to duplicate the event');
      }
    }
  };

  return (
    <Dialog
      open={open}
      onClose={(event, reason) => {
        if (reason !== 'backdropClick' && reason !== 'escapeKeyDown') {
          onClose();
        }
      }}
      maxWidth="50%"
      PaperProps={{
        sx: {
          top: 0,
          position: 'absolute',
          borderRadius: '16px',
          width: '100%',
          maxWidth: '600px'
        }
      }}
    >
      <LocalizationProvider dateAdapter={AdapterDayjs}>
        <DialogTitle>Duplicate Event</DialogTitle>
        <DialogContent dividers>
          <Grid container spacing={2}>
            <Grid item xs={12}>
              <Grid container spacing={3} direction="column">
                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    label="Event Title"
                    name="name"
                    value={formData.name || ''}
                    required
                    onChange={(e) => handleChange('name', e.target.value)}
                  />
                </Grid>

                <Grid item xs={12} sm={6}>
                  <Autocomplete
                    fullWidth
                    options={categories}
                    getOptionLabel={(option) => option.name || ''}
                    value={categories.find((cat) => cat.id === formData.category_id) || null}
                    onChange={(_, value) => handleChange('category_id', value?.id || '')}
                    renderInput={(params) => <TextField {...params} label="Category" name="category_id" required />}
                  />
                </Grid>

                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    multiline
                    rows={4}
                    label="Description"
                    required
                    name="description"
                    value={formData.description || ''}
                    onChange={(e) => handleChange('description', e.target.value)}
                  />
                  <Typography variant="caption" sx={{ display: 'block', textAlign: 'right', color: 'gray', mt: 0.5 }}>
                    {formData.description?.length ? MAX_LENGTH - formData.description.length : MAX_LENGTH} characters left
                  </Typography>
                </Grid>

                <Grid item xs={12} sm={6}>
                  <Grid container md={12} rowSpacing={2} columnSpacing={1}>
                    <Grid item xs={12} sm={6}>
                      <Typography variant="subtitle1" gutterBottom>
                        Event Format *
                      </Typography>
                      <RadioGroup row value={eventFormat} onChange={(e) => setEventFormat(e.target.value)}>
                        <FormControlLabel value="physical" control={<Radio color="primary" />} label="📍 Physical" />
                        <FormControlLabel value="virtual" control={<Radio color="primary" />} label="🎥 Virtual" />
                      </RadioGroup>
                    </Grid>

                    <Grid item xs={12} sm={6}>
                      <Typography variant="subtitle1" gutterBottom>
                        Volunteer Type *
                      </Typography>
                      <RadioGroup row value={volunteerType} onChange={(e) => setVolunteerType(e.target.value)}>
                        <FormControlLabel value="self" control={<Radio color="primary" />} label="Self (Free)" />
                        <FormControlLabel value="paid" control={<Radio color="primary" />} label="Paid" />
                      </RadioGroup>
                    </Grid>
                  </Grid>
                </Grid>

                <Grid item xs={12} sm={6}>
                  <Grid container md={12} rowSpacing={2} columnSpacing={1}>
                    <Grid item xs={12} sm={6}>
                      <TextField
                        type="text"
                        name="no_of_volunteers"
                        label="Number of Volunteers Required"
                        required
                        value={formData.no_of_volunteers || ''}
                        onChange={(e) => {
                          const val = e.target.value;
                          if (/^\d*$/.test(val)) handleChange('no_of_volunteers', val);
                        }}
                        fullWidth
                      />
                    </Grid>
                    <Grid item xs={12} sm={6}>
                      <TextField
                        name="amount_per_person"
                        label="Amount per Person (₹)"
                        required
                        value={volunteerType === 'self' ? 0 : formData.amount_per_person}
                        onChange={(e) => {
                          const val = e.target.value;
                          if (/^\d*$/.test(val) && Number(val) >= 1) handleChange('amount_per_person', val);
                        }}
                        InputProps={{ inputProps: { min: 1 } }}
                        disabled={volunteerType === 'self'}
                        helperText={volunteerType === 'self' ? 'Free volunteer (₹0)' : ''}
                        fullWidth
                      />
                    </Grid>

                    <Grid item xs={12} sm={6}>
                      <MobileDatePicker
                        label="Event Date *"
                        minDate={dayjs()}
                        maxDate={dayjs().add(102, 'day')}
                        format="DD/MM/YYYY"
                        value={formData.event_date ? dayjs(formData.event_date) : null} // convert to dayjs
                        onChange={(value) => handleChange('event_date', value)}
                        slotProps={{ textField: { fullWidth: true } }}
                      />
                    </Grid>

                    <Grid item xs={12}>
                      <FormControlLabel
                        control={<Checkbox checked={fullDay === 'yes'} onChange={(e) => setFullDay(e.target.checked ? 'yes' : 'no')} />}
                        label="Full Day Event *"
                      />
                    </Grid>
                    {fullDay == 'no' && (
                      <Grid item xs={12} sm={12}>
                        <Grid container md={12} rowSpacing={2} columnSpacing={1}>
                          <Grid item xs={12} sm={6}>
                            <MobileTimePicker
                              label="Start Time *"
                              value={formData.event_start_time ? dayjs(`2024-01-01T${formData.event_start_time}`) : null}
                              onChange={(value) => {
                                if (value) {
                                  handleChange('event_start_time', value.format('HH:mm:ss'));
                                } else {
                                  handleChange('event_start_time', '');
                                }
                              }}
                              slotProps={{ textField: { fullWidth: true } }}
                            />
                          </Grid>
                          <Grid item xs={12} sm={6}>
                            <MobileTimePicker
                              label="End Time *"
                              value={formData.event_end_time ? dayjs(`2024-01-01T${formData.event_end_time}`) : null}
                              onChange={(value) => {
                                if (value) {
                                  handleChange('event_end_time', value.format('HH:mm:ss'));
                                } else {
                                  handleChange('event_end_time', '');
                                }
                              }}
                              slotProps={{ textField: { fullWidth: true } }}
                            />
                          </Grid>
                        </Grid>
                      </Grid>
                    )}

                    {eventFormat === 'virtual' ? (
                      <Grid item xs={12} sm={6}>
                        {' '}
                        <TextField
                          label="Meeting Link *"
                          name="meeting_link"
                          value={formData.meeting_link || ''}
                          onChange={(e) => handleChange('meeting_link', e.target.value)}
                          fullWidth
                        />
                      </Grid>
                    ) : (
                      <>
                        <Grid item xs={12} sm={12}>
                          <Grid container md={12} rowSpacing={2} columnSpacing={1}>
                            <Grid item xs={12} sm={6}>
                              <TextField
                                fullWidth
                                required
                                label="Pincode"
                                name="pincode"
                                value={formData.pincode || ''}
                                // disabled={event.pincode}
                                onChange={(e) => handleChange('pincode', e.target.value)}
                                disabled
                                sx={{ marginRight: 5 }}
                              />
                            </Grid>
                            <Grid item xs={12} sm={6}>
                              <TextField
                                fullWidth
                                required
                                label="City"
                                name="place_name"
                                value={formData.place_name || ''}
                                disabled
                                // disabled={event.place_name}
                                onChange={(e) => handleChange('place_name', e.target.value)}
                              />
                            </Grid>

                            <Grid item xs={12} sm={6}>
                              <TextField
                                fullWidth
                                required
                                // disabled={event.state}
                                label="State"
                                name="state"
                                value={formData.state || ''}
                                onChange={(e) => handleChange('state', e.target.value)}
                                disabled
                              />
                            </Grid>
                            <Grid item xs={12} sm={6}>
                              <TextField
                                fullWidth
                                required
                                label="Country"
                                name="country"
                                value={formData.country || ''}
                                // disabled={event.country}
                                onChange={(e) => handleChange('country', e.target.value)}
                                disabled
                              />
                            </Grid>
                          </Grid>
                        </Grid>
                        <Grid item xs={12} sm={12}>
                          <TextField
                            fullWidth
                            multiline
                            rows={4}
                            label="Address"
                            required
                            name="current_address"
                            value={formData.current_address || ''}
                            onChange={(e) => {
                              handleChange('current_address', e.target.value);
                              // setAddressInformation((prev) => ({ ...prev, current_address: e.target.value }));
                            }}
                            InputLabelProps={{ shrink: true }}
                            // inputProps={{ maxLength: 300 }}
                            // InputProps={{
                            //   endAdornment: (
                            //     <InputAdornment position="end">
                            //       <Button
                            //         variant="contained"
                            //         color="primary"
                            //         onClick={() => handleSearchSubmit(event?.current_address)}
                            //         disabled={isLoadingLocate}
                            //       >
                            //         {isLoadingLocate ? <CircularProgress size={20} color="inherit" /> : 'Pin Your Address'}
                            //       </Button>
                            //     </InputAdornment>
                            //   )
                            // }}
                          />
                        </Grid>
                      </>
                    )}
                  </Grid>
                </Grid>
              </Grid>
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={onClose}>Cancel</Button>
          <Button variant="contained" onClick={handleCampaignDuplicate}>
            Duplicate
          </Button>
        </DialogActions>
      </LocalizationProvider>
    </Dialog>
  );
};

export default EventFormDialog;
