import axiosServices from 'utils/axios_node';

const API_BASE_URL = `${import.meta.env.VITE_APP_APPLICATION_API_URL}/api`;

export const getDocumentsMasterList = async (type = null, documentType, pageName) => {
  const pageNameParam = pageName ? `&pageName=${pageName}` : '';
  const response = await axiosServices.get(`${API_BASE_URL}/documents-master/?type=${type}&documentType=${documentType}${pageNameParam}`);
  return response?.data;
};

export const getNGODocumentsList = async (ngo_id, pageName) => {
  const pageNameParam = pageName ? `&pageName=${pageName}` : '';

  const response = await axiosServices.get(`${API_BASE_URL}/document-users/?ngo_id=${ngo_id}${pageNameParam}`);
  return response?.data;
};
export const patchNgoDocumentsList = async (id, payload) => {
  const response = await axiosServices.patch(`${API_BASE_URL}/document-users/${id}`, payload);
  return response?.data;
};

export const insertDocumentList = async (payload) => {
  const response = await axiosServices.post(`${API_BASE_URL}/documents-master`, payload);
  return response?.data;
};

export const updateDocumentList = async (id, payload) => {
  const response = await axiosServices.put(`${API_BASE_URL}/documents-master/${id}`, payload);
  return response?.data;
};
export const deleteDocumentList = async (id) => {
  const response = await axiosServices.delete(`${API_BASE_URL}/documents-master/${id}`);
  return response?.data;
};
