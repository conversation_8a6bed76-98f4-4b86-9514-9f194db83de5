import { Link as RouterLink } from 'react-router-dom';

// material-ui
import Link from '@mui/material/Link';
import Stack from '@mui/material/Stack';
import Typography from '@mui/material/Typography';
import { API_BASE_URL, DOCUMENTS_BASE_URL } from 'api/campaigns.service';

export default function Footer() {
  return (
    <Stack direction="row" justifyContent="space-between" alignItems="center" sx={{ p: '24px 16px 0px', mt: 'auto' }}>
      <Typography variant="caption">&copy; All rights reserved</Typography>
      <Stack spacing={1.5} direction="row" justifyContent="space-between" alignItems="center">
        <Link component={RouterLink} to="#" variant="caption" color="text.primary">
          {import.meta.env.VITE_APP_VERSION}
        </Link>
        <Link component={RouterLink} to="https://doright.world" target="_blank" variant="caption" color="text.primary">
          About us
        </Link>
        <Link
          component={RouterLink}
          to={`${DOCUMENTS_BASE_URL}/TheZenSocio_Privacy Policy.pdf`}
          target="_blank"
          variant="caption"
          color="text.primary"
        >
          Privacy
        </Link>
        <Link
          component={RouterLink}
          to={`${DOCUMENTS_BASE_URL}/TheZenSocio T&C_Donor and NGO.pdf`}
          target="_blank"
          variant="caption"
          color="text.primary"
        >
          Terms
        </Link>
      </Stack>
    </Stack>
  );
}
