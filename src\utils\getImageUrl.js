// export let ImagePath;

// (function (ImagePath) {
//   ImagePath['LANDING'] = 'landing';
//   ImagePath['USERS'] = 'users';
//   ImagePath['ECOMMERCE'] = 'e-commerce';
//   ImagePath['PROFILE'] = 'profile';
// })(ImagePath || (ImagePath = {}));

// ==============================|| NEW URL - GET IMAGE URL ||============================== //

// export function getImageUrl(name, path) {
//   return new URL(`/src/assets/images/${path}/${name}`, import.meta.url).href;
// }
export const getImageUrl = (imageName, basePath) => {
  return `${basePath}/${imageName}`;
};

export const ImagePath = {
  ECOMMERCE: 'http://localhost:4000/uploads' // Ensure this matches your backend route
};
