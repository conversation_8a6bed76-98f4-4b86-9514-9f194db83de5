import React, { useEffect, useState } from 'react';
import axios from 'axios';
import CustomReactTable from 'components/CustomerDataGrid'; // Adjust the import path as necessary
import {
  Button,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Grid
} from '@mui/material';
import { toast, ToastContainer } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import CustomerTable from 'sections/apps/customer/CustomerTable';
import { getUserPermissions } from 'utils/permissionUtils';
import { API_BASE_URL } from 'api/categories.service';
import TableActions from 'components/TableActions';
import useAuth from 'hooks/useAuth';
import { addSkillService, deleteSkillService, fetchSkillsService, updateSkillService } from './apis/skills.service';
import dayjs from 'dayjs';
import * as yup from 'yup';
import { Formik } from 'formik';

export default function SkillsTable() {
  const { user } = useAuth();
  const [skills, setSkills] = useState([]);
  const [openDialog, setOpenDialog] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [currentSkill, setCurrentSkill] = useState({
    name: '',
    status: 'Active'
  });

  //role based access
  const permissions = getUserPermissions(user);
  const canAdd = permissions?.Add?.Skills || false;
  const canEdit = permissions?.Edit?.Skills || false;
  const canDelete = permissions?.Delete?.Skills || false;

  useEffect(() => {
    fetchSkills();
  }, []);

  const fetchSkills = async () => {
    try {
      const data = await fetchSkillsService();
      setSkills(data);
    } catch (error) {
      console.error('Failed to fetch skills:', error);
      toast.error('Failed to fetch skills');
    }
  };

  const handleAddOrEdit = async (values) => {
    try {
      if (isEditing) {
        await updateSkillService(values.id, values);
        toast.success('Skill updated successfully!');
      } else {
        await addSkillService(values);
        toast.success('Skill added successfully!');
      }
      setOpenDialog(false);
      fetchSkills();
    } catch (error) {
      console.error('Failed to save skill:', error);
      toast.error(error.response?.data?.message || 'Failed to save skill');
    }
  };

  const handleDelete = async (id) => {
    if (!canDelete) {
      toast.error('You do not have permission to delete Skills.');
      return;
    }
    const confirmDelete = window.confirm('Are you sure you want to delete this skill?');
    if (confirmDelete) {
      try {
        await deleteSkillService(id);

        toast.success('Skill deleted successfully!');
        fetchSkills();
      } catch (error) {
        console.error('Failed to delete skill:', error);
        toast.error('Failed to delete skill');
      }
    }
  };

  const openAddDialog = () => {
    if (!canAdd) {
      toast.error('You do not have permission to Add Skills.');
      return;
    }
    setCurrentSkill({ id: 0, name: '', slug: '', status: 'Active' });
    setOpenDialog(true);
    setIsEditing(false);
  };

  const openEditDialog = (skill) => {
    if (!canEdit) {
      toast.error('You do not have permission to edit Skills.');
      return;
    }
    setCurrentSkill(skill);
    setIsEditing(true);
    setOpenDialog(true);
  };
  const columns = [
    {
      accessorKey: 'name',
      header: 'Name',
      showByDefault: true,
      cell: ({ cell }) => {
        return (
          <Button variant="text" onClick={() => openEditDialog(cell.row.original)}>
            {cell.row.original.name}
          </Button>
        );
      }
    },
    { accessorKey: 'status', header: 'Status', showByDefault: true },
    {
      accessorKey: 'createdAt',
      header: 'Created At',
      showByDefault: true,
      cell: (cell) => {
        const formattedDate = dayjs(cell.row.original.createdAt).format('DD-MM-YYYY hh:mm A');
        return <span>{formattedDate}</span>;
      }
    },
    {
      accessorKey: 'updatedAt',
      header: 'Updated At',
      showByDefault: true,
      cell: (cell) => {
        const formattedDate = dayjs(cell.row.original.updatedAt).format('DD-MM-YYYY hh:mm A');
        return <span>{formattedDate}</span>;
      }
    },
    {
      id: 'actions',
      header: 'Action',
      cell: (cell) => {
        return <TableActions handleEditClick={openEditDialog} cell={cell} handleDeleteClick={handleDelete} />;
      }
    }
  ];

  return (
    <div>
      <CustomerTable data={skills} columns={columns} modalToggler={openAddDialog} category={'Skill'} />

      <Dialog
        open={openDialog}
        onClose={(event, reason) => {
          if (reason !== 'backdropClick' && reason !== 'escapeKeyDown') {
            setOpenDialog(false);
          }
        }}
        maxWidth="50%"
        PaperProps={{
          sx: {
            top: 0,
            position: 'absolute',
            borderRadius: '16px',
            width: '100%',
            maxWidth: '600px'
          }
        }}
      >
        <Formik
          initialValues={{
            ...currentSkill
          }}
          validationSchema={yup.object().shape({
            name: yup.string().max(150, 'Name must be at most 150 characters long').required('Name is required'),

            status: yup.string().required('Please select a status')
          })}
          onSubmit={async (values) => {
            await handleAddOrEdit(values);
          }}
        >
          {({ values, handleSubmit, handleChange, handleBlur, setFieldValue, touched, errors }) => (
            <form autoComplete="off" onSubmit={handleSubmit}>
              <DialogTitle>{isEditing ? 'Edit Skill' : 'Add Skill'}</DialogTitle>
              <DialogContent>
                <Grid container spacing={1}>
                  <Grid item xs={12}>
                    <TextField
                      autoFocus
                      margin="dense"
                      label="Name"
                      type="text"
                      fullWidth
                      variant="outlined"
                      name="name"
                      // value={currentSkill?.name || ''}
                      // onChange={(e) => setCurrentSkill({ ...currentSkill, name: e.target.value })}
                      onChange={handleChange}
                      onBlur={handleBlur}
                      value={values.name}
                      error={Boolean(touched.name && errors.name)}
                      helperText={touched.name && errors.name}
                    />
                  </Grid>
                  <Grid item xs={12}>
                    <FormControl fullWidth margin="dense" variant="outlined">
                      <InputLabel>Status</InputLabel>
                      <Select
                        value={values?.status || ''}
                        // onChange={(e) => setCurrentSkill({ ...currentSkill, status: e.target.value })}
                        onChange={handleChange}
                        label="Status"
                        name="status"
                      >
                        <MenuItem value="Active">Active</MenuItem>
                        <MenuItem value="Inactive">Inactive</MenuItem>
                      </Select>
                    </FormControl>
                  </Grid>
                </Grid>
              </DialogContent>
              <DialogActions>
                <Button onClick={() => setOpenDialog(false)} color="primary">
                  Cancel
                </Button>
                <Button type="submit" color="primary">
                  {isEditing ? 'Update' : 'Add'}
                </Button>
              </DialogActions>
            </form>
          )}
        </Formik>
      </Dialog>

      <ToastContainer />
    </div>
  );
}
