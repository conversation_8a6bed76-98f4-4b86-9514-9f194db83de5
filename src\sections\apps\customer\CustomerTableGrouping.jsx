import PropTypes from 'prop-types';
import { Fragment, useMemo, useRef, useState } from 'react';

// material-ui
import { alpha, useTheme } from '@mui/material/styles';
import useMediaQuery from '@mui/material/useMediaQuery';
import Button from '@mui/material/Button';
import Divider from '@mui/material/Divider';
import MenuItem from '@mui/material/MenuItem';
import Select from '@mui/material/Select';
import Stack from '@mui/material/Stack';
import Table from '@mui/material/Table';
import TableBody from '@mui/material/TableBody';
import TableCell from '@mui/material/TableCell';
import TableContainer from '@mui/material/TableContainer';
import TableHead from '@mui/material/TableHead';
import TableRow from '@mui/material/TableRow';
import Box from '@mui/material/Box';

// third-party
import {
  flexRender,
  getCoreRowModel,
  getSortedRowModel,
  getPaginationRowModel,
  getFilteredRowModel,
  getExpandedRowModel,
  useReactTable,
  getGroupedRowModel
} from '@tanstack/react-table';
import { rankItem } from '@tanstack/match-sorter-utils';

// project-import
import ScrollX from 'components/ScrollX';
import MainCard from 'components/MainCard';

import {
  CSVExport,
  DebouncedInput,
  HeaderSort,
  RowSelection,
  SelectColumnSorting,
  TablePagination
} from 'components/third-party/react-table';

import ExpandingUserDetail from 'sections/apps/customer/ExpandingUserDetail';

// assets
import PlusOutlined from '@ant-design/icons/PlusOutlined';
import { COMMON_STATUS_LIST } from 'utils/statusconstans';
import { Paper } from '@mui/material';
import { useVirtualizer } from '@tanstack/react-virtual';
import IconButton from 'components/@extended/IconButton';
import { DownOutlined, GroupOutlined, RightOutlined, UngroupOutlined } from '@ant-design/icons';

export const fuzzyFilter = (row, columnId, value, addMeta) => {
  // rank the item
  const itemRank = rankItem(row.getValue(columnId), value);

  // store the ranking info
  addMeta(itemRank);

  // return if the item should be filtered in/out
  return itemRank.passed;
};

// ==============================|| REACT TABLE - LIST ||============================== //

export default function CustomerTableGrouping({ data, columns, modalToggler, category, statusList }) {
  const theme = useTheme();
  const downSM = useMediaQuery(theme.breakpoints.down('sm'));
  const updatedStatusList = statusList?.length > 0 ? statusList : COMMON_STATUS_LIST;

  const [sorting, setSorting] = useState([
    {
      id: 'id',
      desc: true
    }
  ]);
  const [rowSelection, setRowSelection] = useState({});
  const [globalFilter, setGlobalFilter] = useState('');
  const [statusFilter, setStatusFilter] = useState(updatedStatusList[0]);
  const [grouping, setGrouping] = useState(['document_type']);

  const filteredData = useMemo(() => {
    if (!statusFilter || statusFilter === 'All') return data;

    return data.filter((customer) => customer.status === statusFilter);
  }, [statusFilter, data]);

  const table = useReactTable({
    data: filteredData,
    columns,
    state: {
      sorting,
      rowSelection,
      globalFilter,
      grouping
    },
    enableRowSelection: true,
    onSortingChange: setSorting,
    onRowSelectionChange: setRowSelection,
    onGlobalFilterChange: setGlobalFilter,
    getRowCanExpand: () => true,
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getExpandedRowModel: getExpandedRowModel(),
    getGroupedRowModel: getGroupedRowModel(),
    globalFilterFn: fuzzyFilter,
    onGroupingChange: setGrouping,
    debugTable: true
  });

  const backColor = alpha(theme.palette.primary.lighter, 0.1);
  let headers = [];
  columns.map(
    (columns) =>
      // @ts-ignore
      columns.accessorKey &&
      headers.push({
        label: typeof columns.header === 'string' ? columns.header : '#',
        // @ts-ignore
        key: columns.accessorKey
      })
  );

  const tableContainerRef = useRef(null);

  const { rows } = table.getRowModel();

  const rowVirtualizer = useVirtualizer({
    count: rows.length,
    getScrollElement: () => tableContainerRef.current,
    estimateSize: () => 34,
    overscan: 10
  });

  const virtualRows = rowVirtualizer.getVirtualItems();
  const totalSize = rowVirtualizer.getTotalSize();

  const paddingTop = virtualRows.length > 0 ? virtualRows?.[0]?.start || 0 : 0;
  const paddingBottom = virtualRows.length > 0 ? totalSize - (virtualRows?.[virtualRows.length - 1]?.end || 0) : 0;

  return (
    <MainCard
      content={false}
      title="Document Master"
      secondary={<CSVExport {...{ data: table.getGroupedRowModel().rows.map((row) => row.original), headers, filename: 'grouping.csv' }} />}
    >
      <ScrollX>
        <TableContainer component={Paper} ref={tableContainerRef} sx={{ height: 544, overflow: 'auto' }}>
          <Table size="small">
            <TableHead className="sticky-header">
              {table.getHeaderGroups().map((headerGroup) => (
                <TableRow key={headerGroup.id}>
                  {headerGroup.headers.map((header) => (
                    <TableCell key={header.id} {...header.column.columnDef.meta}>
                      {header.isPlaceholder ? null : (
                        <Stack direction="row" alignItems="center">
                          {/* {header.column.getCanGroup() && (
                            <IconButton
                              color={header.column.getIsGrouped() ? 'error' : 'primary'}
                              onClick={header.column.getToggleGroupingHandler()}
                              size="small"
                              sx={{ p: 0, width: 24, height: 24, fontSize: '1rem', mr: 0.75 }}
                            >
                              {header.column.getIsGrouped() ? <UngroupOutlined /> : <GroupOutlined />}
                            </IconButton>
                          )} */}
                          {flexRender(header.column.columnDef.header, header.getContext())}
                        </Stack>
                      )}
                    </TableCell>
                  ))}
                </TableRow>
              ))}
            </TableHead>
            <TableBody>
              {paddingTop > 0 && (
                <TableRow>
                  <TableCell sx={{ height: `${paddingTop}px` }} />
                </TableRow>
              )}
              {virtualRows.map((virtualRow) => {
                const row = rows[virtualRow.index];
                return (
                  <TableRow key={row.id}>
                    {row.getVisibleCells().map((cell) => {
                      let bgcolor = 'background.paper';
                      if (cell.getIsGrouped()) bgcolor = 'primary.lighter';
                      if (cell.getIsAggregated()) bgcolor = 'warning.lighter';
                      if (cell.getIsPlaceholder()) bgcolor = 'error.lighter';

                      if (cell.column.columnDef.meta !== undefined && cell.column.getCanSort()) {
                        Object.assign(cell.column.columnDef.meta, {
                          style: { background: bgcolor }
                        });
                      }

                      return (
                        <TableCell
                          key={cell.id}
                          {...cell.column.columnDef.meta}
                          sx={{ bgcolor }}
                          {...(cell.getIsGrouped() &&
                            cell.column.columnDef.meta === undefined && {
                              style: { background: bgcolor }
                            })}
                        >
                          {cell.getIsGrouped() ? (
                            <Stack direction="row" alignItems="center" spacing={0.5}>
                              <IconButton
                                color="secondary"
                                onClick={row.getToggleExpandedHandler()}
                                size="small"
                                sx={{ p: 0, width: 24, height: 24 }}
                              >
                                {row.getIsExpanded() ? <DownOutlined /> : <RightOutlined />}
                              </IconButton>
                              <Box>{flexRender(cell.column.columnDef.cell, cell.getContext())}</Box> <Box>({row.subRows.length})</Box>
                            </Stack>
                          ) : cell.getIsAggregated() ? (
                            flexRender(cell.column.columnDef.aggregatedCell ?? cell.column.columnDef.cell, cell.getContext())
                          ) : cell.getIsPlaceholder() ? null : (
                            flexRender(cell.column.columnDef.cell, cell.getContext())
                          )}
                        </TableCell>
                      );
                    })}
                  </TableRow>
                );
              })}
              {paddingBottom > 0 && (
                <TableRow>
                  <TableCell sx={{ height: `${paddingBottom}px` }} />
                </TableRow>
              )}
            </TableBody>
          </Table>
        </TableContainer>
      </ScrollX>
    </MainCard>
  );
}

CustomerTableGrouping.propTypes = { data: PropTypes.array, columns: PropTypes.array, modalToggler: PropTypes.func };
