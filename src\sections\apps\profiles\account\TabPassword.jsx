import React, { useState, useEffect } from 'react';
import axios from 'axios';
import { Button, Typography, List, ListItem, ListItemText, TextField, Stack } from '@mui/material';
import { ToastContainer, toast } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';

import { API_BASE_URL } from 'api/categories.service';
import { deleteDocument, getDocuments, uploadDocuments } from './tabs.service';
const DocumentUpload = () => {
  const [selectedFiles, setSelectedFiles] = useState([]);
  const [documents, setDocuments] = useState([]);

  useEffect(() => {
    fetchDocuments();
  }, []);

  const fetchDocuments = async () => {
    try {
      const response = await getDocuments();
      setDocuments(response);
    } catch (error) {
      console.error('Failed to fetch documents:', error);
    }
  };

  const handleFileChange = (event) => {
    setSelectedFiles(event.target.files ? Array.from(event.target.files) : []);
  };

  const handleUpload = async () => {
    if (selectedFiles.length === 0) return;

    const formData = new FormData();
    selectedFiles.forEach((file) => {
      formData.append('documents', file); // Append each file to the FormData
    });

    try {
      // await axios.post(`${API_BASE_URL}documents/upload`, formData, {
      //   headers: { 'Content-Type': 'multipart/form-data' }
      // });
      await uploadDocuments(formData, {
        headers: { 'Content-Type': 'multipart/form-data' }
      });
      toast.success('Files uploaded successfully');
      setSelectedFiles([]); // Clear selected files
      fetchDocuments(); // Refresh the documents list
    } catch (error) {
      console.error('Failed to upload documents:', error);
      toast.error('Failed to upload documents');
    }
  };

  const handleDelete = async (filename) => {
    try {
      await deleteDocument(filename);
      toast.success('Document deleted successfully');
      fetchDocuments();
    } catch (error) {
      console.error('Failed to delete document:', error);
      toast.error('Failed to delete document');
    }
  };

  return (
    <Stack spacing={2}>
      <Typography variant="h5">Upload Document</Typography>
      <TextField
        type="file"
        inputProps={{ multiple: true }} // Allow multiple files
        onChange={handleFileChange}
      />
      <Button variant="contained" color="primary" onClick={handleUpload} disabled={selectedFiles.length === 0}>
        Upload
      </Button>

      <Typography variant="h6">Documents List</Typography>
      <List>
        {documents.map((doc) => (
          <ListItem key={doc.name}>
            <ListItemText primary={doc.name} />
            <Button variant="contained" color="error" onClick={() => handleDelete(doc.name)}>
              Delete
            </Button>
          </ListItem>
        ))}
      </List>
      <ToastContainer />
    </Stack>
  );
};

export default DocumentUpload;
