import { useEffect, useRef, useState } from 'react';
import { toast } from 'react-toastify';

// material-ui
import { useTheme } from '@mui/material/styles';
import useMediaQuery from '@mui/material/useMediaQuery';
import Avatar from '@mui/material/Avatar';
import Badge from '@mui/material/Badge';
import Box from '@mui/material/Box';
import ClickAwayListener from '@mui/material/ClickAwayListener';
import Divider from '@mui/material/Divider';
import List from '@mui/material/List';
import ListItemButton from '@mui/material/ListItemButton';
import ListItemAvatar from '@mui/material/ListItemAvatar';
import ListItemText from '@mui/material/ListItemText';
import ListItemSecondaryAction from '@mui/material/ListItemSecondaryAction';
import Paper from '@mui/material/Paper';
import Popper from '@mui/material/Popper';
import Tooltip from '@mui/material/Tooltip';
import Typography from '@mui/material/Typography';

// project import
import MainCard from 'components/MainCard';
import IconButton from 'components/@extended/IconButton';
import Transitions from 'components/@extended/Transitions';
import { ThemeMode } from 'config';

// assets
import BellOutlined from '@ant-design/icons/BellOutlined';
import CheckCircleOutlined from '@ant-design/icons/CheckCircleOutlined';

import { API_BASE_URL } from 'api/campaigns.service';
import useAuth from 'hooks/useAuth';
import { getNotifications, patchNotifications } from 'pages/masters/apis/notification.service';
import { getCommentText } from 'utils/permissionUtils';
import dayjs from 'dayjs';
// sx styles
const avatarSX = {
  width: 36,
  height: 36,
  fontSize: '1rem'
};

const actionSX = {
  mt: '6px',
  ml: 1,
  top: 'auto',
  right: 'auto',
  alignSelf: 'flex-start',
  transform: 'none'
};

export default function Notification() {
  const theme = useTheme();
  const matchesXs = useMediaQuery(theme.breakpoints.down('md'));
  const { user } = useAuth();
  const anchorRef = useRef(null);
  const [notifications, setNotifications] = useState([]);
  const [read, setRead] = useState(0); // Update read notifications count dynamically
  const [open, setOpen] = useState(false);

  const fetchComments = async (newNgoId) => {
    try {
      const response = await getNotifications(newNgoId);
      setNotifications(response || []);
    } catch (error) {
      console.error('Failed to fetch notifications:', error);
      toast.error('Failed to fetch notifications');
    }
  };

  useEffect(() => {
    if (user?.ngo_id) {
      fetchComments(user?.ngo_id);
    }
    // fetchComments(58381);
  }, []);

  const handleToggle = () => {
    setOpen((prevOpen) => !prevOpen);
  };

  const handleClose = (event) => {
    if (anchorRef.current && anchorRef.current.contains(event.target)) {
      return;
    }
    setOpen(false);
  };

  const iconBackColorOpen = theme.palette.mode === ThemeMode.DARK ? 'background.default' : 'grey.100';

  return (
    <Box sx={{ flexShrink: 0, ml: 0.75 }}>
      <IconButton
        color="secondary"
        variant="light"
        sx={{ color: 'text.primary', bgcolor: open ? iconBackColorOpen : 'transparent' }}
        aria-label="open profile"
        ref={anchorRef}
        aria-controls={open ? 'profile-grow' : undefined}
        aria-haspopup="true"
        onClick={handleToggle}
      >
        <Badge badgeContent={read} color="primary">
          <BellOutlined />
        </Badge>
      </IconButton>
      <Popper
        placement={matchesXs ? 'bottom' : 'bottom-end'}
        open={open}
        anchorEl={anchorRef.current}
        role={undefined}
        transition
        disablePortal
        popperOptions={{ modifiers: [{ name: 'offset', options: { offset: [matchesXs ? -5 : 0, 9] } }] }}
      >
        {({ TransitionProps }) => (
          <Transitions type="grow" position={matchesXs ? 'top' : 'top-right'} in={open} {...TransitionProps}>
            <Paper sx={{ boxShadow: theme.customShadows.z1, width: '100%', minWidth: 285, maxWidth: { xs: 285, md: 500 } }}>
              <ClickAwayListener onClickAway={handleClose}>
                <MainCard
                  title="Notification"
                  elevation={0}
                  border={false}
                  content={false}
                  secondary={
                    read > 0 && (
                      <Tooltip title="Mark as all read">
                        <IconButton color="success" size="small" onClick={() => setRead(0)}>
                          <CheckCircleOutlined style={{ fontSize: '1.15rem' }} />
                        </IconButton>
                      </Tooltip>
                    )
                  }
                >
                  <List
                    component="nav"
                    sx={{
                      p: 0,
                      maxHeight: 300, // Set max height to limit notifications shown at once (~5-6 items)
                      overflowY: 'auto', // Enable scrolling when content overflows
                      '&::-webkit-scrollbar': { width: '6px' },
                      '&::-webkit-scrollbar-thumb': { background: theme.palette.grey[400], borderRadius: '10px' },
                      '& .MuiListItemButton-root': {
                        py: 0.5,
                        '&.Mui-selected': { bgcolor: 'grey.50', color: 'text.primary' },
                        '& .MuiListItemSecondaryAction-root': { ...actionSX, position: 'relative' }
                      }
                    }}
                  >
                    {notifications.length > 0 ? (
                      <>
                        {notifications.some((notification) => notification.messageRead === 'no') ? (
                          notifications.map((notification) =>
                            notification.messageRead === 'no' ? (
                              <div key={notification.id}>
                                <ListItemButton>
                                  <ListItemText
                                    primary={
                                      <Box display="flex" alignItems="center" gap={1}>
                                        {/* Capsule-shaped Category Label */}
                                        <Typography
                                          variant="caption"
                                          sx={{
                                            bgcolor: 'primary.main',
                                            color: 'white',
                                            fontWeight: 'bold',
                                            borderRadius: '12px',
                                            px: 1.5,
                                            py: 0.5,
                                            display: 'inline-block',
                                            fontSize: '0.75rem'
                                          }}
                                        >
                                          {notification.type.toUpperCase()}
                                        </Typography>

                                        {/* Notification Description */}
                                        <Typography variant="h6">{getCommentText(notification.description)}</Typography>
                                      </Box>
                                    }
                                    secondary={
                                      <Typography variant="caption" sx={{ color: 'text.secondary' }}>
                                        Received on : {dayjs(notification.createdAt).format('DD/MM/YYYY')}
                                      </Typography>
                                    }
                                  />
                                  <ListItemSecondaryAction>
                                    <Typography variant="caption" noWrap>
                                      {new Date(notification.createdAt).toLocaleTimeString()}
                                    </Typography>
                                    <Tooltip title="Mark as Read">
                                      <IconButton
                                        color="primary"
                                        size="small"
                                        onClick={async () => {
                                          try {
                                            await patchNotifications(notification.id, {
                                              messageRead: 'yes'
                                            });

                                            // Update local state
                                            setNotifications((prevNotifications) =>
                                              prevNotifications.map((notif) =>
                                                notif.id === notification.id ? { ...notif, messageRead: 'yes' } : notif
                                              )
                                            );

                                            // Optionally update the read count
                                            setRead((prev) => Math.max(0, prev - 1));
                                            toast.success('Notification marked as read.');
                                          } catch (error) {
                                            console.error('Failed to mark notification as read:', error);
                                            toast.error('Failed to mark notification as read.');
                                          }
                                        }}
                                      >
                                        <CheckCircleOutlined style={{ fontSize: '1.15rem' }} />
                                      </IconButton>
                                    </Tooltip>
                                  </ListItemSecondaryAction>
                                </ListItemButton>

                                <Divider />
                              </div>
                            ) : null
                          )
                        ) : (
                          <Typography variant="body2" textAlign="center" color="red" padding={2}>
                            You are up to date with notifications
                          </Typography>
                        )}
                      </>
                    ) : (
                      <Typography variant="body2" textAlign="center" color="red" padding={2}>
                        No notifications available
                      </Typography>
                    )}
                  </List>
                </MainCard>
              </ClickAwayListener>
            </Paper>
          </Transitions>
        )}
      </Popper>
    </Box>
  );
}
