import React, { useEffect, useState } from 'react';
import PropTypes from 'prop-types';
import {
  Card,
  CardHeader,
  CardMedia,
  CardContent,
  CardActions,
  IconButton,
  Typography,
  Button,
  Chip,
  Grid,
  Menu,
  MenuItem,
  Link
} from '@mui/material';

import {
  EditOutlined,
  DeleteOutlined,
  FacebookOutlined,
  InstagramOutlined,
  TwitchOutlined,
  YoutubeOutlined,
  MoneyCollectOutlined,
  MoreOutlined
} from '@ant-design/icons';
import { IMAGE_BASE_URL } from 'sections/apps/profiles/profile.service';
import { getKindDonationByCampaignId } from 'api/campaigns.service';

const CampaignCard = ({ campaign, onEdit, onDelete }) => {
  const [anchorEl, setAnchorEl] = useState(null);
  const [inputList, setInputList] = useState([{ item: null, quantity: '', unit: '' }]);

  useEffect(() => {
    const fetchKindDonation = async () => {
      try {
        const response = await getKindDonationByCampaignId(campaign?.id);
        const inKindList = response.map((item) => ({
          item: item.item_name,
          quantity: item.quantity,
          unit: item.unit_of_measure
        }));
        setInputList(inKindList.length > 0 ? inKindList : [{ item: null, quantity: '', unit: '' }]);
      } catch (error) {
        console.error(error);
      }
    };
    fetchKindDonation();
  }, [campaign]);

  const handleMenuClick = (event) => {
    setAnchorEl(event.currentTarget);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
  };

  const handleEdit = () => {
    handleMenuClose();
    onEdit(campaign);
  };

  const handleDelete = () => {
    handleMenuClose();
    onDelete(campaign);
  };

  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', { year: 'numeric', month: 'short', day: 'numeric' });
  };

  return (
    <Card elevation={3} sx={{ maxWidth: 345, m: 2 }}>
      <CardHeader
        action={
          <IconButton aria-label="settings" onClick={handleMenuClick}>
            <MoreOutlined />
          </IconButton>
        }
        title={<Typography variant="h5">{campaign.name}</Typography>}
        subheader={
          <Typography variant="body2" color="text.secondary" sx={{ display: 'flex', alignItems: 'center' }}>
            <MoneyCollectOutlined style={{ marginRight: 8 }} />
            {formatDate(campaign.campaign_start_date)}
            {campaign.campaign_end_date && ` - ${formatDate(campaign.campaign_end_date)}`}
            {campaign.sameday_event === 'no' ? `${formatDate(campaign.campaign_end_date)}` : ' - Single Day Event'}
          </Typography>
        }
      />
      <Menu anchorEl={anchorEl} open={Boolean(anchorEl)} onClose={handleMenuClose}>
        <MenuItem onClick={handleEdit}>
          <EditOutlined style={{ marginRight: 8 }} /> Edit
        </MenuItem>
        <MenuItem onClick={handleDelete}>
          <DeleteOutlined style={{ marginRight: 8 }} /> Delete
        </MenuItem>
      </Menu>
      <CardMedia component="img" height="140" image={`${IMAGE_BASE_URL}/campaigns/${campaign?.fileName}`} alt={campaign.name} />
      <CardContent>
        <Typography variant="body2" color="text.secondary" gutterBottom>
          {campaign.description}
        </Typography>
        <Typography variant="subtitle1">{campaign.format === 'Physical' ? 'Physical event' : 'Virtual Event'}</Typography>

        <Grid container spacing={1} sx={{ mt: 2 }}>
          <Grid item xs={12}>
            <Chip label={campaign.status} color={campaign.status === 'Active' ? 'success' : 'default'} size="small" />
            {campaign.donor_target_type && (
              <Chip
                label={campaign.donor_target_type === 'monetary_donation' ? 'Moneytory Donation' : 'Kind Donation'}
                color="primary"
                size="small"
                sx={{ ml: 1 }}
              />
            )}
          </Grid>

          <Grid item xs={12}>
            <Typography variant="body2" color="text.secondary" sx={{ display: 'flex', alignItems: 'center' }}>
              <MoneyCollectOutlined style={{ marginRight: 8 }} />
              {`${campaign.city ? campaign.city : 'N/A'}, ${campaign.state ? campaign.state : 'N/A'}, ${campaign.country ? campaign.country : 'N/A'}`}
            </Typography>
          </Grid>
          {campaign.donor_target_type === 'monetary_donation' && campaign.fund_raising_target && (
            <Grid item xs={12}>
              <Typography variant="body2" color="text.secondary" sx={{ display: 'flex', alignItems: 'center' }}>
                <MoneyCollectOutlined style={{ marginRight: 8 }} />
                Target: ${campaign.fund_raising_target.toLocaleString()}
              </Typography>
            </Grid>
          )}
          {campaign.donor_target_type === 'in_kind' && inputList && (
            <>
              <Grid item xs={12}>
                {inputList.map((item, index) => (
                  <Typography key={index} variant="body2" color="text.secondary" sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <MoneyCollectOutlined />
                    Name: {item.item}, Quantity: {item.quantity}, Unit: {item.unit}
                  </Typography>
                ))}
              </Grid>
            </>
          )}
        </Grid>
        {campaign.skills && (
          <Typography variant="body2" color="text.secondary" sx={{ mt: 2 }}>
            Skills: {campaign.skills}
          </Typography>
        )}
        <Typography variant="body2" sx={{ mt: 2 }}>
          Impact Goal: {campaign.impact_goal}
        </Typography>
      </CardContent>
      <CardActions disableSpacing>
        {campaign.facebook_url && (
          <IconButton aria-label="facebook" href={campaign.facebook_url} target="_blank">
            <FacebookOutlined style={{ color: '#1877F2' }} />
          </IconButton>
        )}
        {campaign.instagram_url && (
          <IconButton aria-label="instagram" href={campaign.instagram_url} target="_blank">
            <InstagramOutlined style={{ color: '#E4405F' }} />
          </IconButton>
        )}
        {campaign.twitter_url && (
          <IconButton aria-label="twitter" href={campaign.twitter_url} target="_blank">
            <TwitchOutlined style={{ color: '#6441A4' }} />
          </IconButton>
        )}
        {campaign.youtube_url && (
          <IconButton aria-label="youtube" href={campaign.youtube_url} target="_blank">
            <YoutubeOutlined style={{ color: '#FF0000' }} />
          </IconButton>
        )}
        {campaign?.meeting_link && (
          <Button
            size="small"
            color="primary"
            sx={{ marginLeft: 'auto' }}
            component={Link}
            href={campaign.meeting_link}
            target="_blank"
            disabled={!campaign.meeting_link}
          >
            Join Meeting
          </Button>
        )}
      </CardActions>
    </Card>
  );
};

export default CampaignCard;
