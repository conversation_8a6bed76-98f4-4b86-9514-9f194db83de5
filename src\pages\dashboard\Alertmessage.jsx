import { QuestionCircleOutlined } from '@ant-design/icons';
import { Alert, Button } from '@mui/material';
import { Fragment } from 'react';
import { Link } from 'react-router-dom';

const AlertMessage = ({ message, status, type = 'ngo' }) => {
  return (
    <Alert
      variant="border"
      color="primary"
      icon={<QuestionCircleOutlined />}
      action={
        <Fragment>
          {type === 'ngo' && (
            <Button variant="contained" size="small" component={Link} to={`/masters/ngos/?alertStatus=${status}`}>
              Review
            </Button>
          )}
          {type === 'campaign' && (
            <Button variant="contained" size="small" component={Link} to={`/masters/campaigns/?alertStatus=${status}`}>
              Review
            </Button>
          )}
          {type === 'portaluser' && (
            <Button variant="contained" size="small" component={Link} to={`/masters/staff-members?memberStatus=${status}`}>
              Review
            </Button>
          )}
          {type === 'profile-query' && (
            <Button variant="contained" size="small" component={Link} to={`/masters/profile-queries`}>
              Review
            </Button>
          )}
        </Fragment>
      }
    >
      {message}
    </Alert>
  );
};

export default AlertMessage;
