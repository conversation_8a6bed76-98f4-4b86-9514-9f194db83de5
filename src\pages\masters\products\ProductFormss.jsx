import React, { useState, useEffect } from 'react';
import axios from 'axios';
import { useParams, useNavigate } from 'react-router-dom';
import { Button, TextField, Select, MenuItem, FormControl, InputLabel, Grid, Typography, Stack } from '@mui/material';
import { toast } from 'react-toastify';
import { API_BASE_URL } from 'api/categories.service';
import MainCard from 'components/MainCard';
import UploadSingleFile from 'components/third-party/dropzone/SingleFile';
import { getConvertedFileName } from 'utils/permissionUtils';
import useAuth from 'hooks/useAuth';
import { Formik, Field } from 'formik';
import * as yup from 'yup';
import { addProducts, editProducts, getSingleProduct } from './product.service';

const ProductForm = () => {
  const { user } = useAuth();
  const { productId } = useParams();
  const navigate = useNavigate();
  const [imagePreview, setImagePreview] = useState(null);
  const [product, setProduct] = useState({
    name: '',
    rating: '',
    isStock: 'yes',
    about: '',
    sizes: '',
    colors: '',
    salePrice: '',
    offerPrice: '',
    discount: '',
    imageName: ''
  });

  useEffect(() => {
    if (productId) {
      fetchProduct();
    }
  }, [productId]);

  const fetchProduct = async () => {
    try {
      const response = await getSingleProduct(productId);
      setProduct(response);
    } catch (error) {
      console.error('Failed to fetch product:', error);
      toast.error('Failed to fetch product');
    }
  };

  const validationSchema = yup.object().shape({
    name: yup.string().required('Name is required'),
    rating: yup.number().min(0).max(5).required('Rating is required'),
    isStock: yup.string().required('Stock status is required'),
    about: yup.string().required('About is required'),
    sizes: yup.string().required('Sizes are required'),
    colors: yup.string().required('Colors are required'),
    salePrice: yup.number().min(0).required('Sale price is required'),
    offerPrice: yup.number().min(0).required('Offer price is required'),
    discount: yup.number().min(0).max(100).required('Discount is required'),
    imageName: yup.string().required('Image name is required')
  });

  const handleSubmit = async (values, { setSubmitting }) => {
    try {
      const convertedFileName = getConvertedFileName(values?.files[0]?.name);
      const payload = {
        ...values,
        rating: parseFloat(values.rating),
        salePrice: parseFloat(values.salePrice),
        offerPrice: parseFloat(values.offerPrice),
        discount: parseFloat(values.discount),
        ngo_id: user.ngo_id ?? 123,
        imageName: values.files ? convertedFileName : 'filename1.jpg'
      };

      if (productId) {
        await editProducts(productId, payload);
        toast.success('Product updated successfully!');
      } else {
        await addProducts(payload);
        toast.success('Product added successfully!');
      }

      navigate('/masters/products');
    } catch (error) {
      console.error('Failed to save product:', error);
      toast.error('Failed to save product');
    }
  };
  const handleImageChange = (e) => {
    const file = e.target.files[0];
    if (file) {
      setImagePreview(URL.createObjectURL(file));
      setFieldValue('files', e.target.files);
    }
  };

  return (
    <Formik initialValues={product} validationSchema={validationSchema} onSubmit={handleSubmit} enableReinitialize>
      {({ values, errors, touched, handleChange, handleBlur, handleSubmit, isSubmitting, setFieldValue }) => (
        <form onSubmit={handleSubmit}>
          <MainCard>
            <Typography variant="h3" component="h1" gutterBottom>
              {productId ? 'Edit Product' : 'Add New Product'}
            </Typography>
            <Grid container spacing={2}>
              <Grid item xs={12} sm={6}>
                <TextField
                  name="name"
                  label="Name"
                  value={values.name}
                  onChange={handleChange}
                  onBlur={handleBlur}
                  error={touched.name && Boolean(errors.name)}
                  helperText={touched.name && errors.name}
                  fullWidth
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  name="rating"
                  label="Rating"
                  type="number"
                  value={values.rating}
                  onChange={handleChange}
                  onBlur={handleBlur}
                  error={touched.rating && Boolean(errors.rating)}
                  helperText={touched.rating && errors.rating}
                  fullWidth
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <FormControl fullWidth error={touched.isStock && Boolean(errors.isStock)}>
                  <InputLabel>In Stock</InputLabel>
                  <Select name="isStock" value={values.isStock} onChange={handleChange} onBlur={handleBlur} label="In Stock">
                    <MenuItem value="yes">Yes</MenuItem>
                    <MenuItem value="no">No</MenuItem>
                  </Select>
                  {touched.isStock && errors.isStock && <div>{errors.isStock}</div>}
                </FormControl>
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  name="sizes"
                  label="Sizes"
                  value={values.sizes}
                  onChange={handleChange}
                  onBlur={handleBlur}
                  error={touched.sizes && Boolean(errors.sizes)}
                  helperText={touched.sizes && errors.sizes}
                  fullWidth
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  name="colors"
                  label="Colors"
                  value={values.colors}
                  onChange={handleChange}
                  onBlur={handleBlur}
                  error={touched.colors && Boolean(errors.colors)}
                  helperText={touched.colors && errors.colors}
                  fullWidth
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  name="salePrice"
                  label="Sale Price"
                  type="number"
                  value={values.salePrice}
                  onChange={handleChange}
                  onBlur={handleBlur}
                  error={touched.salePrice && Boolean(errors.salePrice)}
                  helperText={touched.salePrice && errors.salePrice}
                  fullWidth
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  name="offerPrice"
                  label="Offer Price"
                  type="number"
                  value={values.offerPrice}
                  onChange={handleChange}
                  onBlur={handleBlur}
                  error={touched.offerPrice && Boolean(errors.offerPrice)}
                  helperText={touched.offerPrice && errors.offerPrice}
                  fullWidth
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  name="discount"
                  label="Discount"
                  type="number"
                  value={values.discount}
                  onChange={handleChange}
                  onBlur={handleBlur}
                  error={touched.discount && Boolean(errors.discount)}
                  helperText={touched.discount && errors.discount}
                  fullWidth
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  name="imageName"
                  label="Image Name"
                  value={values.imageName}
                  onChange={handleChange}
                  onBlur={handleBlur}
                  error={touched.imageName && Boolean(errors.imageName)}
                  helperText={touched.imageName && errors.imageName}
                  fullWidth
                />
              </Grid>
              <Grid item xs={12}>
                <TextField
                  name="about"
                  label="About"
                  value={values.about}
                  onChange={handleChange}
                  onBlur={handleBlur}
                  error={touched.about && Boolean(errors.about)}
                  helperText={touched.about && errors.about}
                  fullWidth
                  multiline
                  rows={3}
                />
              </Grid>
              {/* Image Upload */}
              <Grid item xs={12} sm={6}>
                <Stack spacing={1.5} alignItems="center">
                  <UploadSingleFile
                    setFieldValue={setFieldValue}
                    file={values.imageFile}
                    error={touched.imageFile && Boolean(errors.imageFile)}
                  />
                  {touched.imageFile && errors.imageFile && <FormHelperText error>{errors.imageFile}</FormHelperText>}
                </Stack>
              </Grid>
              <Grid item xs={12}>
                <Button type="submit" variant="contained" color="primary" disabled={isSubmitting}>
                  {productId ? 'Update Product' : 'Add Product'}
                </Button>
              </Grid>
            </Grid>
          </MainCard>
        </form>
      )}
    </Formik>
  );
};

export default ProductForm;
