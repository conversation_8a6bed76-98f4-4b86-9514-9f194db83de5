import axiosServices from 'utils/axios_node';

export const API_BASE_URL = `${import.meta.env.VITE_APP_APPLICATION_API_URL}/api`;

export const getProducts = async (ngoId, page = 1, limit = 10, search) => {
  const queryParams = new URLSearchParams();

  if (ngoId) queryParams.append('ngo_id', ngoId);
  if (page) queryParams.append('page', page);
  if (limit) queryParams.append('limit', limit);
  if (search) queryParams.append('search', search);
  const url = `${API_BASE_URL}/products/${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;

  const response = await axiosServices.get(url);
  return response.data;
};
export const getSingleProduct = async (id) => {
  const response = await axiosServices.get(`${API_BASE_URL}/products/${id}`);
  return response.data;
};
export const addProducts = async (payload) => {
  const response = await axiosServices.post(`${API_BASE_URL}/products`, payload);
  return response.data;
};
export const editProducts = async (id, payload) => {
  const response = await axiosServices.put(`${API_BASE_URL}/products/${id}`, payload);
  return response.data;
};

export const patchProduct = async (productId, productData) => {
  const response = await axiosServices.patch(`${API_BASE_URL}/products/${productId}`, productData);
  return response?.data;
};

export const deleteProduct = async (id) => {
  const response = await axiosServices.delete(`${API_BASE_URL}/products/${id}`);
  return response.data;
};

export const getCollections = async () => {
  const response = await axiosServices.get(`${API_BASE_URL}/collections`);
  return response.data;
};
