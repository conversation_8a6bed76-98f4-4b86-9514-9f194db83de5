import React, { useEffect, useState } from 'react';
import axios from 'axios';

// material-ui
import CardContent from '@mui/material/CardContent';
import CardMedia from '@mui/material/CardMedia';
import Grid from '@mui/material/Grid';
import Typography from '@mui/material/Typography';
import CircularProgress from '@mui/material/CircularProgress';
import Alert from '@mui/material/Alert';
import { toast, ToastContainer } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import UploadSingleFile from 'components/third-party/dropzone/SingleFile';

// project imports
import MainCard from 'components/MainCard';
import { getCoverImages, updateCoverImageService, addCoverImageService ,} from './channel.service';
import * as yup from 'yup';
import {
  Button,
  DialogActions,
  Dialog,
  DialogTitle,
  DialogContent,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Stack,
  FormHelperText
} from '@mui/material';
import { EditOutlined } from '@ant-design/icons';
import useAuth from 'hooks/useAuth';
import { Formik } from 'formik';
import { getConvertedFileName } from 'utils/permissionUtils';
import { BASE_URL } from 'sections/apps/profiles/profile.service';

// material-ui styles
const mediaSX = {
  width: 120,
  height: 100,
  borderRadius: 1
};

// ===========================|| DATA WIDGET - Cover IMAGES ||=========================== //

export default function CoverImages() {
  const { user } = useAuth();
  const [images, setImages] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [openDialog, setOpenDialog] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [currentImage, setCurrentImage] = useState(null);

  // Fetch Cover images data
  useEffect(() => {
    fetchImages();
  }, []);

  const fetchImages = async () => {
    try {
      const response = await getCoverImages();
      setImages(response);
      setLoading(false);
    } catch (err) {
      setError(err.message || 'Something went wrong while fetching data.');
      setLoading(false);
    }
  };

  const handleAddOrEdit = async (values) => {
    try {
      const formData = new FormData();

      if (!isEditing && values.files) {
        const convertedFileName = getConvertedFileName(values.files[0].name);
        formData.append('image_url', `${convertedFileName}`);
        formData.append('file', values.files[0], `${convertedFileName}`);
      }

      Object.keys(values).forEach((key) => {
        if (key !== 'files') {
          formData.append(key, values[key]);
        }
      });

      if (isEditing) {
        const response = await updateCoverImageService(currentImage.id, formData);
        if (response.status === 200) {
          toast.success('Cover image updated successfully!');
        }
      } else {
        const response = await addCoverImageService(formData);
        if (response.status) {
          toast.success('Cover image added successfully!');
        }
      }

      setOpenDialog(false);
      fetchImages();
    } catch (error) {
      console.error('Failed to save Cover image:', error);
      toast.error('Failed to save Cover image');
    }
  };

  const openEditDialog = (image) => {
    setCurrentImage(image);
    setIsEditing(true);
    setOpenDialog(true);
  };

  const openAddDialog = () => {
    setCurrentImage(null);
    setIsEditing(false);
    setOpenDialog(true);
  };

  // Render loading and error states
  if (loading) {
    return (
      <MainCard title="Cover Images">
        <CardContent>
          <Grid container justifyContent="center">
            <CircularProgress />
          </Grid>
        </CardContent>
      </MainCard>
    );
  }

  if (error) {
    return (
      <MainCard title="Cover Images">
        <CardContent>
          <Alert severity="error">{error}</Alert>
        </CardContent>
      </MainCard>
    );
  }


  // Render the Cover images
  return (
    <>
      <Grid container justifyContent="flex-end" sx={{ marginBottom: 2 }}>
        <Button variant="contained" color="primary" onClick={openAddDialog}>
          Add Cover Image
        </Button>
      </Grid>
      <MainCard content={false}>
        <CardContent>
          <Grid container spacing={3}>
            {images.map((image, index) => (
              <Grid item xs={12} key={index}>
                <Grid container alignItems="center" spacing={2}>
                  <Grid item>
                    <CardMedia
                      component="img"
                      image={`${BASE_URL}/fetchBannerImage/${image?.image_url}`}
                      alt={image.alt_text}
                      title={image.alt_text}
                      sx={mediaSX}
                    />
                  </Grid>
                  <Grid item xs zeroMinWidth>
                    <Grid container spacing={1}>
                      <Grid item xs={12}>
                        <Typography variant="subtitle1">{image.alt_text}</Typography>
                        <Typography variant="caption" color="secondary">
                          Position: {image.position} | Created At: {new Date(image.createdAt).toLocaleString()} | Last Modified:{' '}
                          {new Date(image.updatedAt).toLocaleString()}
                        </Typography>
                      </Grid>
                      <Grid item xs={12} container justifyContent="flex-end">
                        <Button onClick={() => openEditDialog(image)}>
                          <EditOutlined />
                        </Button>
                      </Grid>
                    </Grid>
                  </Grid>
                </Grid>
              </Grid>
            ))}
          </Grid>
        </CardContent>
      </MainCard>

      {/* Dialog for Add/Edit Cover Image */}
      <Dialog
        open={openDialog}
        onClose={() => setOpenDialog(false)}
        PaperProps={{
          sx: {
            borderRadius: '16px'
          }
        }}
      >
        <Formik
          initialValues={{
            alt_text: currentImage?.alt_text || '',
            position: currentImage?.position || '',
            status: currentImage?.status || 'active',
            files: null,
            updatedBy: user?.id || '',
            createdBy: user?.id || '',
            ngo_id: user?.ngo_id || 1
          }}
          validationSchema={yup.object().shape({
            alt_text: yup.string().required('Alt Text is required'),
            position: yup.number().required('Position is required').typeError('Position must be a number'),
            status: yup.string().required('Status is required')
          })}
          onSubmit={async (values) => {
            await handleAddOrEdit(values);
          }}
        >
          {({ values, handleSubmit, handleChange, setFieldValue, touched, errors }) => (
            <form onSubmit={handleSubmit}>
              <DialogTitle>{isEditing ? 'Edit Cover Image' : 'Add Cover Image'}</DialogTitle>
              <DialogContent>
                <Grid container spacing={2}>
                  <Grid item xs={12}>
                    <TextField
                      label="Alt Text"
                      fullWidth
                      variant="outlined"
                      name="alt_text"
                      value={values?.alt_text || ''}
                      onChange={handleChange}
                      required
                    />
                  </Grid>
                  <Grid item xs={6}>
                    <TextField
                      label="Position"
                      name="position"
                      fullWidth
                      variant="outlined"
                      value={values?.position || ''}
                      onChange={handleChange}
                      required
                    />
                  </Grid>
                  <Grid item xs={6}>
                    <FormControl fullWidth variant="outlined">
                      <InputLabel>Status</InputLabel>
                      <Select value={values?.status || ''} name="status" onChange={handleChange} required>
                        <MenuItem value="active">Active</MenuItem>
                        <MenuItem value="inactive">Inactive</MenuItem>
                      </Select>
                    </FormControl>
                  </Grid>
                  <Grid item xs={12}>
                    <Stack spacing={1.5} alignItems="center">
                      <UploadSingleFile setFieldValue={setFieldValue} name="files" file={values.files} />
                      <Typography align="center" variant="caption" color="secondary">
                        *.png, *.jpeg, *.jpg, *.gif
                      </Typography>
                      {touched.files && errors.files && (
                        <FormHelperText error id="file-error">
                          {errors.files}
                        </FormHelperText>
                      )}
                    </Stack>
                  </Grid>
                </Grid>
              </DialogContent>
              <DialogActions>
                <Button onClick={() => setOpenDialog(false)} color="primary">
                  Cancel
                </Button>
                <Button type="submit" color="primary">
                  {isEditing ? 'Update' : 'Add'}
                </Button>
              </DialogActions>
            </form>
          )}
        </Formik>
      </Dialog>

      <ToastContainer />
    </>
  );
}
