// axios_services.js
import axiosServices from 'utils/axios_node';

const API_BASE_URL = `${import.meta.env.VITE_APP_APPLICATION_API_URL}/api`;

export const fetchSubcategoriesSerice = async () => {
  try {
    const response = await axiosServices.get(`${API_BASE_URL}/sub-categories`);
    return response.data;
  } catch (error) {
    throw new Error('Failed to fetch subcategories');
  }
};

export const addSubcategory = async (subcategory) => {
  try {
    const response = await axiosServices.post(`${API_BASE_URL}/sub-categories`, subcategory);
    return response.data;
  } catch (error) {
    throw new Error('Failed to add subcategory');
  }
};

export const editSubcategory = async (id, subcategory) => {
  try {
    const response = await axiosServices.put(`${API_BASE_URL}/sub-categories/${id}`, subcategory);
    return response.data;
  } catch (error) {
    throw new Error('Failed to update subcategory');
  }
};

export const deleteSubcategory = async (id) => {
  try {
    const response = await axiosServices.delete(`${API_BASE_URL}/sub-categories/${id}`);
    return response.data;
  } catch (error) {
    throw new Error('Failed to delete subcategory');
  }
};
