import React, { useEffect, useState } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import {
  Button,
  Box,
  Grid,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Tooltip,
  CircularProgress,
  Chip,
  Typography
} from '@mui/material';
import { Fragment } from 'react';
import { toast, ToastContainer } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import {
  getAllCampaigns,
  deleteCampaign,
  assignfeaturedToCampaigns,
  addMileStone,
  updateMileStone,
  patchCampaign,
  addCampaign,
  getKindDonationByCampaignId,
  getCampaignById
} from 'api/campaigns.service';
import CustomCampaignsTable from 'sections/apps/customer/CustomCampaignTable';
import { getUserPermissions } from 'utils/permissionUtils';
import { CAMPAIGN_STATUS_LIST } from 'utils/statusconstans';
import TableActions from 'components/TableActions';
import useAuth from 'hooks/useAuth';
import CampaignCard from '../CustomCampaignsCard';
import { getNgoById } from 'api/ngos.service';
import { calculateProfileCompletion } from 'utils/permissionUtils';
import moment from 'moment';
import { Link } from 'react-router-dom';
import { CheckCircleOutlined, FileImageOutlined, FileSyncOutlined, StarFilled, StarOutlined, StopOutlined } from '@ant-design/icons';
import CampaignMilestones from './CampaignMilestones/CampaignMilestones';
import { updateUsingPatchNGO } from 'sections/apps/profiles/profile.service';
import dayjs from 'dayjs';
import CampaignFormDialog from './CampaignFormDialog';
import { getAllCategories } from 'api/categories.service';
import EventFormDialog from './EventFormDialog';

export default function CampaignsTable({ campaignType }) {
  const [loading, setLoading] = useState(false);
  const statusList = CAMPAIGN_STATUS_LIST;

  const [searchParams] = useSearchParams();

  const alertStatus = searchParams.get('alertStatus') ? searchParams.get('alertStatus') : 'All';

  const [campaigns, setCampaigns] = useState([]);
  const [categories, setCategories] = useState([]);
  const [duplicateCampaign, setDuplicateCampaign] = useState(null);

  // const [tabValue, setTabValue] = useState(0);

  const [viewMode, setViewMode] = useState('table');
  const [featuredDialogOpen, setFeaturedDialogOpen] = useState(false);
  const [selectedCampaignId, setSelectedCampaignId] = useState(null);
  const [featureMode, setFeatureMode] = useState('add');

  //pagination
  const [totalCount, setTotalCount] = useState(0);

  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);

  const { user } = useAuth();
  const navigate = useNavigate();

  const permissions = getUserPermissions(user);
  const canAdd = permissions?.Add?.Campaigns || false;
  const canEdit = permissions?.Edit?.Campaigns || false;
  const canDelete = permissions?.Delete?.Campaigns || false;
  const [profileCompletePercentage, setProfileCompletePercentage] = useState(0);
  const [showCampaignMileStonePopup, setShowCampaingMileStonePopup] = useState(false);
  const [showCampaignDuplicatePopup, setShowCampaingDuplicatePopup] = useState(false);
  const [showEventDuplicatePopup, setShowEventDuplicatePopup] = useState(false);
  const [currentCampaignId, setCurrentCampaignId] = useState(null);

  //status for completed and inactive
  const [statusDialogOpen, setStatusDialogOpen] = useState(false);
  const [statusMode, setStatusMode] = useState(null); // 'completed' or 'inactive'
  const [selectedItemType, setSelectedItemType] = useState(null); // 'campaign' or 'event'
  const [selectedItemId, setSelectedItemId] = useState(null);

  //filter states
  const [globalFilter, setGlobalFilter] = useState('');

  useEffect(() => {
    if (campaignType) {
      fetchCampaigns();
      fetchCategories();
    }
  }, [globalFilter, currentPage, pageSize, campaignType]);

  useEffect(() => {
    const fetchNgoDataAndCalculate = async () => {
      if (user?.ngo_id) {
        try {
          const ngodata = await getNgoById(user.ngo_id, 'CampaignsTable');
          const profilepercentage = await calculateProfileCompletion(ngodata);
          setProfileCompletePercentage(profilepercentage);
        } catch (error) {
          console.error('Error fetching NGO data:', error);
        }
      } else {
        setProfileCompletePercentage(0);
      }
    };

    fetchNgoDataAndCalculate();
  }, [user?.ngo_id]);

  const fetchCampaigns = async () => {
    setLoading(true);
    try {
      if (user?.ngo_id) {
        const response = await getAllCampaigns(campaignType, user?.ngo_id, 'CampaignsTable', currentPage, pageSize, null, globalFilter);

        setCampaigns(response?.result);

        setTotalCount(response?.totalCount);

        return;
      } else {
        const response = await getAllCampaigns(
          campaignType,
          null,
          'CampaignsTable',
          currentPage,
          pageSize,
          user.roleInfo.name === 'DR_Staff' ? user?.id : null,
          globalFilter
        );

        setCampaigns(response?.result);

        setTotalCount(response?.totalCount);
      }
    } catch (error) {
      console.error('Failed to fetch campaigns:', error);
      toast.error('Failed to fetch campaigns');
    } finally {
      setLoading(false);
    }
  };

  const fetchCategories = async () => {
    setLoading(true);
    try {
      const response = await getAllCategories();

      setCategories(response);

      return;
    } catch (error) {
      console.error('Failed to fetch categories:', error);
      toast.error('Failed to fetch categories');
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async (id) => {
    if (!canDelete) {
      toast.error('You do not have permission to delete Campaigns.');
      return;
    }
    const confirmDelete = window.confirm(`Are you sure you want to delete this ${campaignType === 'campaigns' ? 'campaign' : 'event'} ?`);
    if (confirmDelete) {
      try {
        await deleteCampaign(id, 'CampaignsTable');
        toast.success('Campaign deleted successfully!');
        fetchCampaigns();
      } catch (error) {
        console.error('Failed to delete campaign:', error);
        toast.error('Failed to delete campaign');
      }
    }
  };

  const handleCloneSubmit = async (campaign) => {
    const confirmApprove = window.confirm('Are you sure you want to clone this Event?');
    if (confirmApprove) {
      try {
        const { id, ...campaignWithoutId } = campaign;

        const campaignData = await addCampaign(campaignWithoutId, 'CampaignsTable');
        toast.success('Campaign is has been Cloned!');
        setTimeout(() => {
          navigate(
            campaignType == 'campaigns'
              ? `/masters/campaigns/edit/campaign/${campaignData?.data.id}`
              : `/masters/events/edit/event/${campaignData?.data.id}`
          );
        }, 2500);
      } catch (error) {
        console.error('Failed to Approve:', error);
        toast.error('Failed to set the campaign as approves');
      }
    }
  };

  const openAddDialog = () => {
    if (!canAdd) {
      toast.error('You do not have permission to Add Campaigns.');
      return;
    }
    navigate(`add/${campaignType == 'campaigns' ? 'campaign' : 'event'}`);
  };

  const handleCampaignDashboardClick = (campaignId) => {
    navigate(`/campaigns/dashboard/${campaignId}`);
  };

  const openEditDialog = (campaign) => {
    if (!canEdit) {
      toast.error('You do not have permission to edit Campaigns.');
      return;
    }
    if (campaignType && campaignType == 'campaigns') {
      navigate(`edit/campaign/${campaign.id}`);
    } else {
      navigate(`edit/event/${campaign.id}`);
    }
  };

  const handleViewImages = (campaignId) => {
    navigate(`/masters/campaigns/campaign-images/${campaignId}`);
  };

  const handleFeaturedSubmit = async () => {
    if (!selectedCampaignId) {
      toast.error('No campaign selected!');
      return;
    }
    try {
      const response = await assignfeaturedToCampaigns(selectedCampaignId, featureMode);
      if (response.statusText === 'OK') {
        featureMode === 'add' ? toast.success('Campaign marked as featured') : toast.success('Campaign removed as featured');
        setFeaturedDialogOpen(false);
        fetchCampaigns();
      }
    } catch (error) {
      console.error('Failed to mark campaign as featured :', error);
      toast.error('Failed to mark campaign as featured');
    }
  };

  const openMileStonesDialog = (campaign) => {
    if (!showCampaignMileStonePopup) {
      setCurrentCampaignId(campaign);
      setShowCampaingMileStonePopup(!showCampaignMileStonePopup);
    } else {
      setCurrentCampaignId(null);
      setShowCampaingMileStonePopup(false);
    }
  };

  const openDuplicateDialog = async (campaign) => {
    let campaignData = { ...campaign };

    if (!showCampaignDuplicatePopup) {
      if (campaign?.eventId) {
        const eventInfo = await getCampaignById(campaign?.eventId);
        campaignData.fullday_event = eventInfo?.fullday_event;
        campaignData.event_type = eventInfo?.event_type;
        campaignData.volunteer_type = eventInfo?.volunteer_type;
        campaignData.no_of_volunteers = eventInfo?.no_of_volunteers;
        campaignData.event_end_time = eventInfo?.event_end_time ? eventInfo?.event_end_time : null;
        campaignData.event_start_time = eventInfo?.event_start_time ? eventInfo?.event_start_time : null;
        campaignData.ngo_id = user?.ngo_id ? user?.ngo_id : eventInfo?.ngo_id;
        campaignData.ratio = eventInfo?.ratio;
        campaignData.pincode = eventInfo?.pincode;
        campaignData.current_address = eventInfo?.current_address;
        campaignData.state = eventInfo?.state;
        campaignData.country = eventInfo?.country;
        campaignData.place_name = eventInfo?.place_name;
        campaignData.meeting_link = eventInfo?.meeting_link;
        campaignData.longitude = eventInfo?.longitude;
        campaignData.latitude = eventInfo?.latitude;
        campaignData.amount_per_person = eventInfo?.amount_per_person;

        setDuplicateCampaign(campaignData);
        setShowCampaingDuplicatePopup(!showCampaignDuplicatePopup);
      } else {
        setDuplicateCampaign(campaignData);
        setShowCampaingDuplicatePopup(!showCampaignDuplicatePopup);
      }
    } else {
      setDuplicateCampaign(null);
      setShowCampaingDuplicatePopup(false);
    }
  };
  const openEventDuplicateDialog = (event) => {
    if (!showEventDuplicatePopup) {
      setDuplicateCampaign(event);
      setShowEventDuplicatePopup(!showEventDuplicatePopup);
    } else {
      setDuplicateCampaign(null);
      setShowEventDuplicatePopup(false);
    }
  };

  const handleMileStoneSubmit = async (formObject) => {
    // if (!currentDocument?.documentId) {
    const record = await addMileStone(formObject, 'CampaignsTable');
    if (record.status === false) {
      toast.error('Mile not added');
    } else {
      toast.success('Milestone added successfully');
      // insert settings value
      openMileStonesDialog(null);
    }
  };

  const handleStatusSubmit = async () => {
    if (!selectedItemId) {
      toast.error('No campaign selected!');
      return;
    }
    const statusUpdate = statusMode === 'completed' ? { status: 'Completed' } : { status: 'Inactive' };

    try {
      await patchCampaign(selectedItemId, statusUpdate);
      toast.success(
        `${selectedItemType === 'campaign' ? 'Campaign' : 'Event'} status updated to ${statusMode === 'completed' ? 'Completed' : 'Inactive'} SuccessFully`
      );
      setTimeout(() => {
        fetchCampaigns();
      }, 2000);
    } catch (error) {
      console.error('Error updating campaign:', error);
    } finally {
      setStatusDialogOpen(false);
    }
  };

  const campaignInfoProps = {
    handleCampaignDashboardClick: handleCampaignDashboardClick,
    showCampaignDashboard: false,
    showCampaignMileStones: true,
    openMileStonesDialog: openMileStonesDialog
  };

  const campaignColumns = [
    {
      accessorKey: 'name',
      header: 'Name',
      showByDefault: true,
      cell: ({ cell }) => {
        return (
          <Link className="ngo-link" to={`/masters/campaigns/edit/campaign/${cell.row.original.id}`}>
            {cell.row.original.name}
          </Link>
        );
      }
    },

    ...(user?.ngo_id
      ? []
      : [
          {
            accessorKey: 'ngoInfo',
            header: 'By',
            showByDefault: true,
            cell: (cell) => {
              return cell.row.original.ngoInfo?.name || '-';
            }
          }
        ]),
    { accessorKey: 'description', header: 'Description', showByDefault: true },
    {
      accessorKey: 'categoryInfo.name',
      header: 'Category Name',
      showByDefault: false,
      cell: ({ cell }) => {
        return cell.row.original.categoryInfo?.name;
      }
    },
    {
      accessorKey: 'fund_raising_target',
      header: 'Goal Amount',
      showByDefault: true,
      cell: ({ cell }) => {
        const amount = cell.getValue();
        return `₹${new Intl.NumberFormat('en-IN').format(amount)}`;
      }
    },
    { accessorKey: 'status', header: 'Status', showByDefault: true },

    {
      accessorKey: 'createdAt',
      header: 'Created At',
      showByDefault: true,
      cell: (cell) => {
        const formattedDate = dayjs(cell.row.original.createdAt).format('DD-MM-YYYY hh:mm A');
        return <span>{formattedDate}</span>;
      }
    },
    {
      accessorKey: 'updatedAt',
      header: 'Updated At',
      showByDefault: true,
      cell: (cell) => {
        const formattedDate = dayjs(cell.row.original.updatedAt).format('DD-MM-YYYY hh:mm A');
        return <span>{formattedDate}</span>;
      }
    },
    {
      id: 'actions',
      header: 'Actions',
      cell: (cell) => {
        const campaign = cell.row.original;
        const isFeatured = campaign.isfeatured === 'yes';

        return (
          <Box display="flex" flexDirection="row">
            <TableActions handleEditClick={openEditDialog} cell={cell} handleDeleteClick={handleDelete} campaginInfo={campaignInfoProps} />

            {user?.roleInfo?.name.startsWith('DR') &&
              (isFeatured ? (
                <Tooltip title="Remove as Featured">
                  <IconButton
                    color="primary"
                    size="medium"
                    onClick={() => {
                      setFeaturedDialogOpen(true);
                      setSelectedCampaignId(campaign.id);
                      setFeatureMode('remove');
                    }}
                    style={{ marginLeft: '8px' }}
                  >
                    <StarFilled />
                  </IconButton>
                </Tooltip>
              ) : (
                <Tooltip title="Mark as Featured">
                  <IconButton
                    color="secondary"
                    size="medium"
                    onClick={() => {
                      setFeaturedDialogOpen(true);
                      setSelectedCampaignId(campaign.id);
                      setFeatureMode('add');
                    }}
                    style={{ marginLeft: '8px' }}
                  >
                    <StarOutlined />
                  </IconButton>
                </Tooltip>
              ))}
            {user?.roleInfo?.name.startsWith('DR') && (
              <>
                <Tooltip title="Mark as Completed">
                  <IconButton
                    color="success"
                    size="medium"
                    onClick={() => {
                      setStatusMode('completed');
                      setSelectedItemType('campaign');
                      setSelectedItemId(campaign.id);
                      setStatusDialogOpen(true);
                    }}
                    style={{ marginLeft: '8px' }}
                  >
                    <CheckCircleOutlined />
                  </IconButton>
                </Tooltip>

                <Tooltip title="Mark as Inactive">
                  <IconButton
                    color="error"
                    size="medium"
                    onClick={() => {
                      setStatusMode('inactive');
                      setSelectedItemType('campaign');
                      setSelectedItemId(campaign.id);
                      setStatusDialogOpen(true);
                    }}
                    style={{ marginLeft: '8px' }}
                  >
                    <StopOutlined />
                  </IconButton>
                </Tooltip>
              </>
            )}

            <Tooltip title="Duplicate Campaign">
              <IconButton
                color="info"
                size="medium"
                onClick={() => {
                  // handleCloneSubmit(campaign);
                  openDuplicateDialog(campaign);
                }}
                style={{ marginLeft: '8px' }}
              >
                <FileSyncOutlined />
              </IconButton>
            </Tooltip>
          </Box>
        );
      },
      meta: {
        style: {
          position: 'sticky',
          right: 0,
          background: '#fff', // Important: so the sticky column doesn't look weird
          zIndex: 2 // or higher if needed
        }
      }
    }
  ];
  const eventColumns = [
    {
      accessorKey: 'name',
      header: 'Name',
      showByDefault: true,
      cell: ({ cell }) => {
        return (
          <Link className="ngo-link" to={`/masters/events/edit/event/${cell.row.original.id}`}>
            {cell.row.original.name}
          </Link>
        );
      }
    },

    ...(user?.ngo_id
      ? []
      : [
          {
            accessorKey: 'ngoInfo',
            header: 'Ngo Name',
            showByDefault: true,
            cell: (cell) => {
              return cell.row.original.ngoInfo?.name || '-';
            }
          }
        ]),
    { accessorKey: 'description', header: 'Description', showByDefault: true },
    {
      accessorKey: 'categoryInfo.name',
      header: 'Category Name',
      showByDefault: false,
      cell: ({ cell }) => {
        return cell.row.original.categoryInfo?.name;
      }
    },
    {
      accessorKey: 'fullday_event',
      header: 'Full Day Event',
      showByDefault: true,
      cell: ({ cell }) => {
        const value = cell.row.original.fullday_event;
        return value === 'yes' ? 'Yes' : 'No';
      }
    },
    {
      accessorKey: 'volunteer_type',
      header: 'Volunteer Type',
      showByDefault: true,
      cell: ({ cell }) => {
        const type = cell.row.original.volunteer_type;
        return type === 'self' ? 'Self' : type === 'paid' ? 'Paid' : '-';
      }
    },
    {
      accessorKey: 'event_date',
      header: 'Event Date',
      showByDefault: true,
      cell: (cell) => {
        return cell.row.original.event_date ? moment(cell.row.original.event_date).format('DD-MM-YYYY') : '-';
      }
    },
    {
      accessorKey: 'event_type',
      header: 'Event Format',
      showByDefault: true,
      cell: ({ cell }) => {
        const type = cell.row.original.event_type;
        return type === 'virtual' ? 'Virtual' : type === 'physical' ? 'Physical' : '-';
      }
    },
    {
      accessorKey: 'event_start_time',
      header: 'Start Time',
      showByDefault: true,
      cell: ({ row }) => {
        const time = row.original.event_start_time;
        return time ? moment(time, 'HH:mm:ss').format('hh:mm A') : '-';
      }
    },
    {
      accessorKey: 'event_end_time',
      header: 'End Time',
      showByDefault: true,
      cell: ({ row }) => {
        const time = row.original.event_end_time;
        return time ? moment(time, 'HH:mm:ss').format('hh:mm A') : '-';
      }
    },
    { accessorKey: 'status', header: 'Status', showByDefault: true },

    {
      accessorKey: 'createdAt',
      header: 'Created At',
      showByDefault: true,
      cell: (cell) => {
        const formattedDate = dayjs(cell.row.original.createdAt).format('DD-MM-YYYY hh:mm A');
        return <span>{formattedDate}</span>;
      }
    },
    {
      accessorKey: 'updatedAt',
      header: 'Updated At',
      showByDefault: true,
      cell: (cell) => {
        const formattedDate = dayjs(cell.row.original.updatedAt).format('DD-MM-YYYY hh:mm A');
        return <span>{formattedDate}</span>;
      }
    },
    {
      id: 'actions',
      header: 'Actions',
      cell: (cell) => {
        const campaign = cell.row.original;
        const isFeatured = campaign.isfeatured === 'yes';

        const campaignInfoProps = {
          handleCampaignDashboardClick: handleCampaignDashboardClick,
          showCampaignDashboard: false,
          showCampaignMileStones: false, // <-- now passing false
          openMileStonesDialog: openMileStonesDialog
        };

        return (
          <Box display="flex" flexDirection="row">
            <TableActions handleEditClick={openEditDialog} cell={cell} handleDeleteClick={handleDelete} campaginInfo={campaignInfoProps} />

            {user?.roleInfo?.name.startsWith('DR') &&
              (isFeatured ? (
                <Tooltip title="Remove as Featured">
                  <IconButton
                    color="primary"
                    size="medium"
                    onClick={() => {
                      setFeaturedDialogOpen(true);
                      setSelectedCampaignId(campaign.id);
                      setFeatureMode('remove');
                    }}
                    style={{ marginLeft: '8px' }}
                  >
                    <StarFilled />
                  </IconButton>
                </Tooltip>
              ) : (
                <Tooltip title="Mark as Featured">
                  <IconButton
                    color="secondary"
                    size="medium"
                    onClick={() => {
                      setFeaturedDialogOpen(true);
                      setSelectedCampaignId(campaign.id);
                      setFeatureMode('add');
                    }}
                    style={{ marginLeft: '8px' }}
                  >
                    <StarOutlined />
                  </IconButton>
                </Tooltip>
              ))}
            {user?.roleInfo?.name.startsWith('DR') && (
              <>
                <Tooltip title="Mark as Completed">
                  <IconButton
                    color="success"
                    size="medium"
                    onClick={() => {
                      setStatusMode('completed');
                      setSelectedItemType('event');
                      setSelectedItemId(campaign.id);
                      setStatusDialogOpen(true);
                    }}
                    style={{ marginLeft: '8px' }}
                  >
                    <CheckCircleOutlined />
                  </IconButton>
                </Tooltip>

                <Tooltip title="Mark as Inactive">
                  <IconButton
                    color="error"
                    size="medium"
                    onClick={() => {
                      setStatusMode('inactive');
                      setSelectedItemType('event');
                      setSelectedItemId(campaign.id);
                      setStatusDialogOpen(true);
                    }}
                    style={{ marginLeft: '8px' }}
                  >
                    <StopOutlined />
                  </IconButton>
                </Tooltip>
              </>
            )}

            <Tooltip title="Duplicate Event">
              <IconButton
                color="info"
                size="medium"
                onClick={() => {
                  openEventDuplicateDialog(campaign);
                }}
                style={{ marginLeft: '8px' }}
              >
                <FileSyncOutlined />
              </IconButton>
            </Tooltip>
          </Box>
        );
      },
      meta: {
        style: {
          position: 'sticky',
          right: 0,
          background: '#fff', // Important: so the sticky column doesn't look weird
          zIndex: 2 // or higher if needed
        }
      }
    }
  ];

  const handlePageChange = (newPage) => {
    setCurrentPage(newPage);
  };

  const handlePageSizeChange = (newPageSize) => {
    setPageSize(newPageSize);
  };

  return (
    <div>
      <Box>
        {viewMode === 'table' ? (
          loading ? (
            <Box
              sx={{
                display: 'flex',
                flexDirection: 'column',
                justifyContent: 'center',
                alignItems: 'center'
              }}
            >
              <CircularProgress />
              <Typography sx={{ mt: 2 }}>Loading {campaignType == 'campaigns' ? 'Campaigns' : 'Events'} Information....</Typography>
            </Box>
          ) : (
            <CustomCampaignsTable
              data={campaigns}
              columns={campaignType == 'campaigns' ? campaignColumns : eventColumns}
              modalToggler={openAddDialog}
              category={campaignType}
              statusList={statusList}
              totalCount={totalCount}
              currentPage={currentPage}
              pageSize={pageSize}
              onPageChange={handlePageChange}
              onPageSizeChange={handlePageSizeChange}
              globalFilter={globalFilter}
              setGlobalFilter={setGlobalFilter}
              campaignType={campaignType}
            />
          )
        ) : (
          <Grid container spacing={3}>
            {campaigns.length > 0 ? (
              campaigns.map((campaign) => (
                <Grid item xs={12} sm={6} md={4} key={campaign.id}>
                  <CampaignCard campaign={campaign} onEdit={() => openEditDialog(campaign)} onDelete={() => handleDelete(campaign.id)} />
                </Grid>
              ))
            ) : (
              <Grid item xs={12}>
                <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '200px' }}>
                  <Typography variant="h6">No data available</Typography>
                </Box>
              </Grid>
            )}
          </Grid>
        )}
      </Box>
      <Dialog
        open={featuredDialogOpen}
        onClose={() => setFeaturedDialogOpen(false)}
        PaperProps={{
          sx: {
            borderRadius: '16px'
          }
        }}
      >
        <DialogTitle>Mark Featured</DialogTitle>
        <DialogContent>Do you want {featureMode === 'add' ? 'mark' : 'remove'} selected Campaign as featured?</DialogContent>
        <DialogActions>
          <Button onClick={() => setFeaturedDialogOpen(false)}>Cancel</Button>
          <Button variant="contained" color="primary" onClick={handleFeaturedSubmit}>
            Okay
          </Button>
        </DialogActions>
      </Dialog>

      <Dialog open={statusDialogOpen} onClose={() => setStatusDialogOpen(false)} PaperProps={{ sx: { borderRadius: '16px' } }}>
        <DialogTitle>{statusMode === 'completed' ? 'Mark as Completed' : 'Mark as Inactive'}</DialogTitle>
        <DialogContent>
          Do you want to mark the selected campaign as <strong>{statusMode === 'completed' ? 'Completed' : 'Inactive'}</strong>?
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setStatusDialogOpen(false)}>Cancel</Button>
          <Button variant="contained" color={statusMode === 'completed' ? 'primary' : 'error'} onClick={handleStatusSubmit}>
            Confirm
          </Button>
        </DialogActions>
      </Dialog>

      {showCampaignMileStonePopup && (
        <CampaignMilestones
          isEditing={false}
          openDialog={showCampaignMileStonePopup}
          setOpenDialog={setShowCampaingMileStonePopup}
          handleMileStoneSubmit={handleMileStoneSubmit}
          campaign={currentCampaignId}
        />
      )}

      {showCampaignDuplicatePopup && (
        <CampaignFormDialog
          open={showCampaignDuplicatePopup}
          onClose={() => setShowCampaingDuplicatePopup(false)}
          onSubmit={handleCloneSubmit}
          campaign={duplicateCampaign}
          categories={categories}
          fetchCampaigns={fetchCampaigns}
        />
      )}

      {showEventDuplicatePopup && (
        <EventFormDialog
          open={showEventDuplicatePopup}
          onClose={() => setShowEventDuplicatePopup(false)}
          event={duplicateCampaign}
          categories={categories}
          fetchCampaigns={fetchCampaigns}
        />
      )}

      <ToastContainer />
    </div>
  );
}
