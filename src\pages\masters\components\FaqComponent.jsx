import React, { useEffect, useState } from 'react';
import axios from 'axios';
import { MinusCircleOutlined, PlusCircleOutlined } from '@ant-design/icons';
import { Accordion, AccordionSummary, AccordionDetails, Typography, CircularProgress, Box } from '@mui/material';
import { fetchFaqService } from '../apis/faq.service';

const FaqComponent = () => {
  const [faqs, setFaqs] = useState([]);
  const [loading, setLoading] = useState(true);
  const [expanded, setExpanded] = useState(null);
  useEffect(() => {
    fetchFaqs();
  }, []);

  const fetchFaqs = async () => {
    try {
      const response = await fetchFaqService();
      setFaqs(response);
      setLoading(false);
    } catch (error) {
      console.error('Failed to fetch FAQs:', error);
      setLoading(false);
    }
  };

  const handleAccordionToggle = (index) => {
    setExpanded(expanded === index ? null : index);
  };

  return (
    <Box >


      {loading ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '50vh' }}>
          <CircularProgress />
        </Box>
      ) : (
        faqs.map((faq, index) => (
          <Accordion key={index} expanded={expanded === index} onChange={() => handleAccordionToggle(index)} sx={{ marginBottom: '10px' }}>
            <AccordionSummary
              expandIcon={
                expanded === index ? (
                  <MinusCircleOutlined style={{ fontSize: '20px', color: '#1976d2' }} />
                ) : (
                  <PlusCircleOutlined style={{ fontSize: '20px', color: '#1976d2' }} />
                )
              }
            >
              <Typography variant="h6">{faq.question}</Typography>
            </AccordionSummary>
            <AccordionDetails>
              <Typography>{faq.answer}</Typography>
            </AccordionDetails>
          </Accordion>
        ))
      )}
    </Box>
  );
};

export default FaqComponent;
