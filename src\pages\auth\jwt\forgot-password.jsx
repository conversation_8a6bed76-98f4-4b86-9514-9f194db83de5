import { Link, useSearchParams } from 'react-router-dom';

// material-ui
import Grid from '@mui/material/Grid';
import Stack from '@mui/material/Stack';
import Typography from '@mui/material/Typography';

// project import
import useAuth from 'hooks/useAuth';
import AuthWrapper from 'sections/auth/AuthWrapper';
import AuthForgotPassword from 'sections/auth/jwt/AuthForgotPassword';
import { Box } from '@mui/material';

import { useTheme } from '@mui/system';
import ImageSlider from 'components/ImageSlider';

// ================================|| JWT - FORGOT PASSWORD ||================================ //

export default function ForgotPassword() {
  const { isLoggedIn } = useAuth();

  const theme = useTheme();

  const [searchParams] = useSearchParams();
  const auth = searchParams.get('auth'); // get auth and set route based on that

  return (
    <Box sx={{ display: 'flex', minHeight: '100vh' }}>
      {/* Left side - Image Slider */}
      <ImageSlider />

      {/* Right side - Registration Form */}
      <Box sx={{ flex: 1, display: 'flex', flexDirection: 'column', bgcolor: theme.palette.primary.main }}>
        <AuthWrapper>
          <Grid container spacing={3}>
            <Grid item xs={12}>
              <Stack direction="row" justifyContent="space-between" alignItems="baseline" sx={{ mb: { xs: -0.5, sm: 0.5 } }}>
                <Typography variant="h3" style={{color:"#fff"}}>Forgot Password</Typography>
                <Typography
                  component={Link}
                  to={isLoggedIn ? '/auth/login' : auth ? `/${auth}/login?auth=jwt` : '/login'}
                  variant="body1"
                  sx={{ textDecoration: 'none' }}
                  color="primary"
                >
                  Back to Sign in
                </Typography>
              </Stack>
            </Grid>
            <Grid item xs={12}>
              <AuthForgotPassword />
            </Grid>
          </Grid>
        </AuthWrapper>
      </Box>
    </Box>
  );
}
