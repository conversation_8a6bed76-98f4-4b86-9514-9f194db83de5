import axiosServices from 'utils/axios_node';

export const API_BASE_URL = `${import.meta.env.VITE_APP_APPLICATION_API_URL}/api`;

export const fetchNotificationsService = async () => {
  const response = await axiosServices.get(`${API_BASE_URL}/user-notifications`);
  return response?.data;
};

export const sendNotificationService = async (payload) => {
  const response = await axiosServices.post(`${API_BASE_URL}/user-notifications`, payload);
  return response?.data;
};
