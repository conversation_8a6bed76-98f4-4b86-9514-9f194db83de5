import PropTypes from 'prop-types';
import { Fragment, useEffect, useMemo, useState } from 'react';

// material-ui
import { alpha, useTheme } from '@mui/material/styles';
import useMediaQuery from '@mui/material/useMediaQuery';
import Button from '@mui/material/Button';
import Divider from '@mui/material/Divider';
import MenuItem from '@mui/material/MenuItem';
import Select from '@mui/material/Select';
import Stack from '@mui/material/Stack';
import Table from '@mui/material/Table';
import TableBody from '@mui/material/TableBody';
import TableCell from '@mui/material/TableCell';
import TableContainer from '@mui/material/TableContainer';
import TableHead from '@mui/material/TableHead';
import TableRow from '@mui/material/TableRow';
import Box from '@mui/material/Box';

// third-party
import {
  flexRender,
  getCoreRowModel,
  getSortedRowModel,
  getPaginationRowModel,
  getFilteredRowModel,
  getExpandedRowModel,
  useReactTable
} from '@tanstack/react-table';
import { rankItem } from '@tanstack/match-sorter-utils';

// project-import
import ScrollX from 'components/ScrollX';
import MainCard from 'components/MainCard';

import {
  CSVExport,
  DebouncedInput,
  HeaderSort,
  RowSelection,
  SelectColumnSorting,
  SelectColumnVisibility,
  TablePagination
} from 'components/third-party/react-table';

import ExpandingUserDetail from 'sections/apps/customer/ExpandingUserDetail';

// assets
import PlusOutlined from '@ant-design/icons/PlusOutlined';
import { COMMON_STATUS_LIST, NGO_STATUS_LIST, SOURCE_TYPES } from 'utils/statusconstans';
import { MenuUnfoldOutlined, SyncOutlined } from '@ant-design/icons';
import { getAdmins, getNgoBySearchTerm } from 'api/ngos.service';
import { Chip, CircularProgress, IconButton, Paper, Tooltip } from '@mui/material';
import { fetchAdminsService, fetchStates } from 'pages/masters/apis/portal-users.service';
import { getFilteredNGO } from 'pages/masters/ngos/ngo.service';
import { CSVLink } from 'react-csv';
import useAuth from 'hooks/useAuth';
import SearchWithButton from 'components/third-party/react-table/SearchWithButton';
import CustomCircularLoaded from 'components/CustomCircularLoaded';

export const fuzzyFilter = (row, columnId, value, addMeta) => {
  const itemRank = rankItem(row.getValue(columnId), value);
  addMeta(itemRank);
  return itemRank.passed;
};

export default function CustomTableCheckBox({
  data,
  columns,
  modalToggler,
  category,
  statusList,
  setRowSelection,
  rowSelection,
  openAssignDialog,
  openFeaturedDialog,
  tabValue,
  roleName,
  totalCount,
  currentPage,
  openAssignGradeDialog,
  pageSize,
  onPageChange,
  onPageSizeChange,
  filters,
  setFilters,
  globalFilter,
  setGlobalFilter,
  portalUsers,
  states,
  isFilterLoading,
  applyFilters,
  filteredPageSize,
  filteredCurrentPage,
  filteredTotalCount,
  searchFilteredData,
  handleRefresh,
  sorting,
  setSorting
}) {
  const { user } = useAuth();
  const theme = useTheme();
  const downSM = useMediaQuery(theme.breakpoints.down('sm'));
  const updatedStatusList = statusList?.length > 0 ? statusList : COMMON_STATUS_LIST;
  const initialColumnVisibility = useMemo(() => {
    const visibility = {};
    columns.forEach((column) => {
      visibility[column.accessorKey] = column.showByDefault !== false;
    });
    return visibility;
  }, [columns]);
  const [columnVisibility, setColumnVisibility] = useState(initialColumnVisibility);
  const [isLoading, setIsLoading] = useState(false);
  const [isExporting, setIsExporting] = useState(false);
  const [isDataReadyForDownload, setIsDataReadyForDownload] = useState(false);

  const [exportData, setExportData] = useState(null);
  // const [filters, setFilters] = useState({
  //   source: '',
  //   state: '',
  //   assignee_id: '',
  //   ngo_status: ''
  // });

  //   useEffect(() => {
  //     setFilteredCurrentPage(1);
  //   }, [filters, globalFilter]);

  //   useEffect(() => {

  //     applyFilters();
  //   }, [filters, filteredCurrentPage, filteredPageSize, data, totalCount, globalFilter]);

  const handleFilterChange = (key, value) => {
    setFilters((prevFilters) => ({
      ...prevFilters,
      [key]: value
    }));
  };

  const handleExport = async () => {
    const statusvalue = statusList[tabValue];

    setIsExporting(true);
    setIsDataReadyForDownload(false);
    let query = Object.entries(filters)
      .filter(([_, value]) => value)
      .map(([key, value]) => `${encodeURIComponent(key)}=${encodeURIComponent(value)}`)
      .join('&');

    if (globalFilter) {
      query += query ? `&searchTerm=${encodeURIComponent(globalFilter)}` : `searchTerm=${encodeURIComponent(globalFilter)}`;
    }
    if (statusvalue !== 'All') {
      query += query ? `&status=${encodeURIComponent(statusvalue)}` : `status=${encodeURIComponent(statusvalue)}`;
    }

    try {
      const response = await getFilteredNGO(query);
      setExportData(response.ngos);
    } catch (error) {
      console.error('Error fetching data for export:', error);
      return null;
    } finally {
      setIsExporting(false);
      setIsDataReadyForDownload(true);
    }
  };
  const handleDownload = () => {
    setIsDataReadyForDownload(false);
  };

  const handlePageChange = (newPage) => {
    onPageChange(newPage + 1);

    // if (globalFilter || Object.values(filters).some((value) => value)) {
    //   applyFilters(); // Call filtering function with the existing global filter
    // } else {
    onPageChange(newPage + 1);
    // }
  };

  const handlePageSizeChange = (newPageSize) => {
    // setFilteredPageSize(newPageSize);

    onPageSizeChange(newPageSize);
  };

  //   const filteredData = useMemo(() => {
  //     if (!statusFilter || statusFilter === 'All') return searchFilteredData;
  //     return searchFilteredData?.filter((customer) => customer.ngo_status === statusFilter) || [];
  //   }, [statusFilter, searchFilteredData]);

  const table = useReactTable({
    data: searchFilteredData,
    columns,
    state: {
      sorting,
      rowSelection,
      globalFilter,
      columnVisibility
    },
    initialState: {
      columnVisibility: initialColumnVisibility
    },
    enableRowSelection: true,
    getRowId: (row) => row.id,
    onSortingChange: setSorting,
    onRowSelectionChange: setRowSelection,
    onGlobalFilterChange: setGlobalFilter,
    getRowCanExpand: () => true,
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    getCoreRowModel: getCoreRowModel(),
    // getPaginationRowModel: getPaginationRowModel(),
    getExpandedRowModel: getExpandedRowModel(),
    globalFilterFn: fuzzyFilter,
    onColumnVisibilityChange: setColumnVisibility
  });

  const backColor = alpha(theme.palette.primary.lighter, 0.1);
  let headers = [];
  // columns.map((col) => {
  //   if (col.accessorKey) {
  //     headers.push({
  //       label: typeof col.header === 'string' ? col.header : '#',
  //       key: col.accessorKey
  //     });
  //   }
  // });
  columns.map((col) => {
    if (col.accessorKey && col.accessorKey !== 'documents_uploaded' && col.accessorKey !== 'assignee_id') {
      headers.push({
        label: typeof col.header === 'string' ? col.header : '#',
        key: col.accessorKey
      });
    }
  });

  headers.push({
    label: 'Assigned To',
    key: 'assigneeName'
  });
  headers.push({
    label: 'Documents Uploaded',
    key: 'uploadedDocs',
    format: (value) => (value ? `'${value}` : '')
  });

  headers.push({
    label: 'Total Document',
    key: 'totalMandatoryDocs',
    format: (value) => (value ? `'${value}` : '')
  });

  const handleFilterChangeForMultipleSelect = (key, value) => {
    setFilters((prevFilters) => ({
      ...prevFilters,
      [key]: Array.isArray(value) ? value.join(',') : value
    }));
  };

  return (
    <MainCard content={false}>
      <Stack
        direction={{ xs: 'column', sm: 'row' }}
        spacing={2}
        alignItems="center"
        justifyContent="space-between"
        sx={{
          padding: 2,
          ...(downSM && {
            '& .MuiOutlinedInput-root, & .MuiFormControl-root': {
              width: '100%'
            }
          }),
          position: 'sticky',
          top: 0,
          zIndex: 1000
        }}
      >
        <Stack
          direction={{ xs: 'column', sm: 'row' }}
          spacing={2}
          alignItems="center"
          justifyContent="space-between"
          sx={{ width: { xs: '100%', sm: 'auto', overflowX: 'scroll' } }}
        >
          {/* <DebouncedInput value={globalFilter ?? ''} onFilterChange={(value) => setGlobalFilter(String(value))} placeholder={`Search`} /> */}

          <SelectColumnVisibility
            {...{
              getVisibleLeafColumns: table.getVisibleLeafColumns,
              getIsAllColumnsVisible: table.getIsAllColumnsVisible,
              getToggleAllColumnsVisibilityHandler: table.getToggleAllColumnsVisibilityHandler,
              getAllColumns: table.getAllColumns
            }}
          />

          <Select
            size="small"
            multiple
            value={filters.source ? filters.source.split(',') : []}
            onChange={(e) => handleFilterChangeForMultipleSelect('source', e.target.value.join(','))}
            displayEmpty
            renderValue={(selected) => (
              <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                {selected.length
                  ? selected.map((value) => <Chip key={value} label={value} variant="outlined" color="primary" size="small" />)
                  : 'All Sources'}
              </Box>
            )}
            className="text-color-grey change_svg"
          >
            {SOURCE_TYPES.map((source) => (
              <MenuItem key={source} value={source}>
                {source}
              </MenuItem>
            ))}
          </Select>

          <Select
            size="small"
            value={filters.state}
            onChange={(e) => handleFilterChange('state', e.target.value)}
            displayEmpty
            className="text-color-grey change_svg"
          >
            <MenuItem value="">All States</MenuItem>
            {states.map((state) => (
              <MenuItem key={state.name} value={state.name}>
                {state.name}
              </MenuItem>
            ))}
          </Select>

          <Select
            size="small"
            value={filters.assignee_id}
            onChange={(e) => handleFilterChange('assignee_id', e.target.value)}
            displayEmpty
            className="text-color-grey change_svg"
          >
            <MenuItem value="">All Assignees</MenuItem>
            {portalUsers.map((user) => (
              <MenuItem key={user.id} value={user.id}>
                {user.fullname}
              </MenuItem>
            ))}
          </Select>

          <Select
            size="small"
            value={filters.ngo_status}
            onChange={(e) => handleFilterChange('ngo_status', e.target.value)}
            displayEmpty
            className="text-color-grey change_svg"
          >
            <MenuItem value="">All Status</MenuItem>
            {NGO_STATUS_LIST.map((status) => (
              <MenuItem key={status} value={status}>
                {status}
              </MenuItem>
            ))}
          </Select>

          {user?.roleInfo?.name === 'DR_Management' && (
            <Stack direction="row" spacing={2} alignItems="center">
              <Button
                variant="outlined"
                color="secondary"
                style={{ borderColor: '#D9D9D9' }}
                size="small"
                onClick={handleExport}
                disabled={isExporting}
              >
                {isExporting ? 'Exporting..' : 'Export'}
              </Button>

              {isDataReadyForDownload && (
                <CSVLink
                  data={exportData ? exportData : []}
                  headers={headers}
                  filename={`exported_data_${new Date().toLocaleDateString()}.csv`}
                  onClick={handleDownload}
                >
                  <Button variant="contained" color="success" size="small">
                    Download
                  </Button>
                </CSVLink>
              )}
            </Stack>
          )}
        </Stack>
        <Stack direction="row" spacing={2} alignItems="center">
          <SearchWithButton
            value={globalFilter ?? ''}
            onFilterChange={(value) => {
              setGlobalFilter(String(value));
            }}
            placeholder={`Search`}
          />
          <Tooltip title="Refresh">
            <IconButton onClick={handleRefresh}>
              <SyncOutlined />
            </IconButton>
          </Tooltip>
        </Stack>
      </Stack>

      <ScrollX>
        <Stack>
          <TableContainer component={Paper} style={{ overflowX: 'auto' }}>
            <Table size="small" style={{ width: table.getCenterTotalSize() }}>
              <TableHead className="sticky-header">
                {table.getHeaderGroups().map((headerGroup) => (
                  <TableRow key={headerGroup.id}>
                    {headerGroup.headers.map((header) => (
                      <TableCell
                        key={header.id}
                        onClick={header.column.getToggleSortingHandler()}
                        className={
                          header.column.getCanSort()
                            ? `cursor-pointer prevent-select ${header.column.className ? header.column.className : ''}`
                            : ''
                        }
                        // style={{ textTransform: 'none' }}
                        style={
                          header.id === 'actions'
                            ? { position: 'sticky', right: 0, background: '#fff', zIndex: 1, textTransform: 'none' }
                            : { textTransform: 'none' }
                        }
                      >
                        {header.isPlaceholder ? null : (
                          <Stack direction="row" spacing={1} alignItems="center">
                            <Box>{flexRender(header.column.columnDef.header, header.getContext())}</Box>
                            {header.column.getCanSort() && <HeaderSort column={header.column} />}
                          </Stack>
                        )}
                      </TableCell>
                    ))}
                  </TableRow>
                ))}
              </TableHead>

              <TableBody>
                {isFilterLoading ? (
                  <TableRow>
                    <TableCell colSpan={columns.length} align="center">
                      <CustomCircularLoaded open={isFilterLoading} />
                      <Box>Loading filtered data...</Box>
                    </TableCell>
                  </TableRow>
                ) : isLoading ? (
                  <TableRow>
                    <TableCell colSpan={columns.length}>
                      <CustomCircularLoaded open={isLoading} />
                      <Box>Loading your data...</Box>
                    </TableCell>
                  </TableRow>
                ) : searchFilteredData.length > 0 ? (
                  table
                    .getRowModel()
                    .rows.slice(0, filteredPageSize)
                    .map((row) => (
                      <TableRow key={row.id}>
                        {row.getVisibleCells().map((cell) => (
                          <TableCell
                            style={cell.column.id === 'actions' ? { position: 'sticky', right: 0, background: '#fff', zIndex: 1 } : {}}
                            key={cell.id}
                          >
                            {flexRender(cell.column.columnDef.cell, cell.getContext())}
                          </TableCell>
                        ))}
                      </TableRow>
                    ))
                ) : (
                  <TableRow>
                    <TableCell colSpan={columns.length} align="center" style={{ color: 'red', fontWeight: 'bold' }}>
                      No NGOs found matching the provided filters.
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </TableContainer>
          <Divider />
          <Box
            sx={{
              px: 2,
              py: 1,
              position: 'sticky',
              bottom: 0, // Ensure it sticks at the bottom
              zIndex: 1000, // Add zIndex to make sure it stays on top of other content
              backgroundColor: theme.palette.background.paper
            }}
          >
            <TablePagination
              getPageCount={() => Math.ceil(filteredTotalCount / filteredPageSize)}
              setPageIndex={handlePageChange}
              setPageSize={handlePageSizeChange}
              getState={() => ({
                pagination: {
                  pageIndex: filteredCurrentPage - 1,
                  pageSize: filteredPageSize
                }
              })}
              initialPageSize={filteredPageSize}
            />
          </Box>
        </Stack>
      </ScrollX>
    </MainCard>
  );
}

CustomTableCheckBox.propTypes = {
  data: PropTypes.array,
  columns: PropTypes.array,
  modalToggler: PropTypes.func,
  category: PropTypes.string,
  statusList: PropTypes.array,
  setRowSelection: PropTypes.func,
  rowSelection: PropTypes.object,
  openAssignDialog: PropTypes.func,
  openFeaturedDialog: PropTypes.func,
  openAssignGradeDialog: PropTypes.func,
  tabValue: PropTypes.number,
  roleName: PropTypes.string,
  totalCount: PropTypes.number,
  currentPage: PropTypes.number,
  pageSize: PropTypes.number,
  onPageChange: PropTypes.func,
  onPageSizeChange: PropTypes.func
};
