import axiosServices from 'utils/axios_node';

const API_BASE_URL = `${import.meta.env.VITE_APP_APPLICATION_API_URL}/api`;

//themes function
export const getThemesdata = async () => {
  try {
    const response = await axiosServices.get(`${API_BASE_URL}/themes`);
    return response?.data;
  } catch (error) {
    console.error('Error fetching themes data');
    return error?.message;
  }
};
export const addThemeService = async (payload) => {
  try {
    const response = await axiosServices.post(`${API_BASE_URL}/themes`, payload);
    return response;
  } catch (error) {
    console.error('Error adding themes data');
    return error?.message;
  }
};
export const updateThemeService = async (id, payload) => {
  try {
    const response = await axiosServices.put(`${API_BASE_URL}/themes/${id}`, payload);
    return response;
  } catch (error) {
    console.error('Error adding themes data');
    return error?.message;
  }
};

export const getCoverImages = async () => {
  try {
    const response = await axiosServices.get(`${API_BASE_URL}/banner-images`);
    return response?.data;
  } catch (error) {
    console.error('Error fetching Cover images data data');
    return error?.message;
  }
};
export const addCoverImageService = async (payload) => {
  try {
    const response = await axiosServices.post(`${API_BASE_URL}/banner-images`, payload);
    return response;
  } catch (error) {
    console.error('Error CoverImage data');
    return error?.message;
  }
};
export const updateCoverImageService = async (id, payload) => {
  try {
    const response = await axiosServices.put(`${API_BASE_URL}/banner-images/${id}`, payload);
    return response;
  } catch (error) {
    console.error('Error CoverImage data');
    return error?.message;
  }
};

export const getImpactAreas = async (ngoId) => {
  try {
    if (ngoId) {
      const response = await axiosServices.get(`${API_BASE_URL}/impact-areas?ngoId=${ngoId}`);
      return response?.data;
    } else {
      const response = await axiosServices.get(`${API_BASE_URL}/impact-areas`);
      return response?.data;
    }
  } catch (error) {
    console.error('Error fetching impact areas data data');
    return error?.message;
  }
};

export const addImpactAreasService = async (payload) => {
  try {
    const response = await axiosServices.post(`${API_BASE_URL}/impact-areas`, payload);
    return response;
  } catch (error) {
    console.error('Error impact-areas data');
    return error?.message;
  }
};
export const updateImpactAreasService = async (id, payload) => {
  try {
    const response = await axiosServices.put(`${API_BASE_URL}/impact-areas/${id}`, payload);
    return response;
  } catch (error) {
    console.error('Error impact-areas data');
    return error?.message;
  }
};
