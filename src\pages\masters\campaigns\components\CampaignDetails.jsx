import PropTypes from 'prop-types';
import { useState, useEffect, useCallback } from 'react';
// material-ui
import Button from '@mui/material/Button';
import Checkbox from '@mui/material/Checkbox';
import { MobileDatePicker, MobileTimePicker, TimePicker } from '@mui/x-date-pickers';
import FormControlLabel from '@mui/material/FormControlLabel';
import Grid from '@mui/material/Grid';
import InputLabel from '@mui/material/InputLabel';
import Stack from '@mui/material/Stack';
import Typography from '@mui/material/Typography';
import TextField from '@mui/material/TextField';
import FormHelperText from '@mui/material/FormHelperText';
import dayjs from 'dayjs';
import { toast, ToastContainer } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import { useNavigate } from 'react-router';

// third-party
import { useFormik } from 'formik';
import * as yup from 'yup';

// project imports
import AnimateButton from 'components/@extended/AnimateButton';
import { DatePicker, LocalizationProvider } from '@mui/x-date-pickers';
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';
import { Autocomplete, Box, CircularProgress, MenuItem, Radio, RadioGroup, Select } from '@mui/material';
import { FormattedMessage } from 'react-intl';
import { addCampaign, getNGOSBySearchTerm, updateCampaign } from 'api/campaigns.service';
import { getNgoById } from 'api/ngos.service';
import { calculateProfileCompletion } from 'utils/permissionUtils';
import useAuth from 'hooks/useAuth';

const validationSchema = yup.object({
  name: yup.string().required('Name is required').max(60, 'Campaign Name cannot exceed 60 characters'),
  campaign_start_date: yup.date().required('Start Date is required'),
  campaign_end_date: yup
    .date()
    .nullable() // Allows null values initially
    .when('sameday_event', {
      is: (value) => value === 'no', // Check if sameday_event is "no"
      then: () =>
        yup
          .date() // If yes, apply these rules
          .required('End Date is required')
          .min(yup.ref('campaign_start_date'), 'End Date must be greater than Start Date'),
      otherwise: () => yup.date().nullable() // If not "no", allow nullable end date
    }),
  event_time: yup
    .date()
    .nullable() // Allows null values initially
    .when('sameday_event', {
      is: (value) => value === 'yes', // Check if sameday_event is "no"
      then: () =>
        yup
          .date() // If yes, apply these rules
          .required('Event Time is required'),
      otherwise: () => yup.date().nullable() // If not "no", allow nullable end date
    }),
  category_id: yup.number().required('Category is required'),
  format: yup.string().required('Format is required'),
  sameday_event: yup.string().required('Same Day Event field is required')
});

export default function CampaignDetails({
  campaignDetails,
  setCampaignDetails,
  handleNext,
  setErrorIndex,
  categories,
  ngoList,
  setNgoList
}) {
  const [searchTerm, setSearchTerm] = useState('');
  const [loading, setLoading] = useState(false);
  const [ratio, setRatio] = useState('');
  const [ngoInfo, setNgoInfo] = useState(null);

  const { user } = useAuth();
  const [profileCompletePercentage, setProfileCompletePercentage] = useState(0);
  const navigate = useNavigate();

  const fetchNgos = useCallback(async (query) => {
    if (query.length < 4) return;
    setLoading(true);
    try {
      const response = await getNGOSBySearchTerm(query, 'Verified');
      setNgoList(response);
    } catch (error) {
      console.error('Error fetching NGOs:', error);
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    const debounceFetch = setTimeout(fetchNgos(searchTerm), 500); // Debounce the API call

    return () => clearTimeout(debounceFetch); // Cleanup

    // fetchNgos(searchTerm);
  }, [searchTerm]);

  useEffect(() => {
    const fetchNgoDataAndCalculate = async () => {
      if (user?.ngo_id) {
        try {
          const ngodata = await getNgoById(user.ngo_id);
          const profilepercentage = await calculateProfileCompletion(ngodata);
          setProfileCompletePercentage(profilepercentage);
          setNgoInfo(ngodata);
        } catch (error) {
          console.error('Error fetching NGO data:', error);
        }
      } else {
        setProfileCompletePercentage(0);
      }
    };

    fetchNgoDataAndCalculate();
  }, [user?.ngo_id]);

  const handleSubmit = async (status) => {
    try {
      let formData = new FormData();

      const formattedEventTime = formik.values.event_time ? dayjs(formik.values.event_time).format('HH:mm:ss') : null;

      const campaignData1 = {
        ...formik.values,
        campaign_start_date: formik.values.campaign_start_date ? dayjs(formik.values.campaign_start_date).format('YYYY-MM-DD') : null,
        campaign_end_date: formik.values.campaign_end_date ? dayjs(formik.values.campaign_end_date).format('YYYY-MM-DD') : null,
        status: 'Draft',
        ratio: ratio,
        createdBy: user.id,
        event_time: formattedEventTime
      };

      Object.keys(campaignData1).forEach((key) => {
        if (campaignData1[key] !== null) {
          formData.append(key, campaignData1[key]);
        }
      });

      if (campaignDetails?.id) {
        await updateCampaign(campaignDetails?.id, formData, 'CampaignForm');
        toast.success('Campaign updated successfully!');
      } else {
        await addCampaign(formData, 'CampaignForm');
        toast.success('Campaign added successfully!');
      }

      setTimeout(() => {
        navigate('/masters/campaigns');
      }, 1000);
    } catch (error) {
      console.error('Failed to save campaign:', error);
      toast.error('Failed to save campaign');
    }
  };

  const formik = useFormik({
    initialValues: {
      name: campaignDetails.name || '',
      ratio: campaignDetails?.ratio,

      description: campaignDetails.description || '',
      campaign_start_date: campaignDetails.campaign_start_date ? dayjs(campaignDetails.campaign_start_date) : null,
      sameday_event: campaignDetails.sameday_event || 'yes',
      campaign_end_date: campaignDetails.campaign_end_date ? dayjs(campaignDetails.campaign_end_date) : null,
      format: campaignDetails.format || '',
      category_id: campaignDetails.category_id || '',
      ngo_id: campaignDetails?.ngo_id || user?.ngo_id,
      event_time: campaignDetails.event_time ? dayjs(campaignDetails.event_time, 'HH:mm:ss') : null
    },
    validationSchema,
    enableReinitialize: true,
    onSubmit: (values) => {
      setCampaignDetails({
        name: values.name,
        ratio: ratio,
        description: values.description || '',
        campaign_start_date: values.campaign_start_date ? dayjs(values.campaign_start_date).format('YYYY-MM-DD') : null,
        campaign_end_date: values.campaign_end_date
          ? dayjs(values.campaign_end_date).format('YYYY-MM-DD')
          : dayjs(values.campaign_start_date).format('YYYY-MM-DD'),
        sameday_event: values.sameday_event || '',
        category_id: values.category_id || '',
        format: values.format || '',
        ngo_id: user?.ngo_id ? user?.ngo_id : values?.ngo_id,
        event_time: values.event_time ? values.event_time.format('HH:mm:ss') : null
      });
      handleNext(values);
    }
  });

  //handling toast meesage for incomplete profile
  const handleButtonClick = (action) => {
    if (user?.ngo_id && profileCompletePercentage !== 100) {
      const message =
        action === 'Draft' ? 'Profile completion must be 100% to save as a draft.' : 'Profile completion must be 100% to proceed.';
      toast.error(message);
      return;
    } else {
      if (action === 'Draft') {
        handleSubmit('Draft');
      } else if (action === 'Next') {
        setErrorIndex(0);
      }
    }
  };

  return (
    <Box sx={{ pl: 1 }}>
      {/* <Typography variant="h5" gutterBottom sx={{ mb: 2 }}>
        Basic Information
      </Typography> */}
      <form onSubmit={formik.handleSubmit} id="validation-forms" autoComplete="off">
        <Grid container spacing={3}>
          {!user?.ngo_id && !campaignDetails?.ngo_id && (
            <Grid item xs={12} sm={4}>
              <Stack spacing={1}>
                <Autocomplete
                  options={ngoList}
                  getOptionLabel={(option) => option.name}
                  value={ngoList.find((cat) => cat.id === formik.values.ngo_id) || null}
                  onInputChange={(event, newInputValue) => {
                    setSearchTerm(newInputValue);
                  }}
                  onChange={(event, newValue) => formik.setFieldValue('ngo_id', newValue?.id || null)}
                  loading={loading} // Show the loading state when data is being fetched
                  renderInput={(params) => (
                    <TextField
                      {...params}
                      label="Search NGO"
                      error={formik.touched.ngo_id && Boolean(formik.errors.ngo_id)}
                      helperText={formik.touched.ngo_id && formik.errors.ngo_id}
                      required
                      InputProps={{
                        ...params.InputProps,
                        endAdornment: (
                          <>
                            {loading ? <CircularProgress color="primary" size={20} /> : null}
                            {params.InputProps.endAdornment}
                          </>
                        )
                      }}
                    />
                  )}
                />
              </Stack>
            </Grid>
          )}
          <Grid item xs={12} sm={4}>
            <Stack spacing={1}>
              <TextField
                id="name"
                label="Campaign Name"
                name="name"
                required
                value={formik.values.name}
                onChange={formik.handleChange}
                error={formik.touched.name && Boolean(formik.errors.name)}
                helperText={formik.touched.name && formik.errors.name}
                fullWidth
                autoComplete="off"
              />
            </Stack>
          </Grid>
          <Grid item xs={12} sm={4}>
            <Stack spacing={1}>
              <Autocomplete
                options={categories}
                getOptionLabel={(option) => option.name}
                value={categories.find((cat) => cat.id === formik.values.category_id) || null}
                onChange={(event, newValue) => {
                  formik.setFieldValue('category_id', newValue?.id || null);
                  setRatio(newValue?.ratio || '');
                }}
                renderInput={(params) => (
                  <TextField
                    {...params}
                    label="Select Category"
                    required
                    error={formik.touched.category_id && Boolean(formik.errors.category_id)}
                    helperText={formik.touched.category_id && formik.errors.category_id}
                  />
                )}
              />
            </Stack>
          </Grid>
          {user?.ngo_id ? (
            <Grid item xs={12} sm={4}></Grid>
          ) : (
            <Grid item xs={12} sm={4}>
              <TextField
                label="Ratio"
                type="number"
                value={formik.values.ratio || ''}
                onChange={(e) => {
                  const newValue = e.target.value;
                  setRatio(newValue); // Update local state
                  formik.setFieldValue('ratio', newValue); // Update formik values
                }}
                fullWidth
              />
            </Grid>
          )}

          <Grid item xs={4}>
            <Stack spacing={1}>
              <FormControlLabel
                control={
                  <Checkbox
                    checked={formik.values.sameday_event === 'yes'}
                    onChange={(e) => {
                      formik.setFieldValue('sameday_event', e.target.checked ? 'yes' : 'no');
                      e.target.checked && formik.setFieldValue('campaign_end_date', null);
                    }}
                    value="yes"
                    name="sameday_event"
                    color="primary"
                  />
                }
                label="Same Day Event"
              />
            </Stack>
          </Grid>
          <Grid item xs={12} sm={4}>
            <Stack spacing={1}>
              <LocalizationProvider dateAdapter={AdapterDayjs}>
                <MobileDatePicker
                  label="Start Date *"
                  value={formik.values.campaign_start_date}
                  format="DD/MM/YYYY"
                  minDate={dayjs()}
                  maxDate={dayjs().add(102, 'day')}
                  onChange={(newValue) => formik.setFieldValue('campaign_start_date', newValue)}
                  error={formik.touched.campaign_start_date && Boolean(formik.errors.campaign_start_date)}
                  helperText={formik.touched.campaign_start_date && formik.errors.campaign_start_date}
                  renderInput={(params) => (
                    <TextField
                      {...params}
                      fullWidth
                      placeholder="Start Date"
                      margin="normal"
                      error={formik.touched.campaign_start_date && Boolean(formik.errors.campaign_start_date)}
                      helperText={formik.touched.campaign_start_date && formik.errors.campaign_start_date}
                    />
                  )}
                />
                {formik.touched.campaign_start_date && formik.errors.campaign_start_date && (
                  <FormHelperText error>{formik.errors.campaign_start_date}</FormHelperText>
                )}
              </LocalizationProvider>
            </Stack>
          </Grid>
          {formik.values.sameday_event === 'yes' ? (
            <Grid item xs={12} sm={4}>
              <Stack spacing={1}>
                <LocalizationProvider dateAdapter={AdapterDayjs}>
                  <MobileTimePicker
                    label="Event Time *"
                    value={formik.values.event_time ? dayjs(formik.values.event_time, 'HH:mm:ss') : null} // Decode time
                    required
                    onChange={(newValue) => formik.setFieldValue('event_time', newValue)}
                    renderInput={(params) => (
                      <TextField
                        {...params}
                        fullWidth
                        error={formik.touched.event_time && Boolean(formik.errors.event_time)}
                        helperText={formik.touched.event_time && formik.errors.event_time}
                        required
                      />
                    )}
                  />
                  {formik.touched.event_time && formik.errors.event_time && (
                    <FormHelperText error>{formik.errors.event_time}</FormHelperText>
                  )}
                </LocalizationProvider>
              </Stack>
            </Grid>
          ) : (
            <Grid item xs={12} sm={4}>
              <Stack spacing={1}>
                <LocalizationProvider dateAdapter={AdapterDayjs}>
                  <MobileDatePicker
                    label="End Date"
                    value={formik.values.campaign_end_date}
                    required
                    disabled={formik.values.sameday_event === 'yes'}
                    onChange={(newValue) => formik.setFieldValue('campaign_end_date', newValue)}
                    format="DD/MM/YYYY"
                    minDate={
                      formik.values.campaign_start_date ? dayjs(formik.values.campaign_start_date).add(1, 'day') : dayjs().add(1, 'day')
                    }
                    maxDate={dayjs().add(102, 'day')}
                    renderInput={(params) => (
                      <TextField
                        {...params}
                        fullWidth
                        margin="normal"
                        error={formik.touched.campaign_end_date && Boolean(formik.errors.campaign_end_date)}
                        helperText={formik.touched.campaign_end_date && formik.errors.campaign_end_date}
                      />
                    )}
                  />
                  {formik.touched.campaign_end_date && formik.errors.campaign_end_date && (
                    <FormHelperText error>{formik.errors.campaign_end_date}</FormHelperText>
                  )}
                </LocalizationProvider>
              </Stack>
            </Grid>
          )}
          <Grid item xs={12} sm={4}>
            <Stack spacing={1}>
              <Autocomplete
                options={['Physical', 'Virtual']}
                getOptionLabel={(option) => option}
                value={['Physical', 'Virtual'].find((cat) => cat === formik.values.format) || null}
                onChange={(event, newValue) => formik.setFieldValue('format', newValue || null)}
                renderInput={(params) => (
                  <TextField
                    {...params}
                    label="Format *"
                    error={formik.touched.format && Boolean(formik.errors.format)}
                    helperText={formik.touched.format && formik.errors.format}
                  />
                )}
              />
            </Stack>
          </Grid>
          <Grid item xs={4}>
            <Stack spacing={1}>
              <TextField
                label="Descripion"
                id="description"
                name="description"
                multiline
                rows={3}
                placeholder="Provide more details about the campaign"
                value={formik.values.description}
                onChange={formik.handleChange}
                error={formik.touched.description && Boolean(formik.errors.description)}
                helperText={formik.touched.description && formik.errors.description}
                fullWidth
              />
            </Stack>
          </Grid>
          <Grid item xs={12}>
            <Stack direction="row" justifyContent="flex-end">
              <AnimateButton>
                <Button
                  variant="contained"
                  type="button"
                  sx={{ my: 3, ml: 1 }}
                  onClick={() => handleButtonClick('Draft')}
                  // disabled={user?.ngo_id && profileCompletePercentage !== 100}
                >
                  <FormattedMessage id="saveDraft" />
                </Button>
              </AnimateButton>
              <AnimateButton>
                <Button
                  variant="contained"
                  sx={{ my: 3, ml: 1 }}
                  type="submit"
                  // onClick={() => handleButtonClick('Next')}
                  onClick={() => setErrorIndex(0)}
                  disabled={user?.ngo_id && ngoInfo?.status === "Verified"}
                >
                  <FormattedMessage id="next" />
                </Button>
              </AnimateButton>
            </Stack>
          </Grid>
        </Grid>
      </form>
      <ToastContainer autoClose={6000} />
    </Box>
  );
}
