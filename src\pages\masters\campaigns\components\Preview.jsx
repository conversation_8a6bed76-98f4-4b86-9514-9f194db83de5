import {
  Avatar,
  Box,
  Button,
  Card,
  CardContent,
  CardMedia,
  Chip,
  CircularProgress,
  Divider,
  FormControl,
  Grid,
  InputLabel,
  List,
  ListItem,
  ListItemIcon,
  ListItemSecondaryAction,
  MenuItem,
  Select,
  Stack,
  Typography,
  useMediaQuery
} from '@mui/material';
import AnimateButton from 'components/@extended/AnimateButton';
import MainCard from 'components/MainCard';
import React, { Fragment, useEffect, useState } from 'react';
import { FormattedMessage } from 'react-intl';
import { ToastContainer, toast } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';

import 'react-draft-wysiwyg/dist/react-draft-wysiwyg.css';
import ListItemDividerComponent from './ListItemDividerComponent';
import defaultImages from 'assets/images/users/default.png';
import {
  AimOutlined,
  CalendarOutlined,
  CreditCardOutlined,
  FacebookFilled,
  InstagramFilled,
  LinkedinFilled,
  <PERSON>Outlined,
  PhoneOutlined,
  TwitterSquareFilled,
  YoutubeFilled
} from '@ant-design/icons';
import { facebookColor, twitterColor, ThemeMode } from 'config';
import { instagramColor } from 'config';
import { youtubeColor } from 'config';
import moment from 'moment';
import { Link, useParams } from 'react-router-dom';
import useAuth from 'hooks/useAuth';
import { getMileStones, patchCampaign } from 'api/campaigns.service';
import { Timeline, TimelineConnector, TimelineContent, TimelineDot, TimelineItem, TimelineSeparator } from '@mui/lab';
import { BASE_URL } from 'sections/apps/profiles/profile.service';
import { format } from 'date-fns';
import FeedsCard from 'sections/widget/data/FeedsCard';
import { Editor } from 'react-draft-wysiwyg';
import { ContentState, EditorState, convertFromRaw, convertToRaw } from 'draft-js';
import { addNotifications, getNotifications } from 'pages/masters/apis/notification.service';
import { CAMPAIGN_STATUS_LIST } from 'utils/statusconstans';
import dayjs from 'dayjs';

export default function Preview({
  handleBack,
  handleNext,
  campaignDetails,
  targetImpactInformation,
  locationDetails,
  handleSubmit,
  categories
}) {
  const matchDownMD = useMediaQuery((theme) => theme.breakpoints.down('md'));
  const avatarImage = () => {
    return defaultImages;
  };
  const { user: userInfo } = useAuth();

  const { campaignId } = useParams();

  const [milestones, setMilestones] = useState([]);
  const [commentseditorState, setCommentsEditorState] = useState(() => EditorState.createEmpty());
  const [commentEditing, setCommentEditing] = useState(true);
  const [loading, setLoading] = useState(false);
  const [comments, setComments] = useState([]);
  const [campaignInfo, setCampaignInfo] = useState({
    status: ''
  });

  useEffect(() => {
    const fetchMilestones = async () => {
      try {
        const response = await getMileStones(campaignDetails?.id);
        setMilestones(response || []);
      } catch (error) {
        console.error('Error fetching milestones:', error);
      }
    };

    if (campaignDetails?.id) {
      fetchMilestones();
    }
  }, [campaignDetails?.id]);

  useEffect(() => {
    if (campaignDetails?.id) {
      fetchComments(campaignDetails?.id, campaignDetails?.ngo_id);
    }
  }, [campaignDetails?.id]);

  const fetchComments = async (campaignId, ngoId) => {
    try {
      const response = await getNotifications(ngoId,'campaigns',campaignId);
      setComments(response || []);
    } catch (error) {
      console.error('Failed to fetch comments:', error);
      toast.error('Failed to fetch comments');
    }
  };

  const { user } = useAuth();
  const getCategoryValue = () => {
    const find = categories.find((cat) => cat.id === campaignDetails?.category_id);
    return find?.name || '';
  };

  const handleCommentSubmit = async (e) => {
    e.preventDefault();

    const contentState = commentseditorState.getCurrentContent();
    const description = JSON.stringify(convertToRaw(contentState));

    if (!contentState.hasText()) {
      toast.error('Please enter the message to notify');
      return;
    }

    const payload = {
      type_id: campaignDetails?.id,
      sender_id: userInfo.id,
      description,
      messageRead: 'no',
      type: 'campaigns',
      ngo_id: campaignDetails?.ngo_id
    };

    try {
      setLoading(true);

      const response = await addNotifications(payload);

      if (response.status) {
        toast.success('Notification sent successfully!');
        setCommentsEditorState(EditorState.createEmpty());
        fetchComments(campaignId);
      } else {
        throw new Error('Unexpected response');
      }
      fetchComments(campaignDetails?.id);
    } catch (error) {
      if (error.message.includes('email')) {
        toast.success('Notification sent successfully, but email delivery failed.');
        setCommentsEditorState(EditorState.createEmpty());
        fetchComments(campaignDetails?.id);
      } else {
        toast.error('Failed to send notification');
      }
      console.error('Error sending notification:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleNgoInfoChange = (e) => {
    const { name, value } = e.target;
    setCampaignInfo((prev) => ({ ...prev, [name]: value }));
  };

  const handleNgoInfoSubmit = async () => {
    try {
      if (!campaignInfo.status) {
        toast.error('The required field is empty. Please fill it out.');
        return;
      }

      const response = await patchCampaign(campaignId, campaignInfo, 'PreviewPage');

      if (response.id) {
        toast.success('Campaign status updated successfully!');
      } else {
        throw new Error('Unexpected response');
      }
      setCampaignInfo({
        status: ''
      });
      setTimeout(() => {
        window.location.reload();
      }, 1000);
    } catch (error) {
      console.error('Error updating Campaign Information:', error);
      toast.error('Failed to update Campaign Information');
    }
  };


  return (
    <>
      <div>
        <Grid container spacing={3}>
          <Grid item xs={12} sm={5} md={4} xl={3}>
            <Grid container spacing={3}>
              <Grid item xs={12}>
                <MainCard>
                  <Grid container spacing={3}>
                    <Grid item xs={12}>
                      <Stack direction="row" justifyContent="flex-end">
                        {campaignDetails?.status && (
                          <Chip
                            color={
                              campaignDetails?.status === 'Approved' ||
                              campaignDetails?.status === 'Completed' ||
                              campaignDetails?.status === 'Live'
                                ? 'success'
                                : campaignDetails?.status === 'In Review'
                                  ? 'info'
                                  : campaignDetails?.status === 'Pending'
                                    ? 'warning'
                                    : 'error'
                            }
                            label={campaignDetails?.status || 'Draft'}
                            size="small"
                          />
                        )}
                      </Stack>
                      <Stack spacing={2.5} alignItems="center" marginTop={2}>
                        {campaignDetails?.fileName ? (
                          <CardMedia
                            component="img"
                            alt={campaignDetails?.fileName}
                            height="180"
                            width="10"
                            image={`${BASE_URL}/fetchCampaignImage/${campaignDetails?.fileName}`}
                            style={{ objectFit: 'fill' }}
                          />
                        ) : (
                          // <Avatar alt="Avatar 1" size="xl" src={avatarImage(`./default.png`)} />
                          <Typography height="180" width="10" fontWeight={'bold'}>
                            No Image
                          </Typography>
                        )}
                        <Stack spacing={0.5} alignItems="center">
                          <Typography variant="h5">{campaignDetails?.name}</Typography>
                          {campaignDetails?.sameday_event === 'yes' && (
                            <Fragment>
                              <Typography color="secondary">{'Same Day Event'}</Typography>
                              <Typography color="secondary">{moment(campaignDetails?.campaign_start_date).format('DD-MM-YYYY')}</Typography>
                            </Fragment>
                          )}

                          {campaignDetails?.sameday_event === 'no' && (
                            <Typography color="secondary">
                              {moment(campaignDetails?.campaign_start_date).format('DD-MM-YYYY')} to{' '}
                              {moment(campaignDetails?.campaign_end_date).format('DD-MM-YYYY')}
                            </Typography>
                          )}
                          <Typography color="secondary">
                            {targetImpactInformation?.donor_target_type === 'monetary_donation'
                              ? 'Monetary Donation'
                              : targetImpactInformation?.donor_target_type === 'in_kind'
                                ? 'In Kind'
                                : 'Please update'}
                          </Typography>

                          {locationDetails?.promotional_hashtag && (
                            <Typography color={twitterColor}>
                              {locationDetails?.promotional_hashtag?.startsWith('#')
                                ? locationDetails.promotional_hashtag
                                : `#${locationDetails?.promotional_hashtag}`}
                            </Typography>
                          )}
                          <Stack direction="row" spacing={2} alignItems="center">
                            {locationDetails?.facebook_url && (
                              <a href={locationDetails.facebook_url} target="_blank" rel="noopener noreferrer">
                                <FacebookFilled style={{ color: facebookColor, fontSize: '1.5rem', cursor: 'pointer' }} />
                              </a>
                            )}
                            {locationDetails?.instagram_url && (
                              <a href={locationDetails.instagram_url} target="_blank" rel="noopener noreferrer">
                                <InstagramFilled style={{ color: instagramColor, fontSize: '1.5rem', cursor: 'pointer' }} />
                              </a>
                            )}
                            {locationDetails?.youtube_url && (
                              <a href={locationDetails.youtube_url} target="_blank" rel="noopener noreferrer">
                                <YoutubeFilled style={{ color: youtubeColor, fontSize: '1.5rem', cursor: 'pointer' }} />
                              </a>
                            )}
                            {locationDetails?.twitter_url && (
                              <a href={locationDetails.twitter_url} target="_blank" rel="noopener noreferrer">
                                <TwitterSquareFilled style={{ color: twitterColor, fontSize: '1.5rem', cursor: 'pointer' }} />
                              </a>
                            )}
                          </Stack>
                        </Stack>
                      </Stack>
                    </Grid>
                    <Grid item xs={12}>
                      <Divider />
                    </Grid>
                    <Grid item xs={12}>
                      <Stack direction="row" justifyContent="space-around" alignItems="center" style={{ marginBottom: '1rem' }}>
                        <Stack spacing={0.5} alignItems="center">
                          <Typography variant="h5">{0}</Typography>
                          <Typography color="secondary">Contributors</Typography>
                        </Stack>
                        <Divider orientation="vertical" />
                        <Stack spacing={0.5} alignItems="center">
                          <Typography variant="h5">{0}</Typography>
                          <Typography color="secondary">Funds raised</Typography>
                        </Stack>
                      </Stack>

                      <Divider />
                      <Stack direction="row" justifyContent="space-around" alignItems="center" style={{ marginTop: '1rem' }}>
                        <Stack spacing={0.5} alignItems="center">
                          <Typography variant="h5">{0}</Typography>
                          <Typography color="secondary">Volunteers required</Typography>
                        </Stack>
                      </Stack>
                    </Grid>
                  </Grid>
                </MainCard>
              </Grid>
              <Grid item xs={12}>
                <MainCard title="Comments">
                  <FeedsCard comments={comments} />
                </MainCard>
              </Grid>
            </Grid>
          </Grid>
          <Grid item xs={12} sm={7} md={8} xl={9}>
            <Grid container spacing={3}>
              {user?.roleInfo?.name.startsWith('DR') && (
                <Grid item xs={12}>
                  <MainCard title="Update status">
                    <Grid container spacing={3} alignItems="center">
                      <Grid item xs={12} sm={9}>
                        <FormControl fullWidth margin="normal">
                          <InputLabel>Campaign Status</InputLabel>
                          <Select value={campaignDetails?.status || ''} name="status" onChange={handleNgoInfoChange}>
                            {CAMPAIGN_STATUS_LIST.filter((status) => status !== 'All').map((status, index) => (
                              <MenuItem key={index} value={status}>
                                {status}
                              </MenuItem>
                            ))}
                          </Select>
                        </FormControl>
                      </Grid>

                      <Grid item xs={12} sm={3}>
                        <Box display="flex" justifyContent="flex-end" alignItems="center">
                          <Button variant="contained" color="primary" onClick={handleNgoInfoSubmit} disabled={!campaignId}>
                            Submit
                          </Button>
                        </Box>
                      </Grid>
                    </Grid>
                  </MainCard>
                </Grid>
              )}
              <Grid item xs={12}>
                <MainCard title="Basic Information">
                  <List sx={{ py: 0 }}>
                    <ListItem divider>
                      <Grid container spacing={matchDownMD ? 0.5 : 3}>
                        <ListItemDividerComponent name="Name" value={campaignDetails?.name ? campaignDetails?.name : '-'} />
                        <ListItemDividerComponent
                          name="Start Date"
                          value={
                            campaignDetails?.campaign_start_date ? moment(campaignDetails?.campaign_start_date).format('DD-MM-YYYY') : '-'
                          }
                        />

                        {campaignDetails?.sameday_event === 'no' ? (
                          <ListItemDividerComponent
                            name="End Date"
                            value={
                              campaignDetails?.campaign_end_date ? moment(campaignDetails?.campaign_end_date).format('DD-MM-YYYY') : '-'
                            }
                          />
                        ) : campaignDetails?.sameday_event === 'yes' ? (
                          <ListItemDividerComponent name="Same Day Event" value="Yes" />
                        ) : null}
                      </Grid>
                    </ListItem>
                    <ListItem>
                      <Grid container spacing={matchDownMD ? 0.5 : 3}>
                        <ListItemDividerComponent
                          name="Description"
                          value={campaignDetails?.description ? campaignDetails?.description : '-'}
                        />

                        <ListItemDividerComponent name="Format" value={campaignDetails?.format ? campaignDetails?.format : '-'} />
                      </Grid>
                    </ListItem>
                  </List>
                </MainCard>
              </Grid>
              <Grid item xs={12}>
                <MainCard title="Target & Impact">
                  <List sx={{ py: 0 }}>
                    <ListItem divider>
                      <Grid container spacing={matchDownMD ? 0.5 : 3}>
                        <ListItemDividerComponent name="Category" value={getCategoryValue() ? getCategoryValue() : '-'} />

                        <ListItemDividerComponent
                          name="Impact Goal"
                          value={targetImpactInformation?.impact_goal ? targetImpactInformation?.impact_goal : '-'}
                        />

                        <ListItemDividerComponent
                          name="Volunteers Required"
                          value={
                            targetImpactInformation?.volunteers_required === 'yes'
                              ? 'Yes'
                              : targetImpactInformation?.volunteers_required === 'no'
                                ? 'No'
                                : '-'
                          }
                        />
                      </Grid>
                    </ListItem>
                    {targetImpactInformation?.volunteers_required === 'yes' && (
                      <ListItem>
                        <Grid container spacing={matchDownMD ? 0.5 : 3}>
                          <ListItemDividerComponent name="No of Volunteers" value={targetImpactInformation?.no_of_volunteers} />

                          <ListItemDividerComponent name="Skilled Volunteers" value={targetImpactInformation?.skills?.toString()} />
                        </Grid>
                      </ListItem>
                    )}
                  </List>
                </MainCard>
              </Grid>

              {user?.roleInfo?.name.startsWith('DR') && (
                <Grid item xs={12}>
                  <MainCard title="Add Comments">
                    <form onSubmit={handleCommentSubmit}>
                      <Grid container spacing={2}>
                        <Grid item xs={12}>
                          <Box style={{ marginTop: '16px' }} border={'1px solid #ccc'} padding={2} minHeight={250}>
                            <Editor
                              editorState={commentseditorState}
                              toolbarClassName="toolbarClassName"
                              wrapperClassName="wrapperClassName"
                              editorClassName="editorClassName"
                              toolbar={{
                                options: ['inline', 'fontSize', 'link'],
                                fontSize: {
                                  options: [8, 10, 12, 14, 16]
                                },
                                inline: {
                                  inDropdown: false,
                                  className: undefined,
                                  component: undefined,
                                  dropdownClassName: undefined,
                                  options: ['bold', 'italic', 'underline', 'strikethrough']
                                }
                              }}
                              onEditorStateChange={setCommentsEditorState}
                            />
                          </Box>
                        </Grid>
                        <Grid item xs={12}>
                          {commentEditing ? (
                            <div style={{ display: 'flex', justifyContent: 'end' }}>
                              <Button
                                type="submit"
                                variant="contained"
                                color="primary"
                                disabled={loading}
                                startIcon={loading && <CircularProgress size={20} color="inherit" />}
                              >
                                {loading ? 'Sending...' : 'Submit'}
                              </Button>
                            </div>
                          ) : (
                            <Button type="button" variant="contained" color="primary" onClick={handleEditClick}>
                              Edit
                            </Button>
                          )}
                        </Grid>
                      </Grid>
                    </form>
                  </MainCard>
                </Grid>
              )}

              <Grid item xs={12}>
                <MainCard title="Milestones">
                  {milestones.length > 0 ? (
                    <Timeline position="alternate">
                      {milestones.map((milestone, index) => (
                        <TimelineItem key={index}>
                          <TimelineSeparator>
                            <TimelineDot color="primary" />
                            {index < milestones.length - 1 && <TimelineConnector />}
                          </TimelineSeparator>
                          <TimelineContent>
                            <Grid container spacing={2}>
                              <Grid item xs={12} sm={4}>
                                <CardMedia
                                  component="img"
                                  image={`${BASE_URL}/fetchCampaignMileStoneImage/${milestone.fileName}`}
                                  alt={milestone.description}
                                  title={milestone.description}
                                  sx={{ borderRadius: 2, width: '100%', height: 'auto' }}
                                />
                              </Grid>
                              <Grid item xs={12} sm={8}>
                                <Typography variant="h6">{milestone.description}</Typography>
                                <Typography variant="body2" color="textSecondary">
                                  {dayjs(milestone.collection_date).format('DD/MM/YYYY')}
                                </Typography>
                              </Grid>
                            </Grid>
                          </TimelineContent>
                        </TimelineItem>
                      ))}
                    </Timeline>
                  ) : (
                    <Typography>No milestones available for this campaign.</Typography>
                  )}
                </MainCard>
              </Grid>
            </Grid>
          </Grid>
        </Grid>
      </div>
    </>
  );
}
