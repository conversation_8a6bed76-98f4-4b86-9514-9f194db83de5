import axiosServices from 'utils/axios_node';

const API_BASE_URL = `${import.meta.env.VITE_APP_APPLICATION_API_URL}/api`;

export const fetchRSVPDetails = async (campaignId, rsvpValue) => {
  const params = new URLSearchParams();
  params.append('campaignId', campaignId);

  if (rsvpValue) {
    params.append('rsvpValue', rsvpValue);
  }

  const response = await axiosServices.get(`${API_BASE_URL}/campaign-rsvps?${params.toString()}`);
  return response.data;
};

export const fetchAllRSVPInfo = async (campaignId) => {
  const params = new URLSearchParams();
  params.append('campaign_id', campaignId);

  // if (rsvpValue) {
  //   params.append('rsvpValue', rsvpValue);
  // }

  const response = await axiosServices.get(`${API_BASE_URL}/rsvps-informations?${params.toString()}`);
  return response.data;
};

export const addRSVPDetails = async (payload) => {
  const response = await axiosServices.post(`${API_BASE_URL}/rsvps-informations`, payload);
  return response.data;
};
