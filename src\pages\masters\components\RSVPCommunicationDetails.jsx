import React, { useEffect, useState } from 'react';
import {
  Box,
  Typography,
  TextField,
  Button,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Paper,
  TableContainer,
  Table,
  TableHead,
  TableRow,
  TableCell,
  TableBody,
  TablePagination
} from '@mui/material';
import { toast, ToastContainer } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import { useParams } from 'react-router';
import { addRSVPDetails } from '../apis/rvsp.service';

const RSVPSummaryComponent = ({ data }) => {
  const [selectedRSVP, setSelectedRSVP] = useState('yes');
  const [message, setMessage] = useState('');
  const [filteredData, setFilteredData] = useState(data.filter((item) => item.rsvp_value === 'yes'));
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [searchQuery, setSearchQuery] = useState('');
  const { campaignId } = useParams();

  useEffect(() => {
    applyFilters();
  }, [searchQuery, selectedRSVP, data]);

  const handleRSVPChange = (e) => {
    const value = e.target.value;
    setSelectedRSVP(value);
    if (value === 'all') {
      setFilteredData(data);
    } else {
      setFilteredData(data.filter((item) => item.rsvp_value === value));
    }
  };

  const handleSendMessage = async () => {
    const payload = {
      campaign_id: campaignId,
      ngo_id: 80313,
      message: message,
      rsvp_value: selectedRSVP
    };
    if (message.trim()) {
      const response = await addRSVPDetails(payload);
      if (response.status) {
        toast.success(`Message sent to ${selectedRSVP} recipients: \n${message}`);
        setMessage('');
      } else {
        toast.success(`Error while sending message`);
        // setMessage('');
      }
    } else {
      toast.error('Please enter a message to send.');
    }
  };

  const applyFilters = () => {
    let filtered = data.filter((item) => selectedRSVP === 'all' || item.rsvp_value === selectedRSVP);

    if (searchQuery) {
      filtered = filtered.filter((detail) => detail.message.toLowerCase().includes(searchQuery.toLowerCase()));
    }

    setFilteredData(filtered);
  };

  const handleSearchChange = (e) => {
    setSearchQuery(e.target.value);
  };

  const handleChangePage = (event, newPage) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  return (
    <Box sx={{ mt: 2 }}>
      <Box sx={{ display: 'flex', gap: 2, mb: 2, mt: 2, alignItems: 'center' }}>
        <FormControl sx={{ width: '200px' }}>
          <InputLabel>RSVP Filter</InputLabel>
          <Select value={selectedRSVP} onChange={handleRSVPChange} label="RSVP Filter">
            <MenuItem value="yes">Yes</MenuItem>
            <MenuItem value="no">No</MenuItem>
            <MenuItem value="all">All</MenuItem>
          </Select>
        </FormControl>
        {/* <TextField
          sx={{ flex: 1 }}
          variant="outlined"
          placeholder="Search by message sent"
          value={searchQuery}
          onChange={handleSearchChange}
        /> */}
      </Box>

      <Box>
        {filteredData.length === 0 ? (
          <Typography sx={{ margin: 2 }}>No messages available for the selected RSVP filter.</Typography>
        ) : (
          <>
            <TableContainer component={Paper}>
              <Table>
                <TableHead>
                  <TableRow sx={{ backgroundColor: '#f5f5f5' }}>
                    <TableCell>
                      <Typography variant="subtitle2" sx={{ fontWeight: 'bold', color: '#555' }}>
                        RSVP Value
                      </Typography>
                    </TableCell>
                    {/* <TableCell>
                      <Typography variant="subtitle2" sx={{ fontWeight: 'bold', color: '#555' }}>
                        NGO name
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Typography variant="subtitle2" sx={{ fontWeight: 'bold', color: '#555' }}>
                        Campaign name
                      </Typography>
                    </TableCell> */}
                    <TableCell>
                      <Typography variant="subtitle2" sx={{ fontWeight: 'bold', color: '#555' }}>
                        Message
                      </Typography>
                    </TableCell>
                  </TableRow>
                </TableHead>

                <TableBody>
                  {filteredData.slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage).map((detail) => (
                    <TableRow key={detail.id}>
                      <TableCell>{detail.rsvp_value === 'yes' ? 'Yes' : 'No'}</TableCell>
                      {/* <TableCell>{detail.ngoInfo.name}</TableCell>
                      <TableCell>{detail.campaignInfo.name}</TableCell> */}
                      <TableCell>{detail.message}</TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
            <TablePagination
              component="div"
              count={filteredData.length}
              page={page}
              onPageChange={handleChangePage}
              rowsPerPage={rowsPerPage}
              onRowsPerPageChange={handleChangeRowsPerPage}
            />
          </>
        )}
      </Box>

      <TextField
        fullWidth
        multiline
        rows={4}
        variant="outlined"
        label="Enter Message"
        value={message}
        onChange={(e) => setMessage(e.target.value)}
        sx={{ mb: 2 }}
      />

      <Button variant="contained" color="primary" onClick={handleSendMessage}>
        Send Message
      </Button>
      <ToastContainer />
    </Box>
  );
};

export default RSVPSummaryComponent;
