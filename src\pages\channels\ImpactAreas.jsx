import React, { useEffect, useState } from 'react';

// material-ui
import CardContent from '@mui/material/CardContent';
import Grid from '@mui/material/Grid';
import Typography from '@mui/material/Typography';
import CircularProgress from '@mui/material/CircularProgress';
import Alert from '@mui/material/Alert';
import Avatar from '@mui/material/Avatar';
import {
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  TextField,
  DialogActions,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Autocomplete,
  Box
} from '@mui/material';
import { EditOutlined, MessageFilled } from '@ant-design/icons';
import { toast, ToastContainer } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';

// project imports
import MainCard from 'components/MainCard';
import { getImpactAreas, updateImpactAreasService, addImpactAreasService } from './channel.service';
import useAuth from 'hooks/useAuth';

// ===========================|| DATA WIDGET - IMPACT AREAS ||=========================== //

export default function ImpactAreas() {
  const { user } = useAuth();
  const [areas, setAreas] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [openDialog, setOpenDialog] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [currentArea, setCurrentArea] = useState(null);

  useEffect(() => {
    fetchImpactAreas();
  }, []);

  const fetchImpactAreas = async () => {
    try {
      const response = await getImpactAreas(user?.ngo_id);
      setAreas(response);
      setLoading(false);
    } catch (err) {
      setError(err.message || 'Something went wrong while fetching data.');
      setLoading(false);
    }
  };

  const handleAddOrEdit = async () => {
    try {
      if (isEditing) {
        currentArea.updatedBy = user?.id;
        const response = await updateImpactAreasService(currentArea.id, currentArea);
        if (response.status === 200) {
          toast.success('Impact area updated successfully!');

          setOpenDialog(false);
          fetchImpactAreas();
        }
      } else {
        currentArea.createdBy = user?.id;
        currentArea.ngo_id = user?.ngo_id;

        const response = await addImpactAreasService(currentArea);
        if (response.status) {
          toast.success('Impact area added successfully!');
          setOpenDialog(false);
          fetchImpactAreas();
        }
      }
    } catch (error) {
      console.error('Failed to save impact area:', error);
      toast.error('Failed to save impact area.');
    }
  };

  const openEditDialog = (area) => {
    setCurrentArea(area);
    setIsEditing(true);
    setOpenDialog(true);
  };

  const openAddDialog = () => {
    setCurrentArea({
      description: '',
      position: '',
      status: 'active'
    });
    setIsEditing(false);
    setOpenDialog(true);
  };

  if (loading) {
    return (
      <MainCard title="Impact Areas">
        <CardContent>
          <Grid container justifyContent="center">
            <CircularProgress />
          </Grid>
        </CardContent>
      </MainCard>
    );
  }

  if (error) {
    return (
      <MainCard title="Impact Areas">
        <CardContent>
          <Alert severity="error">{error}</Alert>
        </CardContent>
      </MainCard>
    );
  }

  return (
    <>
      <Grid container justifyContent="flex-end" sx={{ marginBottom: 2 }}>
        <Button variant="contained" color="primary" onClick={openAddDialog}>
          Add Impact Area
        </Button>
      </Grid>
      <MainCard content={false}>
        <CardContent>
        {areas?.length > 0 ? (
          <Grid container spacing={3}>
            {areas?.map((area, index) => (
              <Grid item xs={12} key={index}>
                <Grid container spacing={2}>
                  <Grid item>
                    <Avatar sx={{ bgcolor: 'primary.main', color: 'white' }}>
                      <MessageFilled sx={{ fontSize: 24, color: 'yellow' }} />
                    </Avatar>
                  </Grid>
                  <Grid item xs zeroMinWidth>
                    <Grid container spacing={1}>
                      <Grid item xs={12}>
                        <Box display="flex" alignItems="center" gap={2}>
                          <Typography variant="subtitle1">
                            <strong>Description:</strong> {area.description}
                          </Typography>
                          <Typography variant="subtitle1">
                            <strong>Count:</strong> {area.count}
                          </Typography>
                        </Box>

                        <Typography variant="caption" color="secondary">
                          Position: {area.position} | Status: {area.status} | Created At: {new Date(area.createdAt).toLocaleString()} | Last
                          Modified: {area.updatedAt ? new Date(area.updatedAt).toLocaleString() : 'N/A'}
                        </Typography>
                      </Grid>
                      <Grid item xs={12} container justifyContent="flex-end">
                        <Button onClick={() => openEditDialog(area)}>
                          {' '}
                          <EditOutlined />
                        </Button>
                      </Grid>
                    </Grid>
                  </Grid>
                </Grid>
              </Grid>
            ))}
          </Grid>): (
    <Typography align="center" color="primary" sx={{ fontWeight: 'bold' }}>
      No Impact Areas Have Been Added Yet. Click the 'Add Impact Area' button to get started.
    </Typography>
  )}
        </CardContent>
      </MainCard>

      {/* Dialog for Add/Edit Impact Area */}
      <Dialog
        open={openDialog}
        onClose={() => setOpenDialog(false)}
        PaperProps={{
          sx: {
            borderRadius: '16px'
          }
        }}
      >
        <DialogTitle>{isEditing ? 'Edit Impact Area' : 'Add Impact Area'}</DialogTitle>
        <DialogContent>
          <Grid container spacing={2}>
            <Grid item xs={12}>
              <TextField
                label="Description"
                fullWidth
                variant="outlined"
                required
                value={currentArea?.description || ''}
                onChange={(e) => setCurrentArea({ ...currentArea, description: e.target.value })}
              />
            </Grid>
            <Grid item xs={6}>
              <TextField
                label="Position"
                fullWidth
                variant="outlined"
                required
                value={currentArea?.position || ''}
                onChange={(e) => setCurrentArea({ ...currentArea, position: e.target.value })}
              />
            </Grid>
            <Grid item xs={6}>
              <TextField
                label="Count"
                fullWidth
                variant="outlined"
                required
                value={currentArea?.count || ''}
                onChange={(e) => setCurrentArea({ ...currentArea, count: e.target.value })}
              />
            </Grid>
            <Grid item xs={6}>
              <FormControl fullWidth variant="outlined">
                <InputLabel>Status</InputLabel>
                <Select
                  value={currentArea?.status || ''}
                  onChange={(e) => setCurrentArea({ ...currentArea, status: e.target.value })}
                  required
                >
                  <MenuItem value="active">Active</MenuItem>
                  <MenuItem value="inactive">Inactive</MenuItem>
                </Select>
              </FormControl>
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setOpenDialog(false)} color="primary">
            Cancel
          </Button>
          <Button onClick={handleAddOrEdit} color="primary">
            {isEditing ? 'Update' : 'Add'}
          </Button>
        </DialogActions>
      </Dialog>

      <ToastContainer />
    </>
  );
}
