import PropTypes from 'prop-types';
import { useEffect } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';

// project import
import { APP_DEFAULT_PATH, APP_DEFAULT_PATH_NGO } from 'config';
import useAuth from 'hooks/useAuth';
import { getSessionStorageItem } from 'utils/permissionUtils';

// ==============================|| GUEST GUARD ||============================== //

export default function GuestGuard({ children }) {
  const { isLoggedIn } = useAuth();
  const user = getSessionStorageItem('user');
  const navigate = useNavigate();
//   const location = useLocation();
  useEffect(() => {
    if (isLoggedIn) {
      navigate(user?.roleInfo?.name !== 'DR_Management' ? APP_DEFAULT_PATH_NGO : APP_DEFAULT_PATH, {
        replace: true
      });
    }
  }, [isLoggedIn, navigate]);

  return children;
}

GuestGuard.propTypes = { children: PropTypes.any };
