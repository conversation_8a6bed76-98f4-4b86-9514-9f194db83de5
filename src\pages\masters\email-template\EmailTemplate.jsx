import React, { useEffect, useState } from 'react';
import axios from 'axios';
import { EditorState, ContentState } from 'draft-js';
import { Editor } from 'react-draft-wysiwyg';
import 'react-draft-wysiwyg/dist/react-draft-wysiwyg.css';
import {
  Button,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Grid,
  Container,
  Box,
  Tooltip,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  Autocomplete,
  DialogActions,
  Chip,
  CircularProgress
} from '@mui/material';
import { toast, ToastContainer } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import CustomerTable from 'sections/apps/customer/CustomerTable';
import TableActions from 'components/TableActions';
import useAuth from 'hooks/useAuth';
import { getUserPermissions } from 'utils/permissionUtils';
import { API_BASE_URL } from 'api/categories.service';
import { Link, useNavigate } from 'react-router-dom';
import { deleteEmailTemplate, getAllEmailTemplates, sendTestEmails } from './email.service';
import dayjs from 'dayjs';
import { MailOutlined } from '@ant-design/icons';

export default function EmailTemplatesTable() {
  const { user } = useAuth();
  const [templates, setTemplates] = useState([]);
  const [selectedTemplate, setSelectedTemplate] = useState([]);
  const navigate = useNavigate();
  const [openDialog, setOpenDialog] = useState(false);
  const [emailList, setEmailList] = useState([]);
  const [isSending, setIsSending] = useState(false);

  // Role-based access
  const permissions = getUserPermissions(user);
  const canAdd = permissions?.Add?.EmailTemplates || true;
  const canEdit = permissions?.Edit?.EmailTemplates || true;
  const canDelete = permissions?.Delete?.EmailTemplates || true;

  useEffect(() => {
    fetchTemplates();
  }, []);

  const fetchTemplates = async () => {
    try {
      const response = await getAllEmailTemplates();
      setTemplates(response);
    } catch (error) {
      console.error('Failed to fetch email templates:', error);
      toast.error('Failed to fetch email templates');
    }
  };

  const handleDelete = async (id) => {
    if (!canDelete) {
      toast.error('You do not have permission to delete email templates.');
      return;
    }
    const confirmDelete = window.confirm('Are you sure you want to delete this email template?');
    if (confirmDelete) {
      try {
        await deleteEmailTemplate(id);
        toast.success('Email template deleted successfully!');
        fetchTemplates();
      } catch (error) {
        console.error('Failed to delete email template:', error);
        toast.error('Failed to delete email template');
      }
    }
  };

  const navigateToAdd = () => {
    if (!canAdd) {
      toast.error('You do not have permission to add email templates.');
      return;
    }
    navigate('/masters/emails/add');
  };

  const navigateToEdit = (template) => {
    if (!canEdit) {
      toast.error('You do not have permission to edit email templates.');
      return;
    }
    navigate(`/masters/emails/edit/${template.id}`);
  };

  const handleSendEmail = async () => {
    setIsSending(true);

    try {
      const response = await sendTestEmails(selectedTemplate.id, emailList);
      if (response?.status) {
        toast.success(`Test email has been sent to ${emailList.join(', ')}. Please check the inboxes for delivery confirmation.`);
        setEmailList([]);
        setOpenDialog(false);
      }
    } catch (error) {
      console.error('Error sending test email:', error);
      toast.error('Failed to send test email.');
    } finally {
      setIsSending(false);
    }
  };

  const handleSendEmailDialog = (template) => {
    setOpenDialog(true);
    setSelectedTemplate(template);
  };

  const columns = [
    {
      accessorKey: 'title',
      header: 'Title',
      showByDefault: true,
      cell: ({ cell }) => {
        return <Link to={`/masters/emails/edit/${cell.row.original.id}`}>{cell.row.original.title}</Link>;
      }
    },
    { accessorKey: 'subject', header: 'Subject', showByDefault: true },
    { accessorKey: 'status', header: 'Status', showByDefault: true },
    {
      accessorKey: 'createdAt',
      header: 'Created At',
      showByDefault: true,
      cell: (cell) => {
        const formattedDate = dayjs(cell.row.original.createdAt).format('DD-MM-YYYY hh:mm A');
        return <span>{formattedDate}</span>;
      }
    },
    {
      accessorKey: 'updatedAt',
      header: 'Updated At',
      showByDefault: true,
      cell: (cell) => {
        const formattedDate = dayjs(cell.row.original.updatedAt).format('DD-MM-YYYY hh:mm A');
        return <span>{formattedDate}</span>;
      }
    },
    {
      id: 'actions',
      header: 'Actions',
      cell: (cell) => {
        return (
          <Box style={{ display: 'flex', gap: '8px' }}>
            <TableActions handleEditClick={() => navigateToEdit(cell.row.original)} cell={cell} handleDeleteClick={handleDelete} />
            <Tooltip title="Send Test Email">
              <IconButton color="success" onClick={() => handleSendEmailDialog(cell?.row?.original)}>
                <MailOutlined />
              </IconButton>
            </Tooltip>
          </Box>
        );
      }
    }
  ];

  return (
    <Container>
      <CustomerTable data={templates} columns={columns} modalToggler={navigateToAdd} category={'Email Template'} />
      <Dialog
        open={openDialog}
        onClose={(event, reason) => {
          if (reason !== 'backdropClick' && reason !== 'escapeKeyDown') {
            setOpenDialog(false);
          }
        }}
        maxWidth="50%"
        PaperProps={{
          sx: {
            top: 0,
            position: 'absolute',
            borderRadius: '16px',
            width: '100%',
            maxWidth: '600px'
          }
        }}
      >
        <DialogTitle>Send Test Email</DialogTitle>
        <DialogContent>
          <Autocomplete
            multiple
            freeSolo
            options={[]}
            sx={{ marginTop: 1 }}
            value={emailList}
            onChange={(event, newValue) => setEmailList(newValue)}
            renderTags={(value, getTagProps) =>
              value.map((option, index) => (
                <Chip variant="filled" color="secondary" label={option} {...getTagProps({ index })} key={index} />
              ))
            }
            renderInput={(params) => <TextField {...params} variant="outlined" label="Enter the emails" placeholder="Press Enter to add" />}
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setOpenDialog(false)} color="primary" disabled={isSending}>
            Cancel
          </Button>
          <Button onClick={handleSendEmail} color="primary" disabled={isSending}>
            {isSending ? <CircularProgress size={24} color="inherit" /> : 'Send'}
          </Button>
        </DialogActions>
      </Dialog>
      <ToastContainer />
    </Container>
  );
}
