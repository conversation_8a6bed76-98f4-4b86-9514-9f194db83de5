import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router';
import { useFormik } from 'formik';
import * as Yup from 'yup';
import { Alert, Box, Button, Card, CardContent, CircularProgress, Grid, Stack, TextField, Typography } from '@mui/material';
import { ToastContainer, toast } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import { updateBankDetails, getAllBankDetails, addBankDetails } from './tabs.service';
import useAuth from 'hooks/useAuth';
import UnsavedChangesDialog from './UnsavedChangesDialog';

export default function TabBankDetails() {
  const { newNgoId } = useParams();
  const { user } = useAuth();
  const navigate = useNavigate();
  const [isLoading, setIsLoading] = useState(true);
  const [bankDetails, setBankDetails] = useState(null);

  //trace changes
  const [isValuesChanged, setValuesChanged] = useState(false);
  const [showUnsavedDialog, setShowUnsavedDialog] = useState(false);
  const [pendingNavigationPath, setPendingNavigationPath] = useState(null);

  useEffect(() => {
    fetchBankDetails();
  }, []);

  const fetchBankDetails = async () => {
    setIsLoading(true);
    try {
      const response = await getAllBankDetails(user?.ngo_id || newNgoId);
      if (response && response.length > 0) {
        setBankDetails(response[0]); // Assume only one bank detail exists
      }
    } catch (error) {
      console.error('Error fetching bank details:', error);
      toast.error('Failed to fetch bank details');
    } finally {
      setIsLoading(false);
    }
  };

  const formik = useFormik({
    enableReinitialize: true,
    initialValues: {
      beneficiary_name: bankDetails?.beneficiary_name || '',
      bank_name: bankDetails?.bank_name || '',
      branch_name: bankDetails?.branch_name || '',
      branch_address: bankDetails?.branch_address || '',
      account_number: bankDetails?.account_number || '',
      confirm_account_number: bankDetails?.account_number || '', // Set default
      ifsc_code: bankDetails?.ifsc_code || ''
    },
    validationSchema: Yup.object({
      beneficiary_name: Yup.string().required('Beneficiary Name is required'),
      bank_name: Yup.string().required('Bank Name is required'),
      branch_name: Yup.string().required('Branch Name is required'),
      branch_address: Yup.string().required('Branch Address is required'),
      account_number: Yup.string()
        .matches(/^\d{9,18}$/, 'Account Number must be 9-18 digits')
        .required('Account Number is required'),
      confirm_account_number: Yup.string()
        .oneOf([Yup.ref('account_number'), null], 'Account Numbers must match')
        .required('Confirm Account Number is required'),
      ifsc_code: Yup.string()
        .matches(/^[A-Z]{4}0[A-Z0-9]{6}$/, 'Enter a valid IFSC Code (Example: HDFC0123456)')
        .required('IFSC Code is required')
    }),
    onSubmit: async (values) => {
      try {
        const payload = {
          ...values,
          ngo_id: user?.ngo_id ? user?.ngo_id : newNgoId,
          status: 'Active'
        };
        if (!bankDetails) {
          await addBankDetails(payload);
          toast.success('Bank details Added successfully');
        } else {
          await updateBankDetails(bankDetails.id, payload);
          toast.success('Bank details updated successfully');
        }
        setTimeout(() => {
          fetchBankDetails();
          setValuesChanged(false);
        }, 2000);
      } catch (error) {
        console.error('Error updating bank details:', error);
        toast.error('Failed to update bank details');
      }
    }
  });

  const handleNavigation = (path) => {
    if (isValuesChanged) {
      setPendingNavigationPath(path);
      setShowUnsavedDialog(true);
    } else {
      navigate(path);
    }
  };

  useEffect(() => {
    if (JSON.stringify(formik.values) !== JSON.stringify(formik.initialValues)) {
      setValuesChanged(true);
    }
  }, [formik.values, formik.initialValues]);

  if (isLoading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
        <CircularProgress />
        <Typography sx={{ mt: 2 }}>Loading Bank Details...</Typography>
      </Box>
    );
  }

  return (
    <Card>
      <CardContent>
        <form onSubmit={formik.handleSubmit}>
          <Grid container spacing={2}>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Beneficiary Name"
                name="beneficiary_name"
                // value={formik.values.beneficiary_name}
                // onChange={formik.handleChange}
                // onBlur={formik.handleBlur}
                required
                {...formik.getFieldProps('beneficiary_name')}
                error={formik.touched.beneficiary_name && Boolean(formik.errors.beneficiary_name)}
                helperText={formik.touched.beneficiary_name && formik.errors.beneficiary_name}
              />
            </Grid>

            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Bank Name"
                name="bank_name"
                required
                // value={formik.values.bank_name}
                // onChange={formik.handleChange}
                // onBlur={formik.handleBlur}
                {...formik.getFieldProps('bank_name')}
                error={formik.touched.bank_name && Boolean(formik.errors.bank_name)}
                helperText={formik.touched.bank_name && formik.errors.bank_name}
              />
            </Grid>

            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Branch Name"
                name="branch_name"
                required
                // value={formik.values.branch_name}
                // onChange={formik.handleChange}
                // onBlur={formik.handleBlur}
                {...formik.getFieldProps('branch_name')}
                error={formik.touched.branch_name && Boolean(formik.errors.branch_name)}
                helperText={formik.touched.branch_name && formik.errors.branch_name}
              />
            </Grid>

            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Branch Address"
                name="branch_address"
                required
                // value={formik.values.branch_address}
                // onChange={formik.handleChange}
                // onBlur={formik.handleBlur}
                {...formik.getFieldProps('branch_address')}
                error={formik.touched.branch_address && Boolean(formik.errors.branch_address)}
                helperText={formik.touched.branch_address && formik.errors.branch_address}
                autoComplete="off"
              />
            </Grid>

            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Account Number"
                name="account_number"
                type="password"
                autoComplete="off"
                required
                // value={formik.values.account_number}
                // onChange={formik.handleChange}
                // onBlur={formik.handleBlur}
                {...formik.getFieldProps('account_number')}
                error={formik.touched.account_number && Boolean(formik.errors.account_number)}
                helperText={formik.touched.account_number && formik.errors.account_number}
              />
            </Grid>

            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Confirm Account Number"
                name="confirm_account_number"
                required
                // value={formik.values.confirm_account_number}
                // onChange={formik.handleChange}
                // onBlur={formik.handleBlur}
                {...formik.getFieldProps('confirm_account_number')}
                error={formik.touched.confirm_account_number && Boolean(formik.errors.confirm_account_number)}
                helperText={formik.touched.confirm_account_number && formik.errors.confirm_account_number}
                autoComplete="off"
              />
            </Grid>

            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="IFSC Code"
                name="ifsc_code"
                required
                // value={formik.values.ifsc_code}
                // onChange={formik.handleChange}
                // onBlur={formik.handleBlur}
                {...formik.getFieldProps('ifsc_code')}
                error={formik.touched.ifsc_code && Boolean(formik.errors.ifsc_code)}
                helperText={formik.touched.ifsc_code && formik.errors.ifsc_code}
              />
            </Grid>

            {/* Update Button */}
            <Grid item xs={12}>
              <Button
                type="submit"
                variant="contained"
                color="primary"
                disabled={bankDetails?.ngoInfo?.ngo_status == 'Verified' || bankDetails?.ngoInfo?.ngo_status == 'In Review'}
              >
                Save
              </Button>
            </Grid>
          </Grid>
        </form>
        <Grid container sx={{ display: 'flex', justifyContent: 'flex-end', marginTop: 2 }}>
          <Stack spacing={2} alignItems="flex-end">
            <Button
              variant="contained"
              color="primary"
              onClick={() => handleNavigation(newNgoId ? `/masters/ngos/edit/socials/${newNgoId}` : '/apps/profiles/account/socials')}
            >
              Back
            </Button>

            <Alert severity="error">
              Note: If you click 'Next' or 'Back', any unsaved changes will be discarded. Be sure to click 'Save' first.
            </Alert>
          </Stack>
        </Grid>
      </CardContent>
      <UnsavedChangesDialog
        showUnsavedDialog={showUnsavedDialog}
        setShowUnsavedDialog={setShowUnsavedDialog}
        pendingNavigationPath={pendingNavigationPath}
      />
      <ToastContainer autoClose={6000} />
    </Card>
  );
}
