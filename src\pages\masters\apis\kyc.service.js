import axiosServices from 'utils/axios_node';

const API_BASE_URL = `${import.meta.env.VITE_APP_APPLICATION_API_URL}/api`;

export const generateLink = async (newNgoId) => {
  const response = await axiosServices.get(`${API_BASE_URL}/kyc-informations/generate-link/${newNgoId}`);
  return response?.data;
};
export const getLastestDataofNgo = async (newNgoId) => {
  const response = await axiosServices.get(`${API_BASE_URL}/kyc-informations/latest-data/${newNgoId}`);
  return response?.data;
};
