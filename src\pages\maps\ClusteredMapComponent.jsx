'use client';

import React, { useEffect, useState, useCallback } from 'react';
import { useTheme } from '@mui/material/styles';
import { LoadScript, GoogleMap, Marker, MarkerClusterer } from '@react-google-maps/api';
import MainCard from 'components/MainCard';
import { getData } from 'api/map.service';
const GOOGLE_MAPS_API_KEY = 'AIzaSyDAlmZjT27PfFPOFsUVixpv6jPPtwkRVcs';

const mapStyles = {
  light: [],
  dark: [
    { elementType: 'geometry', stylers: [{ color: '#242f3e' }] },
    { elementType: 'labels.text.stroke', stylers: [{ color: '#242f3e' }] },
    { elementType: 'labels.text.fill', stylers: [{ color: '#746855' }] },
    {
      featureType: 'administrative.locality',
      elementType: 'labels.text.fill',
      stylers: [{ color: '#d59563' }]
    },
    {
      featureType: 'poi',
      elementType: 'labels.text.fill',
      stylers: [{ color: '#d59563' }]
    },
    {
      featureType: 'poi.park',
      elementType: 'geometry',
      stylers: [{ color: '#263c3f' }]
    },
    {
      featureType: 'poi.park',
      elementType: 'labels.text.fill',
      stylers: [{ color: '#6b9a76' }]
    },
    {
      featureType: 'road',
      elementType: 'geometry',
      stylers: [{ color: '#38414e' }]
    },
    {
      featureType: 'road',
      elementType: 'geometry.stroke',
      stylers: [{ color: '#212a37' }]
    },
    {
      featureType: 'road',
      elementType: 'labels.text.fill',
      stylers: [{ color: '#9ca5b3' }]
    },
    {
      featureType: 'road.highway',
      elementType: 'geometry',
      stylers: [{ color: '#746855' }]
    },
    {
      featureType: 'road.highway',
      elementType: 'geometry.stroke',
      stylers: [{ color: '#1f2835' }]
    },
    {
      featureType: 'road.highway',
      elementType: 'labels.text.fill',
      stylers: [{ color: '#f3d19c' }]
    },
    {
      featureType: 'transit',
      elementType: 'geometry',
      stylers: [{ color: '#2f3948' }]
    },
    {
      featureType: 'transit.station',
      elementType: 'labels.text.fill',
      stylers: [{ color: '#d59563' }]
    },
    {
      featureType: 'water',
      elementType: 'geometry',
      stylers: [{ color: '#17263c' }]
    },
    {
      featureType: 'water',
      elementType: 'labels.text.fill',
      stylers: [{ color: '#515c6d' }]
    },
    {
      featureType: 'water',
      elementType: 'labels.text.stroke',
      stylers: [{ color: '#17263c' }]
    }
  ]
};

const mapContainerStyle = {
  width: '100%',
  height: '400px'
};

const center = {
  lat: 20.5937,
  lng: 78.9629
};

export default function GoogleClusteredMapComponent({ headerName, url }) {
  const theme = useTheme();
  const [markers, setMarkers] = useState([]);
  const [mapRef, setMapRef] = useState(null);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      try {
        const response = await getData(url);
        const filteredMarkers = response
          .filter((campaign) => campaign.latitude && campaign.longitude)
          .map((campaign) => ({
            lat: parseFloat(campaign.latitude),
            lng: parseFloat(campaign.longitude)
          }));
        setMarkers(filteredMarkers);
      } catch (error) {
        console.error('Error fetching campaign data:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [url]);

  const onLoad = useCallback((map) => {
    setMapRef(map);
  }, []);

  const options = {
    styles: theme.palette.mode === 'dark' ? mapStyles.dark : mapStyles.light,

    disableDefaultUI: true,
    zoomControl: true
  };

  return (
    <MainCard title={headerName}>
      <LoadScript googleMapsApiKey={GOOGLE_MAPS_API_KEY}>
        <GoogleMap mapContainerStyle={mapContainerStyle} zoom={4} center={center} options={options} onLoad={onLoad}>
          <MarkerClusterer>
            {(clusterer) => markers.map((marker, index) => <Marker key={index} position={marker} clusterer={clusterer} />)}
          </MarkerClusterer>
        </GoogleMap>
      </LoadScript>
    </MainCard>
  );
}
