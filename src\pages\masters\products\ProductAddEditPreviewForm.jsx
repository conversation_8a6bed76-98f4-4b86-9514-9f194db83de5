import React, { Fragment, useState } from 'react';
import { Box, Tabs, Tab, Divider } from '@mui/material';
import AddEditProduct from './ProductForm';
import ProductPreview from '../campaigns/components/ProductPreview';
import MainCard from 'components/MainCard';
import ImageComponent from '../components/ImageComponent';
import { useParams } from 'react-router';

const ProductTabs = ({ isEditing }) => {
  const [currentTab, setCurrentTab] = useState(0);
  const { productId } = useParams();

  const handleTabChange = (event, newValue) => {
    setCurrentTab(newValue);
  };

  return (
    <Fragment>
      <MainCard>
        <Tabs value={currentTab} onChange={handleTabChange} aria-label="product-tabs">
          <Tab label="Preview" />
          <Tab label="Product form" />

          {isEditing && <Tab label="Product images" />}
        </Tabs>
        <Divider />
        <Box sx={{ mt: 2 }}>
          {currentTab === 0 && (
            <Box>
              <ProductPreview />
            </Box>
          )}
          {currentTab === 1 && (
            <Box>
              <AddEditProduct isEditing={isEditing} />
            </Box>
          )}
          {currentTab === 2 && (
            <Box>
              <ImageComponent type={'product'} id={productId} />
            </Box>
          )}
        </Box>
      </MainCard>
    </Fragment>
  );
};

export default ProductTabs;
