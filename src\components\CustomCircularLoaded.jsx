
import React from 'react';
import { Backdrop } from '@mui/material';

const CustomCircularLoaded = ({ open, message = 'Loading...' }) => {
  return (
    <Backdrop open={open} sx={{ zIndex: 1300, color: '#FFAE5F', display: 'flex', flexDirection: 'column', alignItems: 'center', justifyContent: 'center' }}>
      <div className="bouncing-balls">
        <div className="ball"></div>
        <div className="ball"></div>
        <div className="ball"></div>
        <div className="ball"></div>
      </div>
      <p style={{ marginTop: '16px' }}>{message}</p>
    </Backdrop>
  );
};

export default CustomCircularLoaded;
