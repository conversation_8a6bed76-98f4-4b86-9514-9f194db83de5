import { useEffect, useState } from 'react';
import { useSearchParams } from 'react-router-dom';
import { CircularProgress, Container, Paper, Typography } from '@mui/material';
import { ToastContainer, toast } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import CustomCircularLoaded from 'components/CustomCircularLoaded';
import { verifyEmail } from 'sections/apps/profiles/account/tabs.service';

const VerifyEmail = () => {
  const [searchParams] = useSearchParams();
  const token = searchParams.get('token');
  const email = searchParams.get('email');
  const [loading, setLoading] = useState(true);
  const [message, setMessage] = useState('');

  useEffect(() => {
    const VerifyUserEmail = async () => {
      if (!token || !email) {
        setMessage('Invalid verification link.');
        setLoading(false);
        return;
      }

      try {
        const response = await verifyEmail(token, email); // Call API
        if (response.status) {
          setMessage(
            'Your email has been verified. You can close this page and go back to the previous page and refresh it. Alternatively, you can navigate to your profile from here.'
          );
          toast.success('Email verified successfully!');
        } else {
          setMessage('Verification failed. Please try again later.');
          toast.error('Verification failed.');
        }
      } catch (error) {
        setMessage('Something went wrong. Please try again.');
        toast.error('Error verifying email.');
      } finally {
        setLoading(false);
      }
    };

    VerifyUserEmail();
  }, [token, email]);

  return (
    <Container style={{ textAlign: 'center', marginTop: '50px' }}>
      {loading ? (
        <CustomCircularLoaded open={loading} message="We are in the process of verifying your email. Kindly wait a moment" />
      ) : (
        <Paper
          elevation={3}
          sx={{
            padding: '30px',
            backgroundColor: '#e8f5e9',
            borderRadius: '8px',
            color: '#2e7d32',
            display: 'inline-block',
            minWidth: '300px',
            textAlign: 'center'
          }}
        >
          <Typography variant="h6" sx={{ fontWeight: 'bold', marginBottom: '15px' }}>
            ✅ Email Verified Successfully!
          </Typography>
          <Typography variant="body1" sx={{ fontSize: '16px' }}>
            {message}
          </Typography>
        </Paper>
      )}
      <ToastContainer autoClose={6000} />
    </Container>
  );
};

export default VerifyEmail;
