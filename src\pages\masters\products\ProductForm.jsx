import React, { useState, useEffect, useCallback } from 'react';
import { Formik, Form } from 'formik';
import { ToastContainer, toast } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import * as Yup from 'yup';
import {
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Grid,
  Button,
  Autocomplete,
  Stack,
  Typography,
  FormHelperText,
  Box,
  CircularProgress
} from '@mui/material';
import UploadSingleFile from 'components/third-party/dropzone/SingleFile';
import { addProducts, editProducts, getCollections, getSingleProduct } from './product.service';
import { calculateProfileCompletion, getConvertedFileName } from 'utils/permissionUtils';
import useAuth from 'hooks/useAuth';
import { useNavigate, useParams } from 'react-router';
import MainCard from 'components/MainCard';
import { UNIT_TYPES } from 'utils/statusconstans';
import { getNGOSBySearchTerm } from 'api/campaigns.service';
import { getNgoById } from 'api/ngos.service';
import ProfileCard from 'sections/apps/profiles/user/ProfileCard';

const ProductSchema = Yup.object().shape({
  name: Yup.string().required('Name is required'),
  description: Yup.string().required('Description is required'),
  isStock: Yup.string().required('Stock status is required'),

  discountedPrice: Yup.number().min(0, 'Discounted price must be at least 0').nullable().notRequired(),
  discount: Yup.number().min(0, 'Discount must be at least 0').max(100, 'Discount cannot exceed 100').nullable().notRequired(),

  imageName: Yup.string(),
  count: Yup.number().min(1).required('Count is required'),
  price: Yup.number().min(0).required('Price is required').min(10, 'Price must be at least 10')
});

const AddEditProduct = ({ isEditing }) => {
  const { user } = useAuth();
  const { productId } = useParams();
  const navigate = useNavigate();
  const [selectedNgo, setSelectedNgo] = useState(null);

  const [collections, setCollections] = useState([]);
  // const [searchTerm, setSearchTerm] = useState('');
  // const [ngoList, setNgoList] = useState([]);
  const [loading, setLoading] = useState(false);
  const [isNgoloading, setisNgoLoading] = useState(false);
  const [ngoList, setNgoList] = useState([]);
  const [initialValues, setInitialValues] = useState({
    name: '',
    description: '',
    price: '',
    discount: 0,
    discountedPrice: 0,
    isStock: '',
    count: 1,
    unit_of_measure: '',
    ngo_id: null,
    files: null
  });

  const [profileCompletePercentage, setProfileCompletePercentage] = useState(0);
  const [ngoInfo, setNgoInfo] = useState(0);

  useEffect(() => {
    fetchCollections();
    if (isEditing && productId) {
      fetchProductDetails(productId);
    }
  }, [isEditing, productId]);

  const fetchCollections = async () => {
    try {
      const response = await getCollections();
      setCollections(response);
    } catch (error) {
      toast.error(error?.message || 'Failed to fetch collections');
    }
  };

  const fetchProductDetails = async (id) => {
    setLoading(true);
    try {
      const product = await getSingleProduct(id);
      setInitialValues({
        ...product
      });
    } catch (error) {
      toast.error(error?.message || 'Failed to fetch product details');
    } finally {
      setLoading(false);
    }
  };

  const handleSubmit = async (values) => {
    let formData = new FormData();

    if (values.files && values.files.length > 0) {
      const convertedFileName = getConvertedFileName(values.files[0].name);
      if (isEditing) {
        delete values.imageName;
      }
      formData.append('imageName', convertedFileName);
      formData.append('file', values.files[0], convertedFileName);
    }

    Object.keys(values).forEach((key) => {
      if (key !== 'files' && values[key]) {
        formData.append(key, values[key]);
      }
    });

    try {
      if (isEditing) {
        await editProducts(values.id, formData);
        toast.success('Product updated successfully!');
      } else {
        if (user?.ngo_id) {
          formData.append('ngo_id', user?.ngo_id);
        }

        await addProducts(formData);
        toast.success('Product added successfully!');
      }
      setTimeout(() => {
        navigate('/masters/products');
      }, 3000);
    } catch (error) {
      toast.error(error?.message || 'Failed to save product');
    }
  };
  const [searchTerm, setSearchTerm] = useState('');

  const fetchNgos = useCallback(async (query) => {
    if (query.length < 4) return;
    setisNgoLoading(true);
    try {
      const response = await getNGOSBySearchTerm(query, 'Verified');
      setNgoList(response);
    } catch (error) {
      console.error('Error fetching NGOs:', error);
    } finally {
      setisNgoLoading(false);
    }
  }, []);

  useEffect(() => {
    const debounceFetch = setTimeout(fetchNgos(searchTerm), 500);
    return () => clearTimeout(debounceFetch);
  }, [searchTerm]);

  useEffect(() => {
    const fetchNgoDataAndCalculate = async () => {
      if (user?.ngo_id) {
        try {
          const ngoRecords = await getNgoById(user?.ngo_id);
          setNgoInfo(ngoRecords);
          const profilepercentage = await calculateProfileCompletion(ngoRecords);
          setProfileCompletePercentage(profilepercentage);
        } catch (error) {
          console.error('Error fetching NGO data:', error);
        }
      } else {
        setProfileCompletePercentage(100);
      }
    };

    fetchNgoDataAndCalculate();
  }, [user?.ngo_id]);

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" height="100%">
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box>
      {user?.ngo_id && ngoInfo && profileCompletePercentage < 100 && (
        <Grid item xs={12} sx={{ marginBottom: 2 }}>
          <ProfileCard ngoInfo={ngoInfo} profileCompletePercentage={profileCompletePercentage} />
        </Grid>
      )}
      <MainCard
        title={<Typography sx={{ fontSize: '1rem', fontWeight: 'bold' }}>{isEditing ? 'Update Product' : 'Add Product'}</Typography>}
      >
        <Formik
          enableReinitialize
          initialValues={initialValues}
          validationSchema={ProductSchema}
          onSubmit={async (values) => {
            await handleSubmit(values);
          }}
        >
          {({ values, handleChange, setFieldValue, errors, touched, isSubmitting }) => {
            useEffect(() => {
              if (values.price && values.discount) {
                const discountValue = (values.price * values.discount) / 100;
                const calculatedDiscountedPrice = values.price - discountValue;
                setFieldValue('discountedPrice', calculatedDiscountedPrice.toFixed(2));
              } else {
                setFieldValue('discountedPrice', null);
              }
            }, [values.price, values.discount, setFieldValue]);
            useEffect(() => {
              const fetchNgo = async () => {
                if (values.ngo_id && !ngoList.find((ngo) => ngo.id === values.ngo_id)) {
                  try {
                    const ngo = await getNgoById(values.ngo_id);

                    setSelectedNgo(ngo);
                  } catch (error) {
                    console.error(error);
                  }
                }
              };
              fetchNgo();
            }, [values, ngoList, initialValues.ngo_id]);
            return (
              <Form>
                <Grid container spacing={3} padding={3}>
                  {!user?.ngo_id && (
                    <Grid item xs={12} sm={6}>
                      <Stack spacing={1}>
                        <Autocomplete
                          options={ngoList}
                          getOptionLabel={(option) => option.name}
                          value={ngoList.find((ngo) => ngo.id === values.ngo_id) || selectedNgo || null}
                          onInputChange={(event, newInputValue) => {
                            setSearchTerm(newInputValue);
                          }}
                          onChange={(event, newValue) => setFieldValue('ngo_id', newValue?.id || null)}
                          loading={isNgoloading} // Show the loading state when data is being fetched
                          renderInput={(params) => (
                            <TextField
                              {...params}
                              label="Search NGO"
                              error={touched.ngo_id && Boolean(errors.ngo_id)}
                              helperText={touched.ngo_id && errors.ngo_id}
                              required
                              InputProps={{
                                ...params.InputProps,
                                endAdornment: (
                                  <>
                                    {isNgoloading ? <CircularProgress color="primary" size={20} /> : null}
                                    {params.InputProps.endAdornment}
                                  </>
                                )
                              }}
                            />
                          )}
                        />
                      </Stack>
                    </Grid>
                  )}

                  <Grid item xs={12} sm={6}>
                    <TextField
                      fullWidth
                      name="name"
                      label="Name"
                      required
                      value={values.name || ''}
                      onChange={handleChange}
                      error={touched.name && !!errors.name}
                      helperText={touched.name && errors.name}
                    />
                  </Grid>

                  <Grid item xs={12} sm={6}>
                    <TextField
                      fullWidth
                      name="description"
                      label="Description"
                      required
                      value={values.description || ''}
                      onChange={handleChange}
                      error={touched.description && !!errors.description}
                      helperText={touched.description && errors.description}
                      multiline
                      rows={3}
                      style={{
                        backgroundColor: 'white'
                      }}
                    />
                  </Grid>

                  <Grid item xs={12} sm={6}>
                    <TextField
                      fullWidth
                      name="price"
                      label="Price"
                      required
                      type="number"
                      value={values.price || ''}
                      onChange={handleChange}
                      error={touched.price && !!errors.price}
                      helperText={touched.price && errors.price}
                      inputProps={{ min: 1 }}
                    />
                  </Grid>

                  <Grid item xs={12} sm={6}>
                    <TextField
                      fullWidth
                      name="discount"
                      label="Discount (%)"
                      type="number"
                      inputProps={{ min: 0, max: 100 }}
                      value={values.discount || ''}
                      onChange={handleChange}
                      error={touched.discount && !!errors.discount}
                      helperText={touched.discount && errors.discount}
                    />
                  </Grid>

                  <Grid item xs={12} sm={6}>
                    <TextField
                      fullWidth
                      name="discountedPrice"
                      label="Discounted Price"
                      type="number"
                      inputProps={{ min: 1 }}
                      value={values.discountedPrice || ''}
                      onChange={handleChange}
                      error={touched.discountedPrice && !!errors.discountedPrice}
                      helperText={touched.discountedPrice && errors.discountedPrice}
                      InputProps={{
                        readOnly: true
                      }}
                    />
                  </Grid>

                  <Grid item xs={12} sm={6}>
                    <FormControl fullWidth error={touched.count && !!errors.count}>
                      <InputLabel>Count</InputLabel>
                      <Select name="count" value={values.count || ''} onChange={handleChange}>
                        {Array.from({ length: 100 }, (_, index) => index + 1).map((count) => (
                          <MenuItem key={count} value={count}>
                            {count}
                          </MenuItem>
                        ))}
                      </Select>
                      {touched.count && errors.count && <FormHelperText>{errors.count}</FormHelperText>}
                    </FormControl>
                  </Grid>

                  <Grid item xs={12} sm={6}>
                    <FormControl fullWidth error={touched.unit_of_measure && !!errors.unit_of_measure}>
                      <InputLabel>Unit of measure</InputLabel>
                      <Select name="unit_of_measure" value={values.unit_of_measure || ''} onChange={handleChange}>
                        {UNIT_TYPES.map((unit_of_measure) => (
                          <MenuItem key={unit_of_measure} value={unit_of_measure}>
                            {unit_of_measure}
                          </MenuItem>
                        ))}
                      </Select>
                      {touched.unit && errors.unit && <FormHelperText>{errors.unit}</FormHelperText>}
                    </FormControl>
                  </Grid>

                  <Grid item xs={12} sm={6}>
                    <Autocomplete
                      fullWidth
                      options={collections}
                      getOptionLabel={(option) => option.name || ''}
                      value={collections.find((option) => option.id === values.collection_id) || null}
                      onChange={(event, newValue) => {
                        setFieldValue('collection_id', newValue ? newValue.id : null);
                      }}
                      renderInput={(params) => (
                        <TextField
                          {...params}
                          name="collection_id"
                          label="Collection"
                          error={touched.collection_id && !!errors.collection_id}
                          helperText={touched.collection_id && errors.collection_id}
                        />
                      )}
                    />
                  </Grid>

                  <Grid item xs={12} sm={6}>
                    <FormControl fullWidth>
                      <InputLabel>In Stock</InputLabel>
                      <Select
                        name="isStock"
                        value={values.isStock || ''}
                        onChange={handleChange}
                        error={touched.isStock && !!errors.isStock}
                        // displayEmpty
                      >
                        <MenuItem value="" disabled>
                          <em>Choose an option</em>
                        </MenuItem>
                        <MenuItem value="Yes">Yes</MenuItem>
                        <MenuItem value="No">No</MenuItem>
                      </Select>
                    </FormControl>
                  </Grid>

                  {/* <Grid item xs={12}>
                    <Stack spacing={1.5} alignItems="center">
                      <UploadSingleFile setFieldValue={setFieldValue} file={values.files} error={touched.files && !!errors.files} />
                      <Stack spacing={0}>
                        <Typography align="center" variant="caption" color="secondary">
                          *.png, *.jpeg, *.jpg, *.gif
                        </Typography>
                      </Stack>
                    </Stack>
                    {touched.files && errors.files && <FormHelperText error>{errors.files}</FormHelperText>}
                  </Grid> */}
                  <Grid item xs={12}>
                    <Stack spacing={1.5} alignItems="left">
                      <Typography variant="h5" color="textPrimary" align="left">
                        Upload Product Image
                      </Typography>
                      <UploadSingleFile setFieldValue={setFieldValue} file={values.files} error={touched.files && !!errors.files} />
                    </Stack>
                    {touched.files && errors.files && <FormHelperText error>{errors.files}</FormHelperText>}
                  </Grid>

                  <Grid item xs={12} sm={6}></Grid>
                  <Grid item xs={12} sm={6} sx={{ display: 'flex', justifyContent: 'flex-end' }}>
                    <Button variant="outlined" type="button" style={{ marginRight: '8px' }} onClick={() => window.history.back()}>
                      Cancel
                    </Button>
                    <Button variant="contained" type="submit" disabled={isSubmitting || (user?.ngo_id && profileCompletePercentage < 100)}>
                      {isEditing ? 'Update Product' : 'Add Product'}
                    </Button>
                  </Grid>
                </Grid>
              </Form>
            );
          }}
        </Formik>
      </MainCard>
      <ToastContainer />
    </Box>
  );
};

export default AddEditProduct;
