import React, { useState } from 'react';
import { Box, TextField, IconButton, MenuItem, Typography, Button, Grid, Stack } from '@mui/material';
import Autocomplete from '@mui/material/Autocomplete';
import { MinusCircleOutlined, PlusCircleOutlined } from '@ant-design/icons';

const unitOptions = ['Kg', 'Litre', 'Pieces', 'Box']; // Options for Unit of Measure dropdown

const KindDynamicInputList = ({ inputList, setInputList, itemOptions }) => {
  // Add a new input field
  const handleAddInput = () => {
    setInputList([...inputList, { item: null, quantity: '', unit: '' }]);
  };

  // Remove an input field by index
  const handleRemoveInput = (index) => {
    const list = [...inputList];
    list.splice(index, 1);
    setInputList(list);
  };

  // Handle changes in any input field
  const handleInputChange = (index, field, value) => {
    const list = [...inputList];
    list[index][field] = value;
    setInputList(list);
  };
  return (
    <Grid item xs={12} sm={12}>
      <Stack spacing={1}>
        <Typography variant="h5" gutterBottom style={{ marginBottom: '1rem' }}>
          In-Kind Donation Requirements *
        </Typography>
        {inputList.map((input, index) => (
          <Box
            key={index}
            sx={{
              display: 'flex',
              alignItems: 'center',
              gap: 2,
              mb: 2
            }}
          >
            {/* Autocomplete for Item List */}
            <Autocomplete
              fullWidth
              options={itemOptions}
              getOptionLabel={(option) => option.name || ''}
              value={itemOptions.find((option) => option.name === input.item) || null} // Ensure value matches an option
              isOptionEqualToValue={(option, value) => option.name === value.name} // Customize equality check
              // onChange={(e, newValue) => {
              //   handleInputChange(index, 'item', newValue ? newValue.name : ''); // Handle null gracefully
              // }}
              onChange={(e, newValue) => {
                if (typeof newValue === 'string') {
                  handleInputChange(index, 'item', newValue);
                } else if (newValue && newValue.name) {
                  handleInputChange(index, 'item', newValue.name);
                } else {
                  handleInputChange(index, 'item', '');
                }
              }}
              onInputChange={(e, newInputValue) => {
                handleInputChange(index, 'item', newInputValue);
              }}
              freeSolo
              renderInput={(params) => <TextField {...params} required label="Item List" fullWidth />}
            />

            {/* Quantity Input */}
            <TextField
              label="Quantity"
              type="number"
              required
              value={input.quantity}
              onChange={(e) => handleInputChange(index, 'quantity', e.target.value)}
              fullWidth
            />

            {/* Dropdown for Unit of Measure */}
            <TextField
              select
              label="Unit of Measure"
              required
              value={input.unit}
              onChange={(e) => handleInputChange(index, 'unit', e.target.value)}
              fullWidth
            >
              {unitOptions.map((option) => (
                <MenuItem key={option} value={option}>
                  {option}
                </MenuItem>
              ))}
            </TextField>

            {/* Add/Remove Buttons */}
            <IconButton color="primary" onClick={handleAddInput}>
              <PlusCircleOutlined />
            </IconButton>
            {inputList.length > 1 && (
              <IconButton color="secondary" onClick={() => handleRemoveInput(index)}>
                <MinusCircleOutlined />
              </IconButton>
            )}
          </Box>
        ))}
        {/* Display Data (Optional) */}
      </Stack>
    </Grid>
  );
};

export default KindDynamicInputList;
