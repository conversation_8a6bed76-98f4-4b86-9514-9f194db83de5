import React, { useEffect, useState } from 'react';
import {
  Button,
  <PERSON>alog,
  <PERSON>alogA<PERSON>,
  <PERSON>alogContent,
  DialogTitle,
  TextField,
  Select,
  MenuItem,
  FormControl,
  InputLabel
} from '@mui/material';
import { toast, ToastContainer } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import CustomerTableGrouping from 'sections/apps/customer/CustomerTableGrouping';
import TableActions from 'components/TableActions';
import useAuth from 'hooks/useAuth';
import { getSessionStorageItem, getUserPermissions } from 'utils/permissionUtils';
import { getQuestionsList, insertQuestion, updateQuestion, deleteQuestion } from 'api/questions.service';
import { getAllNgoTypes } from 'api/ngotypes.service';
import CustomerTable from 'sections/apps/customer/CustomerTable';
import dayjs from 'dayjs';

export default function QuestionsTable() {
  const { user } = useAuth();
  const [questions, setQuestions] = useState([]);
  const [openDialog, setOpenDialog] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [currentQuestion, setCurrentQuestion] = useState(null);
  const currentUser = getSessionStorageItem('user');
  const [ngoTypes, setNgoTypes] = useState([]);

  const permissions = getUserPermissions(user);
  const canAdd = permissions?.Add?.Question || true;
  const canEdit = permissions?.Edit?.Question || true;
  const canDelete = permissions?.Delete?.Question || true;

  useEffect(() => {
    fetchQuestions();
    fetchNgoTypes();
  }, []);

  const fetchQuestions = async () => {
    try {
      const response = await getQuestionsList();
      setQuestions(response);
    } catch (error) {
      console.error('Failed to fetch questions:', error);
      toast.error('Failed to fetch questions');
    }
  };

  const fetchNgoTypes = async () => {
    try {
      const response = await getAllNgoTypes();
      setNgoTypes(response);
    } catch (error) {
      console.error('Failed to fetch NGO types:', error);
      toast.error('Failed to fetch NGO types');
    }
  };

  const handleAddOrEdit = async () => {
    try {
      if (isEditing) {
        const updatedQuestion = { ...currentQuestion, updatedBy: currentUser.id };
        await updateQuestion(currentQuestion.id, updatedQuestion);
        toast.success('Question updated successfully!');
      } else {
        const newQuestion = { ...currentQuestion, createdBy: currentUser.id };
        await insertQuestion(newQuestion);
        toast.success('Question added successfully!');
      }
      setOpenDialog(false);
      fetchQuestions();
    } catch (error) {
      console.error('Failed to save question:', error);
      toast.error('Failed to save question');
    }
  };

  const handleDelete = async (id) => {
    if (!canDelete) {
      toast.error('You do not have permission to delete Questions.');
      return;
    }
    const confirmDelete = window.confirm('Are you sure you want to delete this question?');
    if (confirmDelete) {
      try {
        await deleteQuestion(id);
        toast.success('Question deleted successfully!');
        fetchQuestions();
      } catch (error) {
        console.error('Failed to delete question:', error);
        toast.error('Failed to delete question');
      }
    }
  };

  const openAddDialog = () => {
    if (!canAdd) {
      toast.error('You do not have permission to add Questions.');
      return;
    }
    setCurrentQuestion({ name: '', description: '', ngo_type: '', mandatory: '' });
    setIsEditing(false);
    setOpenDialog(true);
  };

  const openEditDialog = (question) => {
    if (!canEdit) {
      toast.error('You do not have permission to edit Questions.');
      return;
    }
    setCurrentQuestion(question);
    setIsEditing(true);
    setOpenDialog(true);
  };

  const columns = [
    { accessorKey: 'name', header: 'Name', showByDefault: true },
    { accessorKey: 'description', header: 'Description', showByDefault: true },
    { accessorKey: 'ngo_type', header: 'NGO Type', showByDefault: true },
    { accessorKey: 'mandatory', header: 'Mandatory', showByDefault: true },
    { accessorKey: 'status', header: 'Status', showByDefault: true },
    {
      accessorKey: 'createdAt',
      header: 'Created At',
      showByDefault: true,
      cell: (cell) => {
        const formattedDate = dayjs(cell.row.original.createdAt).format('DD-MM-YYYY hh:mm A');
        return <span>{formattedDate}</span>;
      }
    },
    {
      accessorKey: 'updatedAt',
      header: 'Updated At',
      showByDefault: true,
      cell: (cell) => {
        const formattedDate = dayjs(cell.row.original.updatedAt).format('DD-MM-YYYY hh:mm A');
        return <span>{formattedDate}</span>;
      }
    },
    {
      id: 'actions',
      header: 'Actions',
      cell: (cell) => {
        return <TableActions handleEditClick={openEditDialog} cell={cell} handleDeleteClick={handleDelete} />;
      }
    }
  ];

  return (
    <div>
      <CustomerTable data={questions} columns={columns} modalToggler={openAddDialog} category="Question" />
      <Dialog
        open={openDialog}
        onClose={(event, reason) => {
          if (reason !== 'backdropClick' && reason !== 'escapeKeyDown') {
            setOpenDialog(false);
          }
        }}
        maxWidth="50%"
        PaperProps={{
          sx: {
            top: 0,
            position: 'absolute',
            borderRadius: '16px',
            width: '100%',
            maxWidth: '600px'
          }
        }}
      >
        <DialogTitle>{isEditing ? 'Edit Question' : 'Add Question'}</DialogTitle>
        <DialogContent>
          <TextField
            autoFocus
            margin="dense"
            label="Name"
            type="text"
            fullWidth
            variant="outlined"
            value={currentQuestion?.name || ''}
            onChange={(e) => setCurrentQuestion({ ...currentQuestion, name: e.target.value })}
          />
          <TextField
            margin="dense"
            label="Description"
            type="text"
            fullWidth
            variant="outlined"
            value={currentQuestion?.description || ''}
            onChange={(e) => setCurrentQuestion({ ...currentQuestion, description: e.target.value })}
          />

          <FormControl fullWidth margin="dense" variant="outlined">
            <InputLabel>NGO Type</InputLabel>
            <Select
              multiple
              value={currentQuestion?.ngo_type?.split(',').filter((item) => item.trim() !== '') || []}
              onChange={(e) => {
                setCurrentQuestion({
                  ...currentQuestion,
                  ngo_type: e.target.value.length > 0 ? e.target.value.join(',') : ''
                });
              }}
              label="NGO Type"
              renderValue={(selected) => selected.join(', ')}
            >
              {ngoTypes &&
                ngoTypes.map((ngotype) => (
                  <MenuItem key={ngotype?.id} value={ngotype?.name}>
                    {ngotype?.name}
                  </MenuItem>
                ))}
            </Select>
          </FormControl>

          <FormControl fullWidth margin="dense" variant="outlined">
            <InputLabel>Mandatory</InputLabel>
            <Select
              value={currentQuestion?.mandatory || ''}
              onChange={(e) => setCurrentQuestion({ ...currentQuestion, mandatory: e.target.value })}
              label="Mandatory"
            >
              <MenuItem value="yes">Yes</MenuItem>
              <MenuItem value="no">No</MenuItem>
            </Select>
          </FormControl>
          <FormControl fullWidth margin="dense" variant="outlined">
            <InputLabel>Status</InputLabel>
            <Select
              value={currentQuestion?.status || 'Active'}
              onChange={(e) => setCurrentQuestion({ ...currentQuestion, status: e.target.value })}
              label="Status"
            >
              <MenuItem value="Active">Active</MenuItem>
              <MenuItem value="Inactive">Inactive</MenuItem>
            </Select>
          </FormControl>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setOpenDialog(false)} color="primary">
            Cancel
          </Button>
          <Button onClick={handleAddOrEdit} color="primary">
            {isEditing ? 'Update' : 'Add'}
          </Button>
        </DialogActions>
      </Dialog>
      <ToastContainer />
    </div>
  );
}
