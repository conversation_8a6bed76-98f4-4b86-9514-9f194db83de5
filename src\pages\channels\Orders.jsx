import React, { useEffect, useState } from 'react';
import {
  Button,
  <PERSON>alog,
  <PERSON>alogA<PERSON>,
  DialogContent,
  DialogTitle,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Grid,
  Box,
  Tooltip,
  IconButton
} from '@mui/material';
import { toast, ToastContainer } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import CustomerTable from 'sections/apps/customer/CustomerTable';
import { Formik } from 'formik';
import * as yup from 'yup';
import { fetchOrdersService, addOrderService, updateOrderService, deleteOrderService, patchOrderService } from './orders.service'; // Replace with actual API services
import dayjs from 'dayjs';
import TableActions from 'components/TableActions';
import useAuth from 'hooks/useAuth';
import { DashboardOutlined } from '@ant-design/icons';
import { ORDER_TYPES, PAYMENT_STATUS } from 'utils/statusconstans';

export default function OrdersTable() {
  const { user } = useAuth();
  const [orders, setOrders] = useState([]);
  const [openDialog, setOpenDialog] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [statusDialogOpen, setStatusDialogOpen] = useState(false);
  const [selectedStatus, setSelectedStatus] = useState('');
  const [selectedOrder, setSelectedOrder] = useState('');
  const [selectedPaymentStatus, setSelectedPaymentStatus] = useState('');

  const [currentOrder, setCurrentOrder] = useState({
    product_id: '',
    user_id: '',
    ngo_id: '',
    quantity: '',
    price_per_unit: '',
    total_price: '',
    order_status: '',
    payment_status: '',
    shipping_address: '',
    billing_address: '',
    shipping_cost: '',
    discount_applied: '',
    remarks: ''
  });

  useEffect(() => {
    fetchOrders();
  }, []);

  const fetchOrders = async () => {
    try {
      const params = {};
      if (user?.ngo_id) {
        params.ngo_id = user?.ngo_id;
      }
      const data = await fetchOrdersService(params);
      setOrders(data);
    } catch (error) {
      console.error('Failed to fetch orders:', error);
      toast.error('Failed to fetch orders');
    }
  };

  const handleAddOrEdit = async (values) => {
    try {
      if (isEditing) {
        if (true) {
          toast.error('You are not allowed edit orders');
          return;
        }
        await updateOrderService(values.id, values);
        toast.success('Order updated successfully!');
      } else {
        await addOrderService(values);
        toast.success('Order added successfully!');
      }
      setOpenDialog(false);
      fetchOrders();
    } catch (error) {
      console.error('Failed to save order:', error);
      toast.error(error.response?.data?.message || 'Failed to save order');
    }
  };

  const handleDelete = async (id) => {
    if (true) {
      toast.error('You are not allowed delete orders');
      return;
    }
    const confirmDelete = window.confirm('Are you sure you want to delete this order?');
    if (confirmDelete) {
      try {
        await deleteOrderService(id);
        toast.success('Order deleted successfully!');
        fetchOrders();
      } catch (error) {
        console.error('Failed to delete order:', error);
        toast.error('Failed to delete order');
      }
    }
  };

  const openAddDialog = () => {
    setCurrentOrder({
      product_id: '',
      user_id: '',
      ngo_id: '',
      quantity: '',
      price_per_unit: '',
      total_price: '',
      order_status: '',
      payment_status: '',
      shipping_address: '',
      billing_address: '',
      shipping_cost: '',
      discount_applied: '',
      remarks: ''
    });
    setOpenDialog(true);
    setIsEditing(false);
  };

  const openEditDialog = (order) => {
    if (true) {
      toast.error('You are not allowed edit orders');
      return;
    }
    setCurrentOrder(order);
    setIsEditing(true);
    setOpenDialog(true);
  };

  const handleOpenStatusDialog = (id) => {
    setSelectedStatus('');
    setSelectedPaymentStatus('');
    setStatusDialogOpen(true);
  };

  const handleUpdateStatus = async (id, status, paymentStatus) => {
    try {
      const response = await patchOrderService(id, { order_status: status, payment_status: paymentStatus });
      if (response?.status == 200) {
        toast.success('Order status updated successfully');
        fetchOrders();
      } else {
        toast.error('Failed to update order status!');
      }
    } catch (error) {
      console.error('Error updating status', error?.message);
      toast.error('Something went wrong while updating status');
    }
  };

  const columns = [
    {
      accessorKey: 'product_id',
      header: 'Product name',
      showByDefault: true,
      cell: ({ cell }) => {
        return <span>{cell.row.original.productInfo?.name || 'N/A'}</span>;
      }
    },
    {
      accessorKey: 'user_id',
      header: 'User name',
      showByDefault: true,
      cell: ({ cell }) => {
        return <span>{cell.row.original.userInfo?.fullname || 'N/A'}</span>;
      }
    },
    ...(user?.roleInfo?.name.startsWith('DR')
      ? [
          {
            accessorKey: 'ngoInfo.name',
            header: 'NGO Name',
            showByDefault: true,
            cell: (cell) => {
              const ngoName = cell.row.original.ngoInfo?.name || 'N/A';
              return <span>{ngoName}</span>;
            }
          }
        ]
      : []),
    { accessorKey: 'quantity', header: 'Quantity', showByDefault: true },
    { accessorKey: 'price_per_unit', header: 'Price per Unit', showByDefault: true },
    { accessorKey: 'total_price', header: 'Total Price', showByDefault: true },
    { accessorKey: 'order_status', header: 'Order Status', showByDefault: true },
    { accessorKey: 'payment_status', header: 'Payment Status', showByDefault: true },
    { accessorKey: 'shipping_address', header: 'Shipping Address', showByDefault: true },
    { accessorKey: 'billing_address', header: 'Billing Address', showByDefault: true },
    { accessorKey: 'shipping_cost', header: 'Shipping Cost', showByDefault: true },
    { accessorKey: 'discount_applied', header: 'Discount Applied', showByDefault: true },
    { accessorKey: 'remarks', header: 'Remarks', showByDefault: true },
    {
      accessorKey: 'createdAt',
      header: 'Created At',
      showByDefault: true,
      cell: ({ cell }) => {
        const formattedDate = dayjs(cell.row.original.createdAt).format('DD-MM-YYYY hh:mm A');
        return <span>{formattedDate}</span>;
      }
    },
    {
      id: 'actions',
      header: 'Action',
      cell: (cell) => {
        return (
          <Box display="flex" flexDirection="row">
            {' '}
            <TableActions handleEditClick={openEditDialog} cell={cell} handleDeleteClick={handleDelete} />
            <Tooltip title="Update product status">
              <IconButton
                color="success"
                size="medium"
                onClick={() => {
                  setSelectedOrder(cell.row.original);
                  handleOpenStatusDialog(cell.row.original.id);
                }}
                style={{ marginLeft: '8px' }}
              >
                <DashboardOutlined />
              </IconButton>
            </Tooltip>
          </Box>
        );
      }
    }
  ];

  return (
    <Box>
      <CustomerTable data={orders} columns={columns} modalToggler={openAddDialog} category={'Order'} />

      <Dialog
        open={statusDialogOpen}
        onClose={(event, reason) => {
          if (reason !== 'backdropClick' && reason !== 'escapeKeyDown') {
            setStatusDialogOpen(false);
          }
        }}
        maxWidth="50%"
        PaperProps={{
          sx: {
            top: 0,
            position: 'absolute',
            borderRadius: '16px',
            width: '100%',
            maxWidth: '600px'
          }
        }}
      >
        <DialogTitle>Update Order Status</DialogTitle>
        <DialogContent>
          <FormControl fullWidth sx={{ marginTop: 1 }}>
            <InputLabel>Order Status</InputLabel>
            <Select value={selectedStatus} onChange={(e) => setSelectedStatus(e.target.value)} label="Order Status">
              {ORDER_TYPES.map((status, index) => (
                <MenuItem key={index} value={status}>
                  {status}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
          <FormControl fullWidth sx={{ marginTop: 1 }}>
            <InputLabel>Payment Status</InputLabel>
            <Select value={selectedPaymentStatus} onChange={(e) => setSelectedPaymentStatus(e.target.value)} label="Payment Status">
              {PAYMENT_STATUS.map((status, index) => (
                <MenuItem key={index} value={status}>
                  {status}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setStatusDialogOpen(false)} color="primary">
            Cancel
          </Button>
          <Button
            onClick={() => {
              handleUpdateStatus(selectedOrder?.id, selectedStatus, selectedPaymentStatus);
              setStatusDialogOpen(false);
            }}
            color="primary"
            disabled={!selectedStatus || !selectedPaymentStatus}
          >
            Update Status
          </Button>
        </DialogActions>
      </Dialog>

      <ToastContainer />
    </Box>
  );
}

{
  /* <Dialog open={openDialog} onClose={() => setOpenDialog(false)} maxWidth="md">
<Formik
  initialValues={{
    product_id: '',
    user_id: '',
    ngo_id: '',
    quantity: '',
    price_per_unit: '',
    total_price: '',
    order_status: '',
    payment_status: '',
    shipping_address: '',
    billing_address: '',
    shipping_cost: '',
    discount_applied: '',
    remarks: ''
  }}
  validationSchema={yup.object().shape({
    product_id: yup.number().required('Product ID is required'),
    user_id: yup.number().required('User ID is required'),
    ngo_id: yup.number().required('NGO ID is required'),
    quantity: yup.number().required('Quantity is required'),
    price_per_unit: yup.number().required('Price per Unit is required'),
    total_price: yup.number().required('Total Price is required'),
    order_status: yup.string().required('Order Status is required'),
    payment_status: yup.string().required('Payment Status is required'),
    shipping_address: yup.string().required('Shipping Address is required'),
    billing_address: yup.string().required('Billing Address is required'),
    shipping_cost: yup.number().nullable(),
    discount_applied: yup.number().nullable(),
    remarks: yup.string().nullable()
  })}
  onSubmit={(values) => handleAddOrEdit(values)}
>
  {({ values, handleSubmit, handleChange, handleBlur, errors, touched }) => (
    <form onSubmit={handleSubmit}>
      <DialogTitle>{isEditing ? 'Edit Order' : 'Add Order'}</DialogTitle>
      <DialogContent>
        <Grid container spacing={2}>
          <Grid item xs={12}>
            <TextField
              label="Product ID"
              name="product_id"
              fullWidth
              type="number"
              value={values.product_id}
              onChange={handleChange}
              onBlur={handleBlur}
              error={touched.product_id && Boolean(errors.product_id)}
              helperText={touched.product_id && errors.product_id}
            />
          </Grid>
          <Grid item xs={12}>
            <TextField
              label="User ID"
              name="user_id"
              fullWidth
              type="number"
              value={values.user_id}
              onChange={handleChange}
              onBlur={handleBlur}
              error={touched.user_id && Boolean(errors.user_id)}
              helperText={touched.user_id && errors.user_id}
            />
          </Grid>
          <Grid item xs={12}>
            <TextField
              label="NGO ID"
              name="ngo_id"
              fullWidth
              type="number"
              value={values.ngo_id}
              onChange={handleChange}
              onBlur={handleBlur}
              error={touched.ngo_id && Boolean(errors.ngo_id)}
              helperText={touched.ngo_id && errors.ngo_id}
            />
          </Grid>
          <Grid item xs={12}>
            <TextField
              label="Quantity"
              name="quantity"
              fullWidth
              type="number"
              value={values.quantity}
              onChange={handleChange}
              onBlur={handleBlur}
              error={touched.quantity && Boolean(errors.quantity)}
              helperText={touched.quantity && errors.quantity}
            />
          </Grid>
          <Grid item xs={12}>
            <TextField
              label="Price per Unit"
              name="price_per_unit"
              fullWidth
              type="number"
              value={values.price_per_unit}
              onChange={handleChange}
              onBlur={handleBlur}
              error={touched.price_per_unit && Boolean(errors.price_per_unit)}
              helperText={touched.price_per_unit && errors.price_per_unit}
            />
          </Grid>
          <Grid item xs={12}>
            <TextField
              label="Total Price"
              name="total_price"
              fullWidth
              type="number"
              value={values.total_price}
              onChange={handleChange}
              onBlur={handleBlur}
              error={touched.total_price && Boolean(errors.total_price)}
              helperText={touched.total_price && errors.total_price}
            />
          </Grid>
          <Grid item xs={12}>
            <TextField
              label="Order Status"
              name="order_status"
              fullWidth
              value={values.order_status}
              onChange={handleChange}
              onBlur={handleBlur}
              error={touched.order_status && Boolean(errors.order_status)}
              helperText={touched.order_status && errors.order_status}
            />
          </Grid>
          <Grid item xs={12}>
            <TextField
              label="Payment Status"
              name="payment_status"
              fullWidth
              value={values.payment_status}
              onChange={handleChange}
              onBlur={handleBlur}
              error={touched.payment_status && Boolean(errors.payment_status)}
              helperText={touched.payment_status && errors.payment_status}
            />
          </Grid>
          <Grid item xs={12}>
            <TextField
              label="Shipping Address"
              name="shipping_address"
              fullWidth
              value={values.shipping_address}
              onChange={handleChange}
              onBlur={handleBlur}
              error={touched.shipping_address && Boolean(errors.shipping_address)}
              helperText={touched.shipping_address && errors.shipping_address}
            />
          </Grid>
          <Grid item xs={12}>
            <TextField
              label="Billing Address"
              name="billing_address"
              fullWidth
              value={values.billing_address}
              onChange={handleChange}
              onBlur={handleBlur}
              error={touched.billing_address && Boolean(errors.billing_address)}
              helperText={touched.billing_address && errors.billing_address}
            />
          </Grid>
          <Grid item xs={12}>
            <TextField
              label="Shipping Cost"
              name="shipping_cost"
              fullWidth
              type="number"
              value={values.shipping_cost}
              onChange={handleChange}
              onBlur={handleBlur}
              error={touched.shipping_cost && Boolean(errors.shipping_cost)}
              helperText={touched.shipping_cost && errors.shipping_cost}
            />
          </Grid>
          <Grid item xs={12}>
            <TextField
              label="Discount Applied"
              name="discount_applied"
              fullWidth
              type="number"
              value={values.discount_applied}
              onChange={handleChange}
              onBlur={handleBlur}
              error={touched.discount_applied && Boolean(errors.discount_applied)}
              helperText={touched.discount_applied && errors.discount_applied}
            />
          </Grid>
          <Grid item xs={12}>
            <TextField
              label="Remarks"
              name="remarks"
              fullWidth
              value={values.remarks}
              onChange={handleChange}
              onBlur={handleBlur}
              error={touched.remarks && Boolean(errors.remarks)}
              helperText={touched.remarks && errors.remarks}
            />
          </Grid>
        </Grid>
      </DialogContent>
      <DialogActions>
        <Button onClick={() => setOpenDialog(false)} color="secondary">
          Cancel
        </Button>
        <Button type="submit" color="primary">
          {isEditing ? 'Update Order' : 'Add Order'}
        </Button>
      </DialogActions>
    </form>
  )}
</Formik>
</Dialog> */
}
