import axiosServices from 'utils/axios_node';

const API_BASE_URL = `${import.meta.env.VITE_APP_APPLICATION_API_URL}/api`;

export const getQuestionsList = async () => {
  //   const pageNameParam = pageName ? `&pageName=${pageName}` : '';
  const response = await axiosServices.get(`${API_BASE_URL}/questions`);
  return response?.data;
};
export const insertQuestion = async (payload) => {
  const response = await axiosServices.post(`${API_BASE_URL}/questions`, payload);
  return response?.data;
};

export const updateQuestion = async (id, payload) => {
  const response = await axiosServices.put(`${API_BASE_URL}/questions/${id}`, payload);
  return response?.data;
};
export const deleteQuestion = async (id) => {
  const response = await axiosServices.delete(`${API_BASE_URL}/questions/${id}`);
  return response?.data;
};
