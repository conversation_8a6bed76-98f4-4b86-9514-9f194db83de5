import { useEffect, useState } from 'react';

// material-ui
import { useTheme } from '@mui/material/styles';
import Box from '@mui/material/Box';

// third-party
import ReactApexChart from 'react-apexcharts';

// project-import
import useConfig from 'hooks/useConfig';
import { ThemeMode } from 'config';
import useAuth from 'hooks/useAuth';
import { getNgoById } from 'api/ngos.service';
import { calculateProfileCompletion } from 'utils/permissionUtils';

// chart options
const redialBarChartOptions = {
  plotOptions: {
    radialBar: {
      hollow: {
        margin: 0,
        size: '75%'
      },
      track: {
        margin: 0
      },
      dataLabels: {
        name: {
          show: false
        },
        value: {
          offsetY: 5
        }
      }
    }
  },
  labels: ['Vimeo']
};

// ==============================|| TOP CARD - RADIAL BAR CHART ||============================== //

export default function ProfileRadialChart() {
  const theme = useTheme();
  const { mode } = useConfig();
  const [ngo, setNgo] = useState(null);
  const textPrimary = theme.palette.text.primary;
  const primary = theme.palette.primary.main;
  const grey0 = theme.palette.grey[0];
  const grey500 = theme.palette.grey[500];
  const grey200 = theme.palette.grey[200];
  const [series, setSeries] = useState([0]);

  const { user } = useAuth();

  useEffect(() => {
    fetchNGO(user.ngo_id);
  }, []);

  useEffect(() => {
    const fetchProfileCompletion = async () => {
      if (ngo) {
        const profileCompletePercentage = await calculateProfileCompletion(ngo);
        setSeries([profileCompletePercentage]);
      }
    };

    fetchProfileCompletion();
  }, [ngo]);

  const fetchNGO = async (ngo_id) => {
    try {
      const response = await getNgoById(ngo_id);
      setNgo(response);
    } catch (error) {
      console.error('Failed to fetch ngos:', error);
    }
  };

  // const profileCompletePercentage=calculateProfileCompletion(ngoD)
  // const calculateProfileCompletion = (ngoData) => {
  //   const requiredFields = [
  //     'name',
  //     'email',
  //     'darpan_id',
  //     'pan',
  //     'type_of_ngo',
  //     'website_url',
  //     'point_of_contact_name',
  //     'point_of_contact_mobile_number',
  //     'registered_address',
  //     'current_address',
  //     'date_of_establishment',
  //     'latitude',
  //     'longitude',
  //     'pincode',
  //     'state',
  //     'claimed_ngo',
  //     'vision',
  //     'mission',
  //     'about_us',
  //     'darpan_last_modified'
  //   ];
  //   const completedFields = requiredFields.filter((field) => ngoData[field] && ngoData[field].toString().trim() !== '');
  //   const profileCompletePercentage = Number((completedFields.length / requiredFields.length) * 100).toFixed();
  //   setSeries([profileCompletePercentage]);
  // };

  // const profileCompletePercentage=
  const [options, setOptions] = useState(redialBarChartOptions);

  useEffect(() => {
    setOptions((prevState) => ({
      ...prevState,
      colors: [primary],
      plotOptions: {
        radialBar: {
          track: {
            background: mode === ThemeMode.DARK ? grey200 : grey0
          },
          dataLabels: {
            value: {
              fontSize: '1rem',
              fontWeight: 600,
              offsetY: 5,
              color: textPrimary
            }
          }
        }
      },
      theme: {
        mode: mode === ThemeMode.DARK ? 'dark' : 'light'
      }
    }));
  }, [mode, grey200, grey0, grey500, textPrimary, primary]);

  return (
    <Box id="chart" sx={{ bgcolor: 'transparent' }}>
      <ReactApexChart options={options} series={series} type="radialBar" width={136} height={136} />
    </Box>
  );
}
