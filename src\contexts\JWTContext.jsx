import React, { createContext, useEffect, useReducer } from 'react';

// third-party
import { jwtDecode } from 'jwt-decode';

// reducer - state management
import { LOGIN, LOGOUT } from 'contexts/auth-reducer/actions';
import authReducer from 'contexts/auth-reducer/auth';

// project import
import Loader from 'components/Loader';
import axios from 'utils/axios_node';

import { toast, ToastContainer } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import { getNgoById } from 'api/ngos.service';
import { openSnackbar } from 'api/snackbar';

// constant
const initialState = {
  isLoggedIn: false,
  isInitialized: false,
  user: null
};

const verifyToken = (serviceToken) => {
  if (!serviceToken) {
    return false;
  }
  const decoded = jwtDecode(serviceToken);
  /**
   * Property 'exp' does not exist on type '<T = unknown>(token: string, options?: JwtDecodeOptions | undefined) => T'.
   */
  return decoded.exp > Date.now() / 1000;
};

const setSession = (serviceToken) => {
  if (serviceToken) {
    localStorage.setItem('serviceToken', serviceToken);
    axios.defaults.headers.common.Authorization = `Bearer ${serviceToken}`;
  } else {
    localStorage.removeItem('serviceToken');
    delete axios.defaults.headers.common.Authorization;
  }
};

// ==============================|| JWT CONTEXT & PROVIDER ||============================== //

const JWTContext = createContext(null);

export const JWTProvider = ({ children }) => {
  const [state, dispatch] = useReducer(authReducer, initialState);

  useEffect(() => {
    const init = async () => {
      try {
        const serviceToken = window.localStorage.getItem('serviceToken');
        const userInfo = JSON.parse(window.localStorage.getItem('user'));
        if (serviceToken && userInfo?.id) {
          setSession(serviceToken);
          const response = await axios.get(`/api/portal-users/${userInfo?.id}`);
          if (userInfo?.ngo_id) {
            fetchNGO(userInfo?.ngo_id);
          }
          dispatch({
            type: LOGIN,
            payload: {
              isLoggedIn: true,
              user: response.data
            }
          });
        } else {
          dispatch({
            type: LOGOUT
          });
        }
      } catch (err) {
        console.error(err);
        dispatch({
          type: LOGOUT
        });
      }
    };

    init();
  }, []);

  const fetchNGO = async (ngo_id) => {
    try {
      const response = await getNgoById(ngo_id);
      localStorage.setItem('userNgoInfo', JSON.stringify(response));
    } catch (error) {
      console.error('Failed to fetch ngos:', error);
    }
  };

  const login = async (email, password) => {
    try {
      const response = await axios.post('/api/portal-users/authenticate', { email, password });
      const { serviceToken, user } = response?.data?.result;
      setSession(serviceToken);
      //   localStorage.setItem('user', JSON.stringify(user));
      localStorage.setItem('user', JSON.stringify(user));
      if (user?.ngo_id) {
        await fetchNGO(user?.ngo_id);
      }
      dispatch({
        type: LOGIN,
        payload: {
          isLoggedIn: true,
          user
        }
      });
      window.location.reload();
    } catch (e) {
      if (e?.message && e?.message?.includes('Inactive')) {
        throw new Error(e?.message);
      } else {
        toast.error(e?.message || 'An error occurred');
      }
    }
  };

  const addNgoUser = async (payload) => {
    payload.fullname = payload.name;
    payload.status = 'Active';
    await axios.post('/api/portal-users', payload);
    return;
  };

  const register = async (payload) => {
    try {
      // const getResponse = await axios.get(
      //   `/api/ngos/search/searchBySlugAndPhone?name=${payload.name}&phone_number=${payload.point_of_contact_mobile_number}`
      // );
      // const nameEmailResponse = await axios.get(`/api/ngos/search/searchBySlugAndEmail?name=${payload.name}&email=${payload.email}`);
      const darpanIdResponse = await axios.get(`/api/ngos/search/searchByDarpanId?darpanId=${payload.darpan_id}&email=${payload.email}`);

      if (darpanIdResponse?.data && darpanIdResponse?.data?.ngoDetails?.length > 0) {
        return { status: false, ngos: darpanIdResponse?.data, matchedNgos: darpanIdResponse?.data };
      }

      payload.source = 'Self';
      const response = await axios.post('/api/ngos', payload);
      if (response?.data) {
        payload.ngo_id = response?.data?.ngoRecord?.id;
        addNgoUser(payload);
        return response;
      }
    } catch (err) {
      if (err?.message?.includes('is already')) {
        openSnackbar({
          open: true,
          message: 'This Email is Already Taken. Please try with a different email.',
          variant: 'alert',
          alert: { color: 'error' }
        });
      } else if (err?.message.includes('We are reviewing your')) {
        openSnackbar({
          open: true,
          message: 'We are reviewing your NGO profile and will get back to you soon.',
          variant: 'alert',
          alert: { color: 'error' }
        });
      } else {
        openSnackbar({
          open: true,
          message: 'Something went wrong. Please try bro.',
          variant: 'alert',
          alert: { color: 'danger' }
        });
      }
    }
  };

  const logout = () => {
    setSession(null);
    localStorage.clear();
    localStorage.clear();
    dispatch({ type: LOGOUT });
    window.location.reload();
  };

  const resetPassword = async (email) => {
    try {
      await axios.post('/api/portal-users/requestPasswordReset', { email });
      return;
    } catch (e) {
      toast.error(e?.message);
    }
  };

  const updateProfile = () => {};

  if (state.isInitialized !== undefined && !state.isInitialized) {
    return <Loader />;
  }

  return (
    <JWTContext.Provider value={{ ...state, login, logout, register, resetPassword, updateProfile }}>
      {children}
      <ToastContainer />
    </JWTContext.Provider>
  );
};

export default JWTContext;
