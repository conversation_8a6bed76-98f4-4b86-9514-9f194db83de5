import React, { useEffect, useState } from 'react';
import axios from 'axios';
import { Link as RouterLink } from 'react-router-dom';

// material-ui
import CardContent from '@mui/material/CardContent';
import CardMedia from '@mui/material/CardMedia';
import Grid from '@mui/material/Grid';
import Typography from '@mui/material/Typography';
import Link from '@mui/material/Link';
import CircularProgress from '@mui/material/CircularProgress';
import UploadSingleFile from 'components/third-party/dropzone/SingleFile';

import Alert from '@mui/material/Alert';
import { toast, ToastContainer } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';

// project imports
import MainCard from 'components/MainCard';
import { addThemeService, getThemesdata, updateThemeService } from './channel.service';
import {
  Button,
  DialogActions,
  Dialog,
  DialogTitle,
  DialogContent,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Stack
} from '@mui/material';
import { EditOutlined } from '@ant-design/icons';
import useAuth from 'hooks/useAuth';
import { getConvertedFileName } from 'utils/permissionUtils';
import { Formik } from 'formik';
import * as yup from 'yup';
import { BASE_URL } from 'sections/apps/profiles/profile.service';

// material-ui styles
const mediaSX = {
  width: 90,
  height: 80,
  borderRadius: 1
};

// ===========================|| DATA WIDGET - LATEST themes ||=========================== //

export default function Themes() {
  const { user } = useAuth();
  const [themes, setThemes] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [openDialog, setOpenDialog] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [currentTheme, setCurrentTheme] = useState(null);

  const [fileError, setFileError] = useState(null);

  // Fetch data from the API
  useEffect(() => {
    fetchThemes();
  }, []);
  const fetchThemes = async () => {
    try {
      const response = await getThemesdata();
      setThemes(response);
      setLoading(false);
    } catch (err) {
      setError(err.message || 'Something went wrong while fetching data.');
      setLoading(false);
    }
  };
  const handleAddOrEdit = async (values) => {
    try {
      let formData = new FormData();

      const convertedFileName = getConvertedFileName(values.files[0].name);
      // currentTheme.image_name = convertedFileName; // Update image name in theme
      formData.append('image_name', convertedFileName);
      formData.append('file', values?.files[0], `${convertedFileName}`);

      formData.append('name', values.name);
      formData.append('description', values.description);
      formData.append('status', values.status);
      formData.append('banner_image_width', values.banner_image_width);
      formData.append('banner_image_height', values.banner_image_height);
      formData.append('banner_image_count', values.banner_image_count);
      formData.append('gallery_image_width', values.gallery_image_width);
      formData.append('gallery_image_height', values.gallery_image_height);
      formData.append('gallery_image_count', values.gallery_image_count);
      formData.append('impact_areas_count', values.impact_areas_count);

      if (isEditing) {
        // Update the theme
        formData.append('createdBy', user.id);
        const response = await updateThemeService(currentTheme.id, formData);
        if (response.status === 200) {
          toast.success('Theme updated successfully!');
          setOpenDialog(false);
          fetchThemes();
        }
      } else {
        // Add new theme
        // currentTheme.createdBy = user.id;
        formData.append('createdBy', user.id);
        const response = await addThemeService(formData);
        if (response.status) {
          toast.success('Theme added successfully!');
          setOpenDialog(false);
          fetchThemes();
        } else {
          toast.error('Failed to add Theme');
        }
      }
    } catch (error) {
      console.error('Failed to save theme:', error);
      toast.error('Failed to save theme');
    }
  };
  const openEditDialog = (theme) => {
    setCurrentTheme(theme);
    setIsEditing(true);
    setOpenDialog(true);
  };
  const openAddDialog = () => {
    setCurrentTheme(null);
    setIsEditing(false);
    setOpenDialog(true);
  };

  // Render loading and error states
  if (loading) {
    return (
      <MainCard title="Themes">
        <CardContent>
          <Grid container justifyContent="center">
            <CircularProgress />
          </Grid>
        </CardContent>
      </MainCard>
    );
  }

  if (error) {
    return (
      <MainCard title="Themes">
        <CardContent>
          <Alert severity="error">{error}</Alert>
        </CardContent>
      </MainCard>
    );
  }

  // Render the themes
  return (
    <>
      <Grid container justifyContent="flex-end" sx={{ marginBottom: 2 }}>
        {!user?.roleInfo?.name.startsWith('NGO') && (
          <Button variant="contained" color="primary" onClick={openAddDialog}>
            Add Theme
          </Button>
        )}
      </Grid>
      <MainCard content={false}>
        <CardContent>
          <Grid container spacing={3}>
            {themes.map((post, index) => (
              <Grid item xs={12} key={index}>
                <Grid container alignItems="center" spacing={2}>
                  <Grid item>
                    <CardMedia component="img" image={`${BASE_URL}/fetchThemeImage/${post?.image_name}`} title={post.name} sx={mediaSX} />
                  </Grid>
                  <Grid item xs zeroMinWidth>
                    <Grid container spacing={1}>
                      <Grid item xs={6}>
                        <Typography variant="subtitle1">{post.name}</Typography>
                        <Typography variant="caption" color="secondary">
                          {post.caption} | {new Date(post.createdAt).toLocaleString()}
                        </Typography>
                      </Grid>
                      <Grid item xs={6} container justifyContent="flex-end">
                        {!user?.roleInfo?.name.startsWith('NGO') && (
                          <Button onClick={() => openEditDialog(post)}>
                            <EditOutlined />
                          </Button>
                        )}
                      </Grid>
                      <Grid item xs={12}>
                        <Typography variant="body2">{post.description}</Typography>
                      </Grid>
                      <Grid item xs={12}>
                        <Typography variant="caption" color="textSecondary">
                          Cover Images: {post.banner_image_width}x{post.banner_image_height} | Count: {post.banner_image_count} |
                        </Typography>
                        <Typography variant="caption" color="textSecondary">
                          Gallery Images: {post.gallery_image_width}x{post.gallery_image_height} | Count: {post.gallery_image_count} |
                        </Typography>
                        <Typography variant="caption" color="textSecondary">
                          Impact Created: Count: {post.impact_areas_count}
                        </Typography>
                      </Grid>
                    </Grid>
                  </Grid>
                </Grid>
              </Grid>
            ))}
          </Grid>
        </CardContent>
        <Dialog
          open={openDialog}
          onClose={() => setOpenDialog(false)}
          PaperProps={{
            sx: {
              borderRadius: '16px'
            }
          }}
        >
          <Formik
            initialValues={{
              ...currentTheme
            }}
            onSubmit={async (values) => {
              await handleAddOrEdit(values);
            }}
            validationSchema={yup.object().shape({
              name: yup.string().required('Name is required'),
              description: yup.string().required('Description is required'),
              files: yup.mixed().required('File is a required.')
            })}
          >
            {({ values, handleSubmit, handleChange, setFieldValue, touched, errors }) => (
              <form onSubmit={handleSubmit} autoComplete="off">
                <DialogTitle>{isEditing ? 'Edit Theme' : 'Add Theme'}</DialogTitle>
                <DialogContent>
                  <TextField
                    label="Name"
                    fullWidth
                    variant="outlined"
                    name="name"
                    value={values?.name || ''}
                    onChange={handleChange}
                    margin="dense"
                    required
                  />
                  <TextField
                    label="Description"
                    fullWidth
                    name="description"
                    required
                    variant="outlined"
                    value={values?.description || ''}
                    onChange={handleChange}
                    margin="dense"
                    multiline
                  />
                  <TextField
                    label="Cover Image Width"
                    fullWidth
                    required
                    name="banner_image_width"
                    variant="outlined"
                    value={values?.banner_image_width || ''}
                    onChange={handleChange}
                    margin="dense"
                  />
                  <TextField
                    label="Cover Image Height"
                    fullWidth
                    required
                    variant="outlined"
                    name="banner_image_height"
                    value={values?.banner_image_height || ''}
                    onChange={handleChange}
                    margin="dense"
                  />
                  <TextField
                    label="Cover Image Count"
                    fullWidth
                    variant="outlined"
                    name="banner_image_count"
                    required
                    value={values?.banner_image_count || ''}
                    onChange={handleChange}
                    margin="dense"
                  />

                  <TextField
                    label="Gallery Image Width"
                    fullWidth
                    required
                    name="gallery_image_width"
                    variant="outlined"
                    value={values?.gallery_image_width || ''}
                    onChange={handleChange}
                    margin="dense"
                  />
                  <TextField
                    label="Gallery Image Height"
                    fullWidth
                    required
                    name="gallery_image_height"
                    variant="outlined"
                    value={values?.gallery_image_height || ''}
                    onChange={handleChange}
                    margin="dense"
                  />

                  <TextField
                    label="Gallery Image Count"
                    fullWidth
                    required
                    name="gallery_image_count"
                    variant="outlined"
                    value={values?.gallery_image_count || ''}
                    onChange={handleChange}
                    margin="dense"
                  />
                  <TextField
                    label="Impact Created Count"
                    fullWidth
                    required
                    name="impact_areas_count"
                    variant="outlined"
                    value={values?.impact_areas_count || ''}
                    onChange={handleChange}
                    margin="dense"
                  />
                  <FormControl fullWidth margin="dense" variant="outlined">
                    <InputLabel>Status</InputLabel>
                    <Select value={values?.status} name="status" onChange={handleChange} required label="Status">
                      <MenuItem value="active">Active</MenuItem>
                      <MenuItem value="inactive">Inactive</MenuItem>
                    </Select>
                  </FormControl>
                  <Grid container spacing={3} style={{ marginTop: '2px' }}>
                    <Grid item xs={12}>
                      <Stack spacing={1.5} alignItems="center">
                        <UploadSingleFile setFieldValue={setFieldValue} file={values.files} error={touched.files && !!errors.files} />

                        <Stack spacing={0}>
                          <Typography align="center" variant="caption" color="secondary">
                            *.png, *.jpeg, *.jpg, *.gif
                          </Typography>
                        </Stack>
                      </Stack>
                      {touched.files && errors.files && (
                        <FormHelperText error id="standard-weight-helper-text-password-login">
                          {errors.files}
                        </FormHelperText>
                      )}
                    </Grid>
                  </Grid>
                </DialogContent>
                <DialogActions>
                  <Button onClick={() => setOpenDialog(false)} color="primary">
                    Cancel
                  </Button>
                  <Button type="submit" color="primary">
                    {isEditing ? 'Update' : 'Add'}
                  </Button>
                </DialogActions>
              </form>
            )}
          </Formik>
        </Dialog>
        <ToastContainer />
      </MainCard>
    </>
  );
}
