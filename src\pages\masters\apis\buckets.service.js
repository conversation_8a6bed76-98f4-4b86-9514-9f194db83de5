import axiosServices from 'utils/axios_node';

const API_BASE_URL = `${import.meta.env.VITE_APP_APPLICATION_API_URL}/api`;

export const fetchBucketsService = async () => {
  const response = await axiosServices.get(`${API_BASE_URL}/buckets`);
  return response.data;
};
export const getBucketById = async (id) => {
  const response = await axiosServices.get(`${API_BASE_URL}/buckets/${id}`);
  return response.data;
};
export const getBucketItems = async (id) => {
  const response = await axiosServices.get(`${API_BASE_URL}/bucket-items?bucketId=${id}`);
  return response.data;
};

export const addBucketservice = async (bucket) => {
  const response = await axiosServices.post(`${API_BASE_URL}/buckets`, bucket);
  return response.data;
};

export const updateBucketservice = async (id, bucket) => {
  const response = await axiosServices.put(`${API_BASE_URL}/buckets/${id}`, bucket);
  return response.data;
};

export const deleteBucketservice = async (id) => {
  const response = await axiosServices.delete(`${API_BASE_URL}/buckets/${id}`);
  return response.data;
};
