import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Grid,
  TextField,
  Typography,
  InputAdornment,
  FormControlLabel,
  RadioGroup,
  Radio,
  Autocomplete,
  Checkbox,
  CircularProgress
} from '@mui/material';
import { LocalizationProvider, MobileDatePicker, MobileTimePicker } from '@mui/x-date-pickers';
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';
import dayjs from 'dayjs';
import { useEffect, useState } from 'react';
import { toast } from 'react-toastify';
import { useNavigate } from 'react-router-dom';
import { addCampaign } from 'api/campaigns.service';

const CampaignFormDialog = ({ open, onClose, campaign, MAX_LENGTH = 500, categories, fetchCampaigns }) => {
  const [formData, setFormData] = useState({ ...campaign });
  const [isSubmitting, setIsSubmitting] = useState(false);

  useEffect(() => {
    if (campaign) {
      const withDate = { ...campaign };

      if (campaign.event_start_time) {
        withDate.event_start_time = dayjs(`1970-01-01T${campaign.event_start_time}`); // ✅ dayjs object
      }

      if (campaign.event_end_time) {
        withDate.event_end_time = dayjs(`1970-01-01T${campaign.event_end_time}`); // ✅ dayjs object
      }

      setFormData(withDate);
    }
  }, [campaign]);

  const handleChange = (field, value) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value
    }));
  };

  const handleCampaignDuplicate = async () => {
    setIsSubmitting(true);
    const confirmApprove = window.confirm('Are you sure you want to duplicate this Campaign?');
    if (confirmApprove) {
      try {
        const { id, ...campaignWithoutId } = formData;
        const campaignDataToSubmit = {
          ...campaignWithoutId,
          event_start_time: formData.event_start_time ? dayjs(formData.event_start_time).format('HH:mm:ss') : null,
          event_end_time: formData.event_end_time ? dayjs(formData.event_end_time).format('HH:mm:ss') : null,
          imagesUpdated: null // assuming you want to reset this
        };
        const campaignData = await addCampaign({ ...campaignDataToSubmit, imagesUpdated: null }, 'CampaignsTable');
        toast.success('Campaign has been Duplicated!');
        setIsSubmitting(false);
        onClose();
        fetchCampaigns();
      } catch (error) {
        console.error('Failed to duplicate:', error);
        toast.error('Failed to duplicate the campaign');
      }
    }
  };

  return (
    <Dialog
      open={open}
      onClose={(event, reason) => {
        if (reason !== 'backdropClick' && reason !== 'escapeKeyDown') {
          onClose();
        }
      }}
      maxWidth="50%"
      PaperProps={{
        sx: {
          top: 0,
          position: 'absolute',
          borderRadius: '16px',
          width: '100%',
          maxWidth: '600px'
        }
      }}
    >
      <LocalizationProvider dateAdapter={AdapterDayjs}>
        <DialogTitle>Duplicate Campaign</DialogTitle>
        <DialogContent dividers>
          <Grid container spacing={2}>
            <Grid item xs={12}>
              <Grid container spacing={3} direction="column">
                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    label="Campaign Title"
                    name="name"
                    value={formData.name || ''}
                    required
                    onChange={(e) => handleChange('name', e.target.value)}
                  />
                </Grid>

                <Grid item xs={12} sm={6}>
                  <Grid container spacing={2}>
                    <Grid item xs={12} sm={6}>
                      <Autocomplete
                        fullWidth
                        options={categories}
                        getOptionLabel={(option) => option.name || ''}
                        value={categories.find((cat) => cat.id === formData.category_id) || null}
                        onChange={(_, value) => handleChange('category_id', value?.id || '')}
                        renderInput={(params) => <TextField {...params} label="Category" name="category_id" required />}
                      />
                    </Grid>

                    <Grid item xs={12} sm={6}>
                      <TextField
                        fullWidth
                        label="Goal Amount"
                        name="fund_raising_target"
                        type="number"
                        required
                        value={formData.fund_raising_target || ''}
                        onChange={(e) => handleChange('fund_raising_target', e.target.value)}
                        InputProps={{
                          startAdornment: <InputAdornment position="start">₹</InputAdornment>
                        }}
                      />
                    </Grid>
                  </Grid>
                </Grid>

                <Grid item xs={12} sm={6}>
                  <Grid container spacing={2}>
                    <Grid item xs={12} sm={6}>
                      <MobileDatePicker
                        label="Campaign Start Date"
                        value={formData.campaign_start_date ? dayjs(formData.campaign_start_date) : null}
                        onChange={(newValue) => handleChange('campaign_start_date', dayjs(newValue).format('YYYY-MM-DD'))}
                        format="DD/MM/YYYY"
                        disablePast
                        disableFuture
                        slotProps={{
                          textField: {
                            fullWidth: true,
                            name: 'campaign_start_date',
                            required: true
                          }
                        }}
                      />
                    </Grid>

                    <Grid item xs={12} sm={6}>
                      <MobileDatePicker
                        label="Campaign End Date"
                        value={formData.campaign_end_date ? dayjs(formData.campaign_end_date) : null}
                        onChange={(newValue) => handleChange('campaign_end_date', dayjs(newValue).format('YYYY-MM-DD'))}
                        format="DD/MM/YYYY"
                        minDate={formData.campaign_start_date ? dayjs(formData.campaign_start_date) : undefined}
                        slotProps={{
                          textField: {
                            fullWidth: true,
                            name: 'campaign_end_date',
                            required: true
                          }
                        }}
                      />
                    </Grid>
                  </Grid>
                </Grid>

                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    multiline
                    rows={4}
                    label="Description"
                    required
                    name="description"
                    value={formData.description || ''}
                    onChange={(e) => handleChange('description', e.target.value)}
                  />
                  <Typography variant="caption" sx={{ display: 'block', textAlign: 'right', color: 'gray', mt: 0.5 }}>
                    {formData.description?.length ? MAX_LENGTH - formData.description.length : MAX_LENGTH} characters left
                  </Typography>
                </Grid>

                <Grid item xs={12} sm={6} sx={{ marginLeft: 1 }}>
                  <Typography variant="subtitle1" gutterBottom>
                    Volunteers Required? *
                  </Typography>
                  <RadioGroup
                    row
                    name="volunteers_required"
                    value={formData.volunteers_required || ''}
                    onChange={(e) => handleChange('volunteers_required', e.target.value)}
                  >
                    <FormControlLabel value="yes" control={<Radio color="primary" />} label="Yes" />
                    <FormControlLabel value="no" control={<Radio color="primary" />} label="No" />
                  </RadioGroup>
                </Grid>

                {formData.volunteers_required === 'yes' && (
                  <Grid item xs={12} sm={6}>
                    <TextField
                      fullWidth
                      required
                      type="text"
                      label="Number of Volunteers"
                      name="number_of_volunteers"
                      inputProps={{ inputMode: 'numeric', pattern: '[0-9]*' }}
                      value={formData.number_of_volunteers || ''}
                      onChange={(e) => {
                        const value = e.target.value;
                        if (/^\d*$/.test(value)) {
                          handleChange('number_of_volunteers', value);
                        }
                      }}
                    />
                  </Grid>
                )}

                {formData.volunteers_required === 'yes' && (
                  <>
                    <Grid item xs={12} sm={6} sx={{ marginLeft: 1 }}>
                      <Grid container md={12} rowSpacing={3} columnSpacing={2}>
                        <Grid item xs={12} sm={6}>
                          <Typography variant="subtitle1" gutterBottom>
                            Event Format *
                          </Typography>
                          <RadioGroup row value={formData.event_type} onChange={(e) => handleChange('event_type', e.target.value)}>
                            <FormControlLabel value="physical" control={<Radio color="primary" />} label="Physical" />
                            <FormControlLabel value="virtual" control={<Radio color="primary" />} label="Virtual" />
                          </RadioGroup>
                        </Grid>

                        <Grid item xs={12} sm={6}>
                          <Typography variant="subtitle1" gutterBottom>
                            Volunteer Type *
                          </Typography>
                          <RadioGroup row value={formData.volunteer_type} onChange={(e) => handleChange('volunteer_type', e.target.value)}>
                            <FormControlLabel value="self" control={<Radio color="primary" />} label="Self (Free)" />
                            <FormControlLabel value="paid" control={<Radio color="primary" />} label="Paid" />
                          </RadioGroup>
                        </Grid>
                      </Grid>
                    </Grid>
                    {formData.volunteer_type == 'paid' && (
                      <Grid item xs={12} sm={6}>
                        <TextField
                          name="amount_per_person"
                          label="Amount per Person (₹)"
                          required
                          value={formData.volunteer_type === 'self' ? null : formData.amount_per_person}
                          onChange={(e) => {
                            const val = e.target.value;
                            if (/^\d*$/.test(val) && Number(val) >= 1) {
                              handleChange(e.target.name, e.target.value);
                            }
                          }}
                          InputProps={{ inputProps: { min: 1 } }}
                          disabled={formData.volunteer_type === 'self'}
                          helperText={formData.volunteer_type === 'self' ? 'Free volunteer (₹0)' : ''}
                          fullWidth
                        />
                      </Grid>
                    )}
                    <Grid item xs={12} sm={6}>
                      <FormControlLabel
                        control={
                          <Checkbox
                            checked={formData.fullday_event === 'yes'}
                            onChange={(e) => handleChange('fullday_event', e.target.checked ? 'yes' : 'no')}
                          />
                        }
                        label="Full Day Event *"
                      />
                    </Grid>
                    {formData.fullday_event == 'no' && (
                      <Grid item xs={12} sm={6}>
                        <Grid container md={12} rowSpacing={2} columnSpacing={1}>
                          <Grid item xs={12} sm={6}>
                            <MobileTimePicker
                              label="Start Time *"
                              value={formData.event_start_time}
                              onChange={(value) => handleChange('event_start_time', value)}
                              slotProps={{ textField: { fullWidth: true } }}
                            />
                          </Grid>
                          <Grid item xs={12} sm={6}>
                            <MobileTimePicker
                              label="End Time *"
                              value={formData.event_end_time}
                              onChange={(value) => handleChange('event_end_time', value)}
                              slotProps={{ textField: { fullWidth: true } }}
                            />
                          </Grid>
                        </Grid>
                      </Grid>
                    )}
                    {formData.event_type === 'virtual' ? (
                      <Grid item xs={12} sm={6}>
                        {' '}
                        <TextField
                          label="Meeting Link *"
                          name="meeting_link"
                          value={formData.meeting_link}
                          onChange={(e) => handleChange(e.target.name, e.target.value)}
                          fullWidth
                        />
                      </Grid>
                    ) : (
                      <>
                        <Grid item xs={12} sm={6}>
                          <Grid container md={12} rowSpacing={2} columnSpacing={1}>
                            <Grid item xs={12} sm={6}>
                              <TextField
                                fullWidth
                                required
                                label="Pincode"
                                name="pincode"
                                value={formData?.pincode || ''}
                                onChange={(e) => handleChange(e.target.name, e.target.value)}
                                sx={{ marginRight: 5 }}
                              />
                            </Grid>
                            <Grid item xs={12} sm={6}>
                              <TextField fullWidth required label="City" name="place_name" value={formData.place_name || ''} disabled />
                            </Grid>

                            <Grid item xs={12} sm={6}>
                              <TextField fullWidth required label="State" name="state" value={formData.state || ''} disabled />
                            </Grid>
                            <Grid item xs={12} sm={6}>
                              <TextField fullWidth required label="Country" name="country" value={formData.country || ''} disabled />
                            </Grid>
                          </Grid>
                        </Grid>
                        <Grid item xs={12} sm={6}>
                          <TextField
                            fullWidth
                            multiline
                            rows={4}
                            label="Address"
                            required
                            name="current_address"
                            value={formData?.current_address}
                            onChange={(e) => handleChange(e.target.name, e.target.value)}
                            InputLabelProps={{ shrink: true }}
                            // inputProps={{ maxLength: 300 }}
                            // InputProps={{
                            //   endAdornment: (
                            //     <InputAdornment position="end">
                            //       <Button
                            //         variant="contained"
                            //         color="primary"
                            //         onClick={() => handleSearchSubmit(formData?.current_address)}
                            //         // disabled={isLoadingLocate}
                            //       >
                            //         {false ? <CircularProgress size={20} color="inherit" /> : 'Pin Your Address'}
                            //       </Button>
                            //     </InputAdornment>
                            //   )
                            // }}
                          />
                        </Grid>
                      </>
                    )}{' '}
                  </>
                )}
              </Grid>
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={onClose}>Cancel</Button>
          <Button variant="contained" onClick={handleCampaignDuplicate}>
            {isSubmitting ? <CircularProgress size={24} color="secondary" /> : 'Duplicate'}
          </Button>
        </DialogActions>
      </LocalizationProvider>
    </Dialog>
  );
};

export default CampaignFormDialog;
