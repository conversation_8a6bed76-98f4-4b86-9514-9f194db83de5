import { useEffect, useState } from 'react';
import { TextF<PERSON>, Box, Typography, CircularProgress, Button } from '@mui/material';
import { getNGOSBySearchTerm } from 'api/campaigns.service';
import useAuth from 'hooks/useAuth';
import CustomerTable from 'sections/apps/customer/CustomerTable';
import MainCard from 'components/MainCard';
import { ToastContainer, toast } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import { getNgos } from 'api/ngos.service';
import CustomerTableWithPagination from 'sections/apps/customer/CustomerTableWithPagination';

const SearchNGOPage = () => {
  const { user } = useAuth();
  const [searchQuery, setSearchQuery] = useState('');
  const [ngos, setNgos] = useState([]);
  const [loading, setLoading] = useState(false);
  const [page, setPage] = useState(1);
  const [totalCount, setTotalCount] = useState(0);
  const [pageSize, setPageSize] = useState(10);

  const handleCurrentPageChange = (newPage) => {
    setPage(newPage);
  };

  const handlePageSizeChange = (newPageSize) => {
    setPageSize(newPageSize);
  };

  const fetchNGOs = async () => {
    if (searchQuery.length < 2) return;

    setLoading(true);
    try {
      const params = {
        page: page,
        limit: pageSize,
        search: searchQuery
      };
      const result = await getNgos(params);

      if (result?.ngos?.length === 0) {
        toast.warn('No results found for your search input.');
        setLoading(false);
        return;
      }

      setNgos(result?.ngos);
      setTotalCount(result?.totalCount);
    } catch (error) {
      toast.error('We are having some problems fetching NGOs. Please try again after sometime.');
      console.error('Error fetching NGOs:', error);
      setNgos([]);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (searchQuery.length >= 2) {
      fetchNGOs(page);
    }
  }, [page]); // Fetch new data when page changes

  useEffect(() => {
    setPage(1);
    if (searchQuery.length >= 2) {
      fetchNGOs(page);
    }
  }, [pageSize]);

  const columns = [
    { accessorKey: 'name', header: 'Name', showByDefault: true },
    { accessorKey: 'ngo_status', header: 'Status', showByDefault: true },
    { accessorKey: 'ngo_type', header: 'NGO Type', showByDefault: true },
    { accessorKey: 'email', header: 'Email', showByDefault: true },
    { accessorKey: 'point_of_contact_mobile_number', header: 'Mobile', showByDefault: true },
    { accessorKey: 'nr_city_name', header: 'City', showByDefault: true },
    { accessorKey: 'state', header: 'State', showByDefault: true },
    { accessorKey: 'source', header: 'Source', showByDefault: true },
    { accessorKey: 'darpan_id', header: 'Darpan ID', showByDefault: false },
    { accessorKey: 'pan', header: 'PAN', showByDefault: false },
    { accessorKey: 'tweleve_number', header: '12A Number', showByDefault: false },
    { accessorKey: 'eighty_g_number', header: '80G Number', showByDefault: false },
    { accessorKey: 'type_of_ngo', header: 'Type of NGO', showByDefault: false },
    { accessorKey: 'website_url', header: 'Website URL', showByDefault: false },
    { accessorKey: 'point_of_contact_name', header: 'Contact Name', showByDefault: false },
    { accessorKey: 'registered_address', header: 'Registered Address', showByDefault: false },
    { accessorKey: 'current_address', header: 'Current Address', showByDefault: false },
    { accessorKey: 'date_of_establishment', header: 'Established Date', showByDefault: false },
    { accessorKey: 'latitude', header: 'Latitude', showByDefault: false },
    { accessorKey: 'longitude', header: 'Longitude', showByDefault: false },
    { accessorKey: 'pincode', header: 'Pincode', showByDefault: false },
    { accessorKey: 'place_name', header: 'Place Name', showByDefault: false },
    { accessorKey: 'claimed_ngo', header: 'Claimed NGO', showByDefault: false },
    { accessorKey: 'vision', header: 'Vision', showByDefault: false },
    { accessorKey: 'mission', header: 'Mission', showByDefault: false },
    { accessorKey: 'about_us', header: 'About Us', showByDefault: false },
    { accessorKey: 'custom_address', header: 'Custom Address', showByDefault: false },
    { accessorKey: 'physical_evidence', header: 'Physical Evidence', showByDefault: false },
    { accessorKey: 'documents_verified', header: 'Documents Verified', showByDefault: false },
    { accessorKey: 'status', header: 'Status', showByDefault: false },
    { accessorKey: 'darpan_last_modified', header: 'Darpan Modified', showByDefault: false },
    { accessorKey: 'grade', header: 'Grade', showByDefault: false },
    { accessorKey: 'rating', header: 'Rating', showByDefault: false },
    { accessorKey: 'country', header: 'Country', showByDefault: false },
    { accessorKey: 'registration_2A', header: 'Registration 2A', showByDefault: false },
    { accessorKey: 'registration_80G', header: 'Registration 80G', showByDefault: false },
    { accessorKey: 'fcra', header: 'FCRA', showByDefault: false },
    { accessorKey: 'csr_registration_number', header: 'CSR Reg. No.', showByDefault: false },
    { accessorKey: 'instagram_url', header: 'Instagram', showByDefault: false },
    { accessorKey: 'facebook_url', header: 'Facebook', showByDefault: false },
    { accessorKey: 'youtube_url', header: 'YouTube', showByDefault: false },
    { accessorKey: 'twitter_url', header: 'Twitter', showByDefault: false }
  ];

  return (
    <MainCard>
      {/* <Typography variant="h5" gutterBottom>
        Search NGOs
      </Typography> */}
      <form autoComplete="off">
        <Box sx={{ display: 'flex', gap: 2, mb: 2, width: '50%' }}>
          <TextField
            label="Search NGO"
            variant="outlined"
            fullWidth
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            onKeyDown={(e) => {
              if (e.key === 'Enter') {
                e.preventDefault();
                fetchNGOs(searchQuery);
              }
            }}
          />
          <Button variant="contained" type="button" onClick={fetchNGOs} disabled={loading || searchQuery?.length < 2}>
            Search
          </Button>
        </Box>
      </form>

      {loading ? (
        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', mt: 3 }}>
          <CircularProgress size={40} sx={{ mr: 1 }} />
          <Typography variant="body1">Fetching NGOs.....</Typography>
        </Box>
      ) : (
        ngos?.length > 0 && (
          <CustomerTableWithPagination
            data={ngos}
            columns={columns}
            category={'SearchNgoPage'}
            currentPage={page}
            totalCount={totalCount}
            onPageChange={handleCurrentPageChange}
            onPageSizeChange={handlePageSizeChange}
            pageSize={pageSize}
            showAddButton={false}
          />
        )
      )}

      <ToastContainer autoClose={6000} />
    </MainCard>
  );
};

export default SearchNGOPage;
