import React, { useState, useEffect, useRef } from 'react';
import axios from 'axios';
import { useNavigate } from 'react-router';
import { useParams } from 'react-router';
import mapboxgl from 'mapbox-gl';
import { TextField, Button, Typography, Grid, Stack, List, ListItem, ListItemText, Autocomplete, Chip, Box } from '@mui/material';
import MainCard from 'components/MainCard';
import { ToastContainer, toast } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import { getAllCategories, getNGOBasedCategories, getSubcategoriesByCategoryId } from 'api/categories.service';
import { deleteNGOCategories, getLoggedInNGOInfo, insertCategories, patchLocation } from '../profile.service';
import { getStateFromCoordinates } from 'api/campaigns.service';
import { getSessionStorageItem } from 'utils/permissionUtils';
import { fetchLocationDetails, fetchSuggestions } from './tabs.service';

mapboxgl.accessToken = 'pk.eyJ1IjoiYmh1c2hhbjY1NiIsImEiOiJjbTJ6cXA4OGgwZHIxMm1xMm81aDNyazE1In0.Zm3xPsoxU9RNU9t468gX9Q';

const TabLocation = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const [suggestions, setSuggestions] = useState([]);
  const [selectedLocation, setSelectedLocation] = useState(null);
  const [error, setError] = useState('');
  const [categoriesLoaded, setcategoriesLoaded] = useState(false);
  const [categories, setCategories] = useState([]);
  const [selectedCategory, setSelectedCategory] = useState(null);
  const [subcategories, setSubcategories] = useState([]);
  const [selectedSubcategories, setSelectedSubcategories] = useState([]);
  const [loadingSubcategories, setLoadingSubcategories] = useState(false);
  const [ngoInfo, setNgoInfo] = useState(null);
  const [editableLocation, setEditableLocation] = useState(null);
  const { newNgoId } = useParams();
  const navigate = useNavigate();

  const userInfo = getSessionStorageItem('user');
  const [selectedCategories, setSelelectedCategories] = useState([]);
  const mapContainer = useRef(null);
  const map = useRef(null);
  const sessionToken = '0af7b610-9cae-42a1-892e-c6a1df102ec5';
  useEffect(() => {
    fetchCategories();
    fetchNGODetails();
  }, []);

  const fetchNGODetails = async () => {
    try {
      if (newNgoId) {
        const response = await getLoggedInNGOInfo(newNgoId); // replace with your actual endpoint
        setNgoInfo(response);
        return;
      }
      const response = await getLoggedInNGOInfo(userInfo?.ngo_id); // replace with your actual endpoint
      setNgoInfo(response);
    } catch (error) {
      console.error('Error fetching ngo details:', error);
      toast.error('Failed to fetch ngo details');
    }
  };

  useEffect(() => {
    if (categoriesLoaded) fetchSavedData();
  }, [categoriesLoaded]);

  useEffect(() => {
    if (ngoInfo) {
      setEditableLocation({
        place_name: ngoInfo.place_name || '',
        state: ngoInfo.state || '',
        latitude: ngoInfo.latitude || '',
        longitude: ngoInfo.longitude || '',
        pincode: ngoInfo.pincode || '',
        current_address: ngoInfo.current_address || '',
        country: ngoInfo.country || ''
      });
    }
  }, [ngoInfo]);
  const fetchCategories = async () => {
    try {
      const response = await getAllCategories();
      setCategories(response);
      setcategoriesLoaded(true);
    } catch (error) {
      console.error('Failed to fetch categories:', error);
    }
  };

  const fetchSubcategories = async (categoryId) => {
    setLoadingSubcategories(true);
    try {
      const response = await getSubcategoriesByCategoryId(categoryId);
      setSubcategories(response);
    } catch (error) {
      console.error('Failed to fetch subcategories:', error);
      toast.error('Failed to fetch subcategories');
    } finally {
      setLoadingSubcategories(false);
    }
  };

  const handleCategoryChange = (event, value) => {
    setSelectedCategory(value);
    setSelectedSubcategories([]);
    if (value) {
      fetchSubcategories(value.id);
    } else {
      setSubcategories([]);
    }
  };

  const fetchSavedData = async () => {
    try {
      const ngoId = newNgoId || userInfo?.ngo_id;
      const categoryResponse = await getNGOBasedCategories(ngoId);

      if (!categoryResponse || categoryResponse.length === 0) {
        return;
      }

      const savedCategories = categoryResponse.map((saved) => categories.find((cat) => cat.id === saved.category_id));
      const selectedCategory = savedCategories[0];
      setSelectedCategory(selectedCategory);

      if (selectedCategory) {
        const subcategoryResponse = await getSubcategoriesByCategoryId(selectedCategory.id);
        setSubcategories(subcategoryResponse);

        const savedSubcategories = categoryResponse
          .filter((saved) => saved.category_id === selectedCategory.id)
          .flatMap((saved) => saved.subCategories.split(',').map((sub) => subcategoryResponse.find((subcat) => subcat.name === sub.trim())))
          .filter(Boolean);
        setSelectedSubcategories(savedSubcategories);
      }
    } catch (error) {
      console.error('Failed to fetch saved data:', error);
      toast.error('Failed to fetch saved categories');
    }
  };
  const handleSearchChange = (event) => {
    setSearchQuery(event.target.value);
  };

  const handleSearchSubmit = async () => {
    if (!searchQuery) return;

    try {
      const response = await fetchSuggestions(searchQuery);
      setSuggestions(response);
      setError('');
    } catch (error) {
      setError('Failed to retrieve suggestions. Please try again.');
    }
  };

  const handleSuggestionClick = async (suggestion) => {
    try {
      const locationData = await fetchLocationDetails(suggestion.mapbox_id);
      const stateName = await getStateFromCoordinates(locationData.geometry.coordinates[1], locationData.geometry.coordinates[0]);
      locationData.state = stateName;
      setSelectedLocation(locationData);
      setEditableLocation({
        place_name: locationData.properties.name,
        country: locationData.properties.context.country.name,
        state: stateName,
        latitude: locationData.geometry.coordinates[1],
        longitude: locationData.geometry.coordinates[0],
        pincode: locationData.properties.context.postcode?.name || '',
        current_address: locationData.properties.full_address || ''
      });
      setSuggestions([]);
      setError('');
    } catch (error) {
      setError('Failed to retrieve location details. Please try again.');
    }
  };

  const handleSaveLocation = async () => {
    if (!editableLocation) return;

    const dataToSave = {
      ...ngoInfo,
      pincode: editableLocation.pincode,
      state: editableLocation.state,
      country: editableLocation.country,
      latitude: editableLocation.latitude + '',
      longitude: editableLocation.longitude + '',
      place_name: editableLocation.place_name,
      current_address: editableLocation.current_address
    };

    try {
      if (!newNgoId) {
        await patchLocation(userInfo?.ngo_id, dataToSave);
        toast.success('Location data added successfully');
        setSearchQuery('');
        // setSelectedLocation(null);
        setEditableLocation(null);
        setSuggestions([]);
        return;
      }
      await patchLocation(newNgoId, dataToSave);
      toast.success('Location data added successfully');
      // Reset the search and location data
      setSearchQuery('');
      //   setSelectedLocation(null);
      setEditableLocation(null);
      setSuggestions([]);
    } catch (error) {
      console.error('Failed to save location data:', error);
    }
  };

  const handleSave = async () => {
    if (!selectedCategory || selectedSubcategories.length === 0) {
      toast.error('Please select a category and at least one subcategory');
      return;
    }

    try {
      const ngo_id = newNgoId || userInfo.ngo_id;
      await deleteNGOCategories(ngo_id);

      await insertCategories({
        ngo_id,
        category_id: selectedCategory.id,
        subCategories: selectedSubcategories.map((sub) => sub.name).join(',')
      });

      toast.success('Categories and subcategories saved successfully');
    } catch (error) {
      console.error('Failed to save categories and subcategories:', error);
      toast.error('Failed to save categories and subcategories');
    }
  };

  return (
    <Grid container spacing={3} justifyContent="center">
      <Grid item xs={12} md={6}>
        <MainCard title="Categories">
          {/* <Autocomplete
          multiple
          options={categories}
          getOptionLabel={(option) => option.name}
          value={selectedCategories}
          onChange={(event, newValue) => setSelelectedCategories(newValue)}
          isOptionEqualToValue={(option, value) => option.id === value.id}
          renderInput={(params) => <TextField {...params} variant="outlined" label="Select Categories" placeholder="Categories" />}
        /> */}
          <Grid item xs={12} md={12} sx={{ marginTop: 2 }}>
            <Autocomplete
              options={categories}
              getOptionLabel={(option) => option.name}
              value={selectedCategory}
              onChange={handleCategoryChange}
              renderInput={(params) => <TextField {...params} label="Select Category" />}
            />
          </Grid>
          <Grid item xs={12} md={12} sx={{ marginTop: 2 }}>
            {selectedCategory && (
              <Autocomplete
                multiple
                options={subcategories}
                getOptionLabel={(option) => option.name}
                value={selectedSubcategories}
                onChange={(event, value) => setSelectedSubcategories(value)}
                isOptionEqualToValue={(option, value) => option.id === value.id}
                renderInput={(params) => (
                  <TextField
                    {...params}
                    label={loadingSubcategories ? 'Loading Subcategories...' : 'Select Subcategories'}
                    placeholder="Subcategories"
                  />
                )}
                disabled={!selectedCategory || loadingSubcategories}
              />
            )}
          </Grid>
          <Button
            style={{ marginTop: '1rem' }}
            variant="contained"
            color="primary"
            onClick={handleSave}
            disabled={!selectedCategory || selectedSubcategories.length === 0}
          >
            Save Categories
          </Button>
        </MainCard>
        <ToastContainer />
      </Grid>
      <Grid item xs={12} md={6}>
        <MainCard title="Location Information">
          {/* <Stack spacing={2}> */}
          <Box style={{ marginBottom: '1rem', display: 'flex', gap: '10px' }}>
            <TextField
              label="Search Place"
              placeholder="Enter a location name"
              value={searchQuery}
              onChange={handleSearchChange}
              fullWidth
            />
            <Button style={{ marginRight: '1rem' }} variant="contained" color="primary" onClick={handleSearchSubmit}>
              Search
            </Button>
          </Box>

          {error && <Typography color="error">{error}</Typography>}

          {/* Display suggestions if available */}
          {suggestions.length > 0 && (
            <Box style={{ display: 'flex', flexWrap: 'wrap', gap: '8px', marginBottom: '10px', marginTop: '10px' }}>
              {suggestions.map((suggestion) => (
                <Chip
                  key={suggestion.mapbox_id}
                  label={`${suggestion.name} (${suggestion.place_formatted})`}
                  onClick={() => handleSuggestionClick(suggestion)}
                  clickable
                  color="primary" // Optional: Customize Chip color
                />
              ))}
            </Box>
          )}

          {/* Display selected location details */}
          {editableLocation && (
            <Grid container spacing={2} marginTop={2}>
              <Grid item xs={12} gap={1}>
                <TextField
                  label="Place Name"
                  value={editableLocation.place_name}
                  onChange={(e) => setEditableLocation({ ...editableLocation, place_name: e.target.value })}
                  fullWidth
                />
              </Grid>
              <Grid item xs={6} gap={1}>
                <TextField
                  label="Country"
                  value={editableLocation.country}
                  InputLabelProps={{ shrink: true }}
                  onChange={(e) => setEditableLocation({ ...editableLocation, country: e.target.value })}
                  fullWidth
                />
              </Grid>
              <Grid item xs={6} gap={1}>
                <TextField
                  label="State"
                  value={editableLocation.state}
                  onChange={(e) => setEditableLocation({ ...editableLocation, state: e.target.value })}
                  fullWidth
                />
              </Grid>
              <Grid item xs={6} gap={2}>
                <TextField
                  label="Latitude"
                  value={editableLocation.latitude}
                  onChange={(e) => setEditableLocation({ ...editableLocation, latitude: e.target.value })}
                  fullWidth
                />
              </Grid>
              <Grid item xs={6} gap={1}>
                <TextField
                  label="Longitude"
                  value={editableLocation.longitude}
                  onChange={(e) => setEditableLocation({ ...editableLocation, longitude: e.target.value })}
                  fullWidth
                />
              </Grid>
              <Grid item xs={6} gap={1}>
                <TextField
                  label="Pincode"
                  value={editableLocation.pincode}
                  onChange={(e) => setEditableLocation({ ...editableLocation, pincode: e.target.value })}
                  fullWidth
                />
              </Grid>
              <Grid item xs={6} marginBottom={2}>
                <TextField
                  label="Full Address"
                  value={editableLocation.current_address}
                  onChange={(e) => setEditableLocation({ ...editableLocation, current_address: e.target.value })}
                  fullWidth
                />
              </Grid>
            </Grid>
          )}

          <Button variant="contained" color="secondary" onClick={handleSaveLocation} disabled={!selectedLocation}>
            Save Location
          </Button>

          {/* </Stack> */}
        </MainCard>
      </Grid>
    </Grid>
  );
};

export default TabLocation;
