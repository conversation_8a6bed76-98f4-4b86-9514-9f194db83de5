import PropTypes from 'prop-types';
import { Fragment, useEffect, useMemo, useState } from 'react';

// material-ui
import { alpha, useTheme } from '@mui/material/styles';
import useMediaQuery from '@mui/material/useMediaQuery';
import Button from '@mui/material/Button';
import Divider from '@mui/material/Divider';
import MenuItem from '@mui/material/MenuItem';
import Select from '@mui/material/Select';
import Stack from '@mui/material/Stack';
import Table from '@mui/material/Table';
import TableBody from '@mui/material/TableBody';
import TableCell from '@mui/material/TableCell';
import TableContainer from '@mui/material/TableContainer';
import TableHead from '@mui/material/TableHead';
import TableRow from '@mui/material/TableRow';
import Box from '@mui/material/Box';

// third-party
import {
  flexRender,
  getCoreRowModel,
  getSortedRowModel,
  getPaginationRowModel,
  getFilteredRowModel,
  getExpandedRowModel,
  useReactTable
} from '@tanstack/react-table';
import { rankItem } from '@tanstack/match-sorter-utils';

// project-import
import ScrollX from 'components/ScrollX';
import MainCard from 'components/MainCard';

import {
  CSVExport,
  DebouncedInput,
  HeaderSort,
  RowSelection,
  SelectColumnSorting,
  SelectColumnVisibility,
  TablePagination
} from 'components/third-party/react-table';

import ExpandingUserDetail from 'sections/apps/customer/ExpandingUserDetail';

// assets
import PlusOutlined from '@ant-design/icons/PlusOutlined';
import { COMMON_STATUS_LIST } from 'utils/statusconstans';
import { getCampaignBySearchTerm } from 'api/campaigns.service';
import { CircularProgress } from '@mui/material';
import useAuth from 'hooks/useAuth';
import SearchWithButton from 'components/third-party/react-table/SearchWithButton';

export const fuzzyFilter = (row, columnId, value, addMeta) => {
  // rank the item
  const itemRank = rankItem(row.getValue(columnId), value);

  // store the ranking info
  addMeta(itemRank);

  // return if the item should be filtered in/out
  return itemRank.passed;
};

// ==============================|| REACT TABLE - LIST ||============================== //

export default function CustomCampaignsTable({
  data,
  columns,
  modalToggler,
  category,
  statusList,
  totalCount,
  currentPage,
  pageSize,
  onPageChange,
  onPageSizeChange,
  globalFilter,
  setGlobalFilter,
  campaignType
}) {
  const { user } = useAuth();
  const theme = useTheme();
  const downSM = useMediaQuery(theme.breakpoints.down('sm'));
  const updatedStatusList = statusList?.length > 0 ? statusList : COMMON_STATUS_LIST;

  const initialColumnVisibility = useMemo(() => {
    const visibility = {};
    columns.forEach((column) => {
      visibility[column.accessorKey] = column.showByDefault !== false;
    });
    return visibility;
  }, [columns]);

  const [sorting, setSorting] = useState([
    {
      id: 'id',
      desc: true
    }
  ]);
  const [rowSelection, setRowSelection] = useState({});
  const [statusFilter, setStatusFilter] = useState(updatedStatusList[0]);
  const [columnVisibility, setColumnVisibility] = useState(initialColumnVisibility);
  const [isLoading, setIsLoading] = useState(false);

  const table = useReactTable({
    data: data,
    columns,
    state: {
      sorting,
      rowSelection,
      globalFilter,
      columnVisibility
    },
    initialState: {
      columnVisibility: initialColumnVisibility
    },
    enableRowSelection: true,
    onSortingChange: setSorting,
    onRowSelectionChange: setRowSelection,
    onGlobalFilterChange: setGlobalFilter,
    getRowCanExpand: () => true,
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    getCoreRowModel: getCoreRowModel(),
    getExpandedRowModel: getExpandedRowModel(),
    globalFilterFn: fuzzyFilter,
    debugTable: true,
    onColumnVisibilityChange: setColumnVisibility
  });

  const backColor = alpha(theme.palette.primary.lighter, 0.1);
  let headers = [];
  columns.map(
    (columns) =>
      // @ts-ignore
      columns.accessorKey &&
      headers.push({
        label: typeof columns.header === 'string' ? columns.header : '#',
        // @ts-ignore
        key: columns.accessorKey
      })
  );

  const handlePageChange = (newPage) => {
    onPageChange(newPage + 1);
  };

  const handlePageSizeChange = (newPageSize) => {
    onPageSizeChange(newPageSize);
  };
  return (
    <MainCard content={false}>
      <Stack
        direction={{ xs: 'column', sm: 'row' }}
        spacing={2}
        alignItems="center"
        justifyContent="space-between"
        sx={{ padding: 2, ...(downSM && { '& .MuiOutlinedInput-root, & .MuiFormControl-root': { width: '100%' } }) }}
      >
        {/* <DebouncedInput
            value={globalFilter ?? ''}
            onFilterChange={(value) => setGlobalFilter(String(value))}
            placeholder={`Search ${data.length} records...`}
            /> */}

        <Stack direction={{ xs: 'column', sm: 'row' }} spacing={2} alignItems="center" sx={{ width: '100%' }}>
          {/* Left Side: Filters and Column Visibility */}
          <Stack direction={{ xs: 'column', sm: 'row' }} spacing={2} alignItems="center" sx={{ flexGrow: 1 }}>
            <SearchWithButton value={globalFilter ?? ''} onFilterChange={(value) => setGlobalFilter(String(value))} placeholder="Search" />
            {/* <Select
              value={statusFilter}
              onChange={(event) => setStatusFilter(event.target.value)}
              displayEmpty
              inputProps={{ 'aria-label': 'Status Filter' }}
              className="text-color-grey"
              size="small"
            >
              {updatedStatusList.map((sl) => (
                <MenuItem key={sl} value={sl}>
                  {sl}
                </MenuItem>
              ))}
            </Select> */}
            <SelectColumnVisibility
              {...{
                getVisibleLeafColumns: table.getVisibleLeafColumns,
                getIsAllColumnsVisible: table.getIsAllColumnsVisible,
                getToggleAllColumnsVisibilityHandler: table.getToggleAllColumnsVisibilityHandler,
                getAllColumns: table.getAllColumns
              }}
            />
          </Stack>

          {/* Right Side: Add Button */}
          <Box sx={{ marginLeft: 'auto' }}>
            <Button size="small" variant="contained" startIcon={<PlusOutlined />} onClick={modalToggler}>
              Add {category === 'campaigns' ? 'Campaign' : 'Event'}
            </Button>
          </Box>
        </Stack>

        {/* <Stack direction="row" spacing={2} alignItems="center">
          {category !== 'Role' && category !== undefined && (
            <Button size="small" variant="contained" startIcon={<PlusOutlined />} onClick={modalToggler}>
              Add {category}
            </Button>
          )}
          <CSVExport
              {...{
                data:
                  table.getSelectedRowModel().flatRows.map((row) => row.original).length === 0
                    ? data
                    : table.getSelectedRowModel().flatRows.map((row) => row.original),
                headers,
                filename: `${category}_list.csv`
              }}
            />
        </Stack> */}
      </Stack>
      <ScrollX>
        <Stack>
          <RowSelection selected={Object.keys(rowSelection).length} />
          <TableContainer sx={{ overflowX: 'auto' }}>
            <Table
              size="small"
              sx={{
                minWidth: category == 'campaigns' ? 1700 : 2700, // or use `table.getTotalSize()` if dynamic sizing
                width: '100%',
                tableLayout: 'auto'
              }}
            >
              <TableHead>
                {table.getHeaderGroups().map((headerGroup) => (
                  <TableRow key={headerGroup.id}>
                    {headerGroup.headers.map((header) => {
                      if (header.column.columnDef.meta !== undefined && header.column.getCanSort()) {
                        Object.assign(header.column.columnDef.meta, {
                          className: header.column.columnDef.meta.className + ' cursor-pointer prevent-select'
                        });
                      }

                      return (
                        <TableCell
                          key={header.id}
                          {...header.column.columnDef.meta}
                          onClick={header.column.getToggleSortingHandler()}
                          {...(header.column.getCanSort() &&
                            header.column.columnDef.meta === undefined && {
                              className: 'cursor-pointer prevent-select'
                            })}
                          style={{ textTransform: 'none' }}
                        >
                          {header.isPlaceholder ? null : (
                            <Stack direction="row" spacing={1} alignItems="center">
                              <Box>{flexRender(header.column.columnDef.header, header.getContext())}</Box>
                              {header.column.getCanSort() && <HeaderSort column={header.column} />}
                            </Stack>
                          )}
                        </TableCell>
                      );
                    })}
                  </TableRow>
                ))}
              </TableHead>
              <TableBody>
                {isLoading ? (
                  <TableRow>
                    <TableCell colSpan={table.getVisibleLeafColumns().length} align="center">
                      <CircularProgress />
                      <Box>Loading your data...</Box>
                    </TableCell>
                  </TableRow>
                ) : table.getRowModel().rows.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={table.getVisibleLeafColumns().length} align="center">
                      There are no {category === 'campaigns' ? 'campaigns' : 'events'} available at the moment.
                    </TableCell>
                  </TableRow>
                ) : (
                  table.getRowModel().rows.map((row) => (
                    <Fragment key={row.id}>
                      <TableRow>
                        {row.getVisibleCells().map((cell) => (
                          <TableCell key={cell.id} {...cell.column.columnDef.meta}>
                            {flexRender(cell.column.columnDef.cell, cell.getContext())}
                          </TableCell>
                        ))}
                      </TableRow>
                      {row.getIsExpanded() && (
                        <TableRow
                          sx={{
                            bgcolor: backColor,
                            '&:hover': {
                              bgcolor: `${backColor} !important`
                            }
                          }}
                        >
                          <TableCell colSpan={table.getVisibleLeafColumns().length}>
                            <ExpandingUserDetail data={row.original} />
                          </TableCell>
                        </TableRow>
                      )}
                    </Fragment>
                  ))
                )}
              </TableBody>
            </Table>
          </TableContainer>

          {data?.length > 0 && (
            <>
              <Divider />
              <Box sx={{ p: 2 }}>
                <TablePagination
                  getPageCount={() => Math.ceil(totalCount / pageSize)}
                  setPageIndex={handlePageChange}
                  setPageSize={handlePageSizeChange}
                  getState={() => ({ pagination: { pageIndex: currentPage - 1, pageSize } })}
                  initialPageSize={pageSize}
                />
              </Box>
            </>
          )}
        </Stack>
      </ScrollX>
    </MainCard>
  );
}

CustomCampaignsTable.propTypes = { data: PropTypes.array, columns: PropTypes.array, modalToggler: PropTypes.func };
