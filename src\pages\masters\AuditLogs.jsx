import React, { useEffect, useState } from 'react';

import { toast, ToastContainer } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import useAuth from 'hooks/useAuth';
import dayjs from 'dayjs';
import CustomerTableWithPagination from 'sections/apps/customer/CustomerTableWithPagination';
import { getAllAuditLogs } from 'api/auditlogs.service';
import { Box, Button, CircularProgress, TextField } from '@mui/material';
import MainCard from 'components/MainCard';

export default function CategoriesTable() {
  const { user } = useAuth();
  const [logs, setLogs] = useState([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [totalCount, setTotalCount] = useState(0);
  const [searchQuery, setSearchQuery] = useState('');
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    fetchAuditLogs();
  }, [currentPage, pageSize]);

  const fetchAuditLogs = async () => {
    setLoading(true); // Start loading

    try {
      const response = await getAllAuditLogs(currentPage, pageSize, searchQuery);

      setLogs(response?.records);
      setTotalCount(response?.totalRecords);
    } catch (error) {
      console.error('Failed to fetch audit logs:', error);
      toast.error('Failed to fetch audit logs');
    } finally {
      setLoading(false); // Stop loading after API call
    }
  };

  const columns = [
    {
      accessorKey: 'createdAt',
      header: 'Created At',
      showByDefault: true,
      cell: ({ cell }) => <span>{dayjs(cell.row.original.createdAt).format('DD-MM-YYYY hh:mm A')}</span>
    },
    { accessorKey: 'userInfo.fullname', header: 'User Name', showByDefault: true },
    { accessorKey: 'description', header: 'Description', showByDefault: true },
    { accessorKey: 'type', header: 'Type', showByDefault: true }
  ];

  const handlePageChange = (newPage) => {
    setCurrentPage(newPage);
  };

  const handlePageSizeChange = (newPageSize) => {
    setPageSize(newPageSize);
  };

  return (
    <MainCard>
      <form autoComplete="off">
        <Box sx={{ display: 'flex', gap: 2, mb: 2, width: '100%' }}>
          <TextField
            label="Search User"
            variant="outlined"
            fullWidth
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            onKeyDown={(e) => {
              if (e.key === 'Enter') {
                e.preventDefault();
                fetchAuditLogs();
              }
            }}
          />
          <Button variant="contained" type="button" onClick={fetchAuditLogs} disabled={loading}>
            {loading ? <CircularProgress size={24} sx={{ color: 'white' }} /> : 'Search'}{' '}
          </Button>
        </Box>
      </form>
      {loading ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', my: 3 }}>
          <CircularProgress />
        </Box>
      ) : (
        <CustomerTableWithPagination
          data={logs}
          columns={columns}
          category="AuditLogs"
          currentPage={currentPage}
          totalCount={totalCount}
          pageSize={pageSize}
          onPageChange={handlePageChange}
          onPageSizeChange={handlePageSizeChange}
        />
      )}

      <ToastContainer />
    </MainCard>
  );
}
