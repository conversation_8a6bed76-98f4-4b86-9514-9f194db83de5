import axiosServices from 'utils/axios_node';

const API_BASE_URL = `${import.meta.env.VITE_APP_APPLICATION_API_URL}/api`;

export const fetchRolesService = async () => {
  const response = await axiosServices.get(`${API_BASE_URL}/roles`);
  return response.data;
};

export const addRoleService = async (role) => {
  const response = await axiosServices.post(`${API_BASE_URL}/roles`, role);
  return response.data;
};

export const updateRoleService = async (id, role) => {
  const response = await axiosServices.put(`${API_BASE_URL}/roles/${id}`, role);
  return response.data;
};

export const deleteRoleService = async (id) => {
  const response = await axiosServices.delete(`${API_BASE_URL}/roles/${id}`);
  return response.data;
};
