VITE_APP_VERSION=v0.6.3
GENERATE_SOURCEMAP=false

## Public URL
PUBLIC_URL = https://mantisdashboard.io
VITE_APP_BASE_NAME= /

## Backend API URL
VITE_APP_API_URL=https://mock-data-api-nextjs.vercel.app/
VITE_APP_APPLICATION_API_URL=https://doright.world
VITE_APP_DOUMENT_URL=https://doright.world

## Google Map Key
VITE_APP_GOOGLE_MAPS_API_KEY=AIzaSyAXv4RQK39CskcIB8fvM1Q7XCofZcLxUXw

## Map Box 
VITE_APP_MAPBOX_ACCESS_TOKEN=pk.eyJ1IjoicmFrZXNoLW5ha3JhbmkiLCJhIjoiY2xsNjNkZm0yMGhvcDNlb3phdjF4dHlzeiJ9.ps6azYbr7M3rGk_QTguMEQ

## Firebase - Google Auth 
VITE_APP_FIREBASE_API_KEY=
VITE_APP_FIREBASE_AUTH_DOMAIN=
VITE_APP_FIREBASE_PROJECT_ID=
VITE_APP_FIREBASE_STORAGE_BUCKET=
VITE_APP_FIREBASE_MESSAGING_SENDER_ID=
VITE_APP_FIREBASE_APP_ID=
VITE_APP_FIREBASE_MEASUREMENT_ID=

## AWS
VITE_APP_AWS_POOL_ID=
VITE_APP_AWS_APP_CLIENT_ID=

## Auth0
VITE_APP_AUTH0_CLIENT_ID=
VITE_APP_AUTH0_DOMAIN=

## SupaBase
VITE_SUPABASE_URL=
VITE_SUPABASE_ANON_KEY=

#map box
REACT_APP_MAPBOX_ACCESS_TOKEN=pk.eyJ1IjoiYmh1c2hhbjY1NiIsImEiOiJjbTJ6cXA4OGgwZHIxMm1xMm81aDNyazE1In0.Zm3xPsoxU9RNU9t468gX9Q