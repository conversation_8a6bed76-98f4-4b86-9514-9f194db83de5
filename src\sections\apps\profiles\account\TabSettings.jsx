import { useState, useEffect } from 'react';
// material-ui
import Button from '@mui/material/Button';
import Divider from '@mui/material/Divider';
import Grid from '@mui/material/Grid';
import List from '@mui/material/List';
import ListItem from '@mui/material/ListItem';
import ListItemText from '@mui/material/ListItemText';
import Stack from '@mui/material/Stack';
import Switch from '@mui/material/Switch';
import Typography from '@mui/material/Typography';
import 'react-toastify/dist/ReactToastify.css';
import { toast, ToastContainer } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';

// project import
import MainCard from 'components/MainCard';
import { deleteAllByNgoId, getNgoSettings, getSettingsMasterInfo, updateNgoSettings } from './settings.service';
import useAuth from 'hooks/useAuth';
import { useParams } from 'react-router';
import { Box, CircularProgress } from '@mui/material';
// ==============================|| ACCOUNT PROFILE - SETTINGS ||============================== //

export default function TabSettings() {
  const { user } = useAuth();
  const [settings, setSettings] = useState([]);
  const [isEditMode, setIsEditMode] = useState(true);
  const [checked, setChecked] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const { newNgoId } = useParams();

  useEffect(() => {
    fetchData();
  }, []);

  const fetchData = async () => {
    setIsLoading(true);

    try {
      const masterData = await getSettingsMasterInfo('SettingsTab');
      const ngo_id = newNgoId ? newNgoId : user?.ngo_id;
      const ngoData = await getNgoSettings(ngo_id, 'SettingsTab');
      const mergedSettings = masterData.map((master) => {
        const ngoSetting = ngoData.find((ngo) => ngo.master_setting_id === master.id);
        return {
          ...master,
          value: ngoSetting ? ngoSetting.value : master.default_value
        };
      });
      setSettings(mergedSettings);
      setChecked(mergedSettings.filter((s) => s.value === 'yes').map((s) => s.slug));
    } catch (error) {
      console.error(error);
      toast.error('Failed to load settings');
    } finally {
      setIsLoading(false);
    }
  };
  const handleToggle = (value) => () => {
    if (!isEditMode) return;
    setChecked((prev) => (prev.includes(value) ? prev.filter((item) => item !== value) : [...prev, value]));
  };
  const handleEditModeToggle = () => {
    if (isEditMode) {
      handleSave();
    } else {
      setIsEditMode(true);
    }
  };

  const handleSave = async () => {
    try {
      const updatedSettings = settings.map((setting) => ({
        master_setting_id: setting.id,
        value: checked.includes(setting.slug) ? 'yes' : 'no'
      }));
      const ngo_id = newNgoId ? newNgoId : user?.ngo_id;

      await deleteAllByNgoId(ngo_id);
      await updateNgoSettings(updatedSettings, ngo_id, 'SettingsTab');
      await fetchData();
      toast.success('Settings updated successfully!');
      // setIsEditMode(false);
    } catch (error) {
      console.error(error);
      toast.error('Failed to update settings');
    }
  };
  const renderSwitch = (slug, label) => (
    <ListItem>
      <ListItemText primary={<Typography color="secondary">{label}</Typography>} />
      <Switch
        edge="end"
        onChange={handleToggle(slug)}
        checked={checked.includes(slug)}
        // disabled={!isEditMode}
        inputProps={{
          'aria-labelledby': `switch-list-label-${slug}`
        }}
      />
    </ListItem>
  );
  if (isLoading) {
    return (
      <Box sx={{ display: 'flex', flexDirection: 'column', justifyContent: 'center', alignItems: 'center' }}>
        <CircularProgress />
        <Typography sx={{ mt: 2 }}>Loading Your Settings....</Typography>
      </Box>
    );
  }
  return (
    <Grid container spacing={3}>
      <Grid item xs={12} sm={6}>
        <Grid container spacing={3}>
          <Grid item xs={12}>
            <MainCard title="Email Settings">
              <Stack spacing={2.5}>
                <Typography variant="subtitle1">Setup Email Notification</Typography>
                <List sx={{ p: 0, '& .MuiListItem-root': { p: 0, py: 0.25 } }}>
                  {renderSwitch('email-notification', 'Email Notification')}
                  {renderSwitch('send_copy_to_mail', 'Send Copy To Personal Email')}
                </List>
              </Stack>
            </MainCard>
          </Grid>

          <Grid item xs={12}>
            <MainCard title="Updates from System Notification">
              <Stack spacing={2.5}>
                <Typography variant="subtitle1">Email you with?</Typography>
                <List sx={{ p: 0, '& .MuiListItem-root': { p: 0, py: 0.25 } }}>
                  {renderSwitch('new_activity', 'Activity: New Donations, Updates')}
                  {renderSwitch('platform_change', 'System: Platform Changes')}
                </List>
              </Stack>
            </MainCard>
          </Grid>
        </Grid>
      </Grid>

      <Grid item xs={12} sm={6}>
        <MainCard title="Privacy & Permissions">
          <Stack spacing={2.5}>
            <Typography variant="subtitle1">Manage Data Visibility</Typography>
            <List sx={{ p: 0, '& .MuiListItem-root': { p: 0, py: 0.25 } }}>
              {renderSwitch('allow_visibility', 'Allow visibility of my profile to donors or public')}
            </List>
          </Stack>
        </MainCard>
      </Grid>

      <Grid item xs={12} sm={6}>
        <MainCard title="Activity Related Emails">
          <Stack spacing={2.5}>
            <Typography variant="subtitle1">When to email?</Typography>
            <List sx={{ p: 0, '& .MuiListItem-root': { p: 0, py: 0.25 } }}>
              {renderSwitch('new_notifications', 'Have new notifications')}
              {renderSwitch('direct_messages', "You're sent a direct message")}
            </List>
            <Divider />
          </Stack>
        </MainCard>
      </Grid>

      <Grid item xs={12}>
        <Stack direction="row" justifyContent="flex-end" alignItems="center" spacing={2}>
          {/* <Button variant="outlined" color="secondary" onClick={() => setIsEditMode(false)} disabled={!isEditMode}>
            Cancel
          </Button> */}
          <Button variant="contained" onClick={handleEditModeToggle}>
            {isEditMode ? 'Update Settings' : 'Edit Settings'}
          </Button>
        </Stack>
      </Grid>
      <ToastContainer />
    </Grid>
  );
}
