import axiosServices from 'utils/axios_node';

const API_BASE_URL = `${import.meta.env.VITE_APP_APPLICATION_API_URL}/api`;

export const fetchSkillsService = async () => {
  const response = await axiosServices.get(`${API_BASE_URL}/skills`);
  return response.data;
};

export const addSkillService = async (skill) => {
  const response = await axiosServices.post(`${API_BASE_URL}/skills`, skill);
  return response.data;
};

export const updateSkillService = async (id, skill) => {
  const response = await axiosServices.put(`${API_BASE_URL}/skills/${id}`, skill);
  return response.data;
};

export const deleteSkillService = async (id) => {
  const response = await axiosServices.delete(`${API_BASE_URL}/skills/${id}`);
  return response.data;
};
