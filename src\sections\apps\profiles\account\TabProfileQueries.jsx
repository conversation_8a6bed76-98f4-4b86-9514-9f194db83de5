import React, { useEffect, useState } from 'react';
import { getProfileQueries, updateProfileQueries } from 'sections/apps/profiles/account/tabs.service';
import {
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  CircularProgress,
  Typography,
  Box,
  Tooltip,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  DialogActions,
  Button,
  Chip
} from '@mui/material';
import useAuth from 'hooks/useAuth';
import { AuditOutlined, EditOutlined } from '@ant-design/icons';
import { useNavigate } from 'react-router';
import { toast, ToastContainer } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import dayjs from 'dayjs';
import CustomerTable from 'sections/apps/customer/CustomerTable';

const statusColors = {
  Pending: '#FFC107', // Yellow
  'In Progress': '#17A2B8', // Blue
  Completed: '#28A745', // Green
  Rejected: '#DC3545' // Red
};

const QueryList = () => {
  const { user } = useAuth();
  const [queries, setQueries] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const navigate = useNavigate();

  const [openDialog, setOpenDialog] = useState(false);
  const [selectedQuery, setSelectedQuery] = useState(null);
  const [newStatus, setNewStatus] = useState('');

  const statusOptions = ['Pending', 'In Progress', 'Completed', 'Rejected'];
  const showNgoNameAndActions = user?.roleInfo?.name?.startsWith('DR');

  useEffect(() => {
    fetchQueries();
  }, []);

  const fetchQueries = async () => {
    try {
      const response = await getProfileQueries(user?.ngo_id, user?.roleInfo?.name === 'DR_Management' ? null : user?.id,"profile-queries");
      setQueries(response);
    } catch (err) {
      setError('Failed to fetch queries. Please try again later.');
    } finally {
      setLoading(false);
    }
  };

  const handleOpenDialog = (query) => {
    setSelectedQuery(query);
    setNewStatus(query.status);
    setOpenDialog(true);
  };

  const handleCloseDialog = () => {
    setOpenDialog(false);
    setSelectedQuery(null);
    setNewStatus('');
  };

  const handleUpdateStatus = async () => {
    if (!selectedQuery) return;

    try {
      const payload = {
        ...selectedQuery,
        status: newStatus
      };

      await updateProfileQueries(selectedQuery.id, payload);
      handleCloseDialog();
      toast.success('Status updated successfully ');

      fetchQueries();
    } catch (error) {
      console.error('Error updating status:', error);
    }
  };

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" height="200px">
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Typography color="error" align="center">
        {error}
      </Typography>
    );
  }

  const columns = [
    ...(showNgoNameAndActions
      ? [
          {
            accessorKey: 'name',
            header: 'Ngo Name',
            showByDefault: true,
            cell: ({ cell }) => (
              <Button variant="text" onClick={() => navigate(`/masters/ngos/edit/ngoprofile/${cell.row.original?.ngoInfo?.id}`)}>
                {cell.row.original.ngoInfo.name}
              </Button>
            ),
            meta: {
              style: { width: '20%' } // Fix column width to 10%
            }
          }
        ]
      : []),

    { accessorKey: 'description', header: 'Your Request Description', showByDefault: true },

    {
      accessorKey: 'status',
      header: 'Status',
      showByDefault: true,

      cell: ({ cell }) => {
        const status = cell.getValue();
        const chipColor = statusColors[status] || '#6c757d';

        return (
          <Chip
            label={status}
            size="small"
            sx={{
              backgroundColor: chipColor,
              color: '#fff', // White text for contrast
              fontWeight: 500
            }}
          />
        );
      },
      meta: {
        style: { width: '10%' } // Fix column width to 10%
      }
    },

    ...(showNgoNameAndActions
      ? [
          {
            id: 'actions',
            header: 'Action',
            cell: ({ row }) => {
              const ngoId = row.original?.ngoInfo?.id;
              return (
                <Box display="flex" alignItems="center" gap={1}>
                  <Tooltip title="Edit">
                    <IconButton size="medium" color="primary" onClick={() => navigate(`/masters/ngos/edit/ngoprofile/${ngoId}`)}>
                      <EditOutlined fontSize="large" />
                    </IconButton>
                  </Tooltip>

                  <Tooltip title="Change Status">
                    <IconButton color="success" size="medium" onClick={() => handleOpenDialog(row.original)}>
                      <AuditOutlined fontSize="large" />
                    </IconButton>
                  </Tooltip>
                </Box>
              );
            },
            meta: {
              style: { width: '10%' } // Fix column width to 10%
            }
          }
        ]
      : []),
    {
      accessorKey: 'createdAt',
      header: 'Created On',
      showByDefault: true,
      cell: (cell) => {
        const formattedDate = dayjs(cell.row.original.createdAt).format('DD-MM-YYYY');
        return <span>{formattedDate}</span>;
      },
      meta: {
        style: { width: '10%' } // Fix column width to 10%
      }
    },
    {
      accessorKey: 'updatedAt',
      header: 'Last Updated',
      showByDefault: true,
      cell: (cell) => {
        const formattedDate = dayjs(cell.row.original.updatedAt).format('DD-MM-YYYY');
        return <span>{formattedDate}</span>;
      },
      meta: {
        style: { width: '10%' } // Fix column width to 10%
      }
    }
  ];

  return (
    <>
      <CustomerTable data={queries} columns={columns} category={'ProfileQueries'} />

      <Dialog
        open={openDialog}
        onClose={(event, reason) => {
          if (reason !== 'backdropClick' && reason !== 'escapeKeyDown') {
            handleCloseDialog();
          }
        }}
        maxWidth="50%"
        PaperProps={{
          sx: {
            top: 0,
            position: 'absolute',
            borderRadius: '16px',
            width: '100%',
            maxWidth: '600px'
          }
        }}
      >
        <DialogTitle>Change Status</DialogTitle>
        <DialogContent>
          <FormControl fullWidth sx={{ marginTop: 1 }}>
            <InputLabel>Status</InputLabel>
            <Select value={newStatus} onChange={(e) => setNewStatus(e.target.value)}>
              {statusOptions.map((status) => (
                <MenuItem key={status} value={status}>
                  {status}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDialog} color="secondary">
            Cancel
          </Button>
          <Button onClick={handleUpdateStatus} color="primary" variant="contained">
            Update
          </Button>
        </DialogActions>
      </Dialog>
      <ToastContainer autoClose={6000} />
    </>
  );
};

export default QueryList;
