import React, { useEffect, useState } from 'react';
import { getLoggedInNGOInfo, updateUsingPatchNGO } from '../profile.service';
import { useParams, useNavigate } from 'react-router';

// material-ui;
import {
  Avatar,
  Card,
  CardContent,
  CardHeader,
  Chip,
  Divider,
  Grid,
  Typography,
  Link,
  Button,
  TextField,
  List,
  ListItemText,
  ListItem,
  Box,
  CircularProgress
} from '@mui/material';

// project import
import MainCard from 'components/MainCard';
import { ToastContainer, toast } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import { API_BASE_URL } from 'api/campaigns.service';
import axios from 'axios';
import useAuth from 'hooks/useAuth';
import dayjs from 'dayjs';
import FeedsCard from 'sections/widget/data/FeedsCard';
import { getSessionStorageItem } from 'utils/permissionUtils';
import { getNotifications } from 'pages/masters/apis/notification.service';

// ==============================|| ACCOUNT PROFILE - SETTINGS ||============================== //

export default function TabSettings() {
  const userInfo = getSessionStorageItem('user');
  const { user } = useAuth();
  const [isLoading, setIsLoading] = useState(true);

  const [comments, setComments] = useState([]);
  const [isEditing, setIsEditing] = useState(true);
  const [ngoDetails, setNgoDetails] = useState({
    description: '',
    ngo_id: ''
  });

  const { newNgoId } = useParams();
  const navigate = useNavigate();

  useEffect(() => {
    if (user?.ngo_id) {
      fetchComments(user?.ngo_id);
    }
  }, []);

  const fetchComments = async (newNgoId) => {
    setIsLoading(true);

    try {
      const response = await getNotifications(newNgoId, 'ngo');
      setComments(response?.data || []);
    } catch (error) {
      console.error('Failed to fetch comments:', error);
      toast.error('Failed to fetch comments');
    } finally {
      setIsLoading(false);
    }
  };

  const handleEditClick = (e) => {
    e.preventDefault();
    setIsEditing(true);
  };
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setNgoDetails((prev) => ({ ...prev, [name]: value }));
  };

  if (isLoading) {
    return (
      <Box sx={{ display: 'flex', flexDirection: 'column', justifyContent: 'center', alignItems: 'center' }}>
        <CircularProgress />
        <Typography sx={{ mt: 2 }}>Loading Your Comments....</Typography>
      </Box>
    );
  }

  return (
    // <Card>
    //   <CardHeader
    //     avatar={<Avatar src="/placeholder.svg" alt={ngoInfo.name} sx={{ width: 60, height: 60 }} />}
    //     title={<Typography variant="h1">{ngoInfo.name}</Typography>}
    //     subheader={<Chip label={ngoInfo.ngo_status} color="primary" variant="outlined" size="medium" sx={{ mt: 1 }} />}
    //   />
    //   <CardContent>
    //     <Typography variant="h3" gutterBottom>
    //       1. Basic Information
    //     </Typography>
    //     <Grid container spacing={2}>
    //       <Grid item xs={12} sm={6}>
    //         <Typography variant="subtitle1">NGO Name</Typography>
    //         <Typography variant="body1">{ngoInfo.name}</Typography>
    //       </Grid>
    //       <Grid item xs={12} sm={6}>
    //         <Typography variant="subtitle1">Contact Person Name</Typography>
    //         <Typography variant="body1">{ngoInfo.point_of_contact_name}</Typography>
    //       </Grid>
    //       <Grid item xs={12} sm={6}>
    //         <Typography variant="subtitle1">Email</Typography>
    //         <Typography variant="body1">{ngoInfo.email}</Typography>
    //       </Grid>
    //       <Grid item xs={12} sm={6}>
    //         <Typography variant="subtitle1">Mobile Number</Typography>
    //         <Typography variant="body1">{ngoInfo.point_of_contact_mobile_number}</Typography>
    //       </Grid>
    //       <Grid item xs={12} sm={6}>
    //         <Typography variant="subtitle1">PAN</Typography>
    //         <Typography variant="body1">{ngoInfo.pan}</Typography>
    //       </Grid>
    //     </Grid>

    //     <Divider sx={{ my: 2 }} />

    //     <Typography variant="h3" gutterBottom>
    //       2. About NGO
    //     </Typography>
    //     <Grid container spacing={2}>
    //       <Grid item xs={12}>
    //         <Typography variant="subtitle1">Vision</Typography>
    //         <Typography variant="body1">{ngoInfo.vision}</Typography>
    //       </Grid>
    //       <Grid item xs={12}>
    //         <Typography variant="subtitle1">Mission</Typography>
    //         <Typography variant="body1">{ngoInfo.mission}</Typography>
    //       </Grid>
    //       <Grid item xs={12}>
    //         <Typography variant="subtitle1">About Us</Typography>
    //         <Typography variant="body1">{ngoInfo.about_us}</Typography>
    //       </Grid>
    //       <Grid item xs={12} sm={6}>
    //         <Typography variant="subtitle1">Website URL</Typography>
    //         <Link href={ngoInfo.website_url} target="_blank" rel="noopener noreferrer">
    //           {ngoInfo.website_url}
    //         </Link>
    //       </Grid>
    //       <Grid item xs={12} sm={6}>
    //         <Typography variant="subtitle1">Darpan ID</Typography>
    //         <Typography variant="body1">{ngoInfo.darpan_id}</Typography>
    //       </Grid>
    //     </Grid>

    //     <Divider sx={{ my: 2 }} />

    //     <Typography variant="h3" gutterBottom>
    //       3. Categories and Location
    //     </Typography>
    //     <Grid container spacing={2}>
    //       <Grid item xs={12} sm={6}>
    //         <Typography variant="subtitle1">Type of NGO</Typography>
    //         <Typography variant="body1">{ngoInfo.type_of_ngo}</Typography>
    //       </Grid>
    //       <Grid item xs={12} sm={6}>
    //         <Typography variant="subtitle1">Pincode</Typography>
    //         <Typography variant="body1">{ngoInfo.pincode}</Typography>
    //       </Grid>
    //       <Grid item xs={12} sm={6}>
    //         <Typography variant="subtitle1">State</Typography>
    //         <Typography variant="body1">{ngoInfo.state}</Typography>
    //       </Grid>
    //       <Grid item xs={12} sm={6}>
    //         <Typography variant="subtitle1">Latitude</Typography>
    //         <Typography variant="body1">{ngoInfo.latitude}</Typography>
    //       </Grid>
    //       <Grid item xs={12} sm={6}>
    //         <Typography variant="subtitle1">Longitude</Typography>
    //         <Typography variant="body1">{ngoInfo.longitude}</Typography>
    //       </Grid>
    //       <Grid item xs={12} sm={6}>
    //         <Typography variant="subtitle1">Date of Establishment</Typography>
    //         <Typography variant="body1">
    //           {ngoInfo.date_of_establishment ? new Date(ngoInfo.date_of_establishment).toLocaleDateString() : '-'}
    //         </Typography>
    //       </Grid>
    //     </Grid>
    //     <Divider sx={{ my: 2 }} />
    //     {!newNgoId && (ngoInfo.ngo_status === 'Pending' || ngoInfo.ngo_status === 'New') && (
    //       <Grid item xs={12}>
    //         <Button onClick={handleSubmitReview} variant="contained" color="primary" sx={{ mr: 2 }}>
    //           Submit for Review
    //         </Button>
    //       </Grid>
    //     )}
    //   </CardContent>
    //   <ToastContainer autoClose={6000} />
    // </Card>
    <Card>
      <CardContent>
        <FeedsCard url={`${API_BASE_URL}/ngo-notifications/${user.ngo_id}`} />
      </CardContent>
      <ToastContainer autoClose={6000} />
    </Card>
  );
}
