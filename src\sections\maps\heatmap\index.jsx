import PropTypes from 'prop-types';
import { useState, useEffect, useMemo, memo } from 'react';

// third-party
import Map, { Source, Layer } from 'react-map-gl';

// project-import
import ControlPanel from './control-panel';
import heatmapLayer from './map-style';
import { getData } from 'api/map.service';

// tpyes

// ==============================|| MAPBOX - HEATMAP ||============================== //

function Heatmap({ ...other }) {
  const [allDays, useAllDays] = useState(true);
  const [timeRange, setTimeRange] = useState([0, 0]);
  const [selectedTime, selectTime] = useState(0);
  const [earthquakes, setEarthQuakes] = useState();
  const [geoJsonData, setGeoJsonData] = useState(null);

  useEffect(() => {
    const fetchData = async () => {
      try {
        const url = 'http://localhost:4000/api/campaigns/status/Live';
        const response = await getData(url);
        const earthquakes = response.data;

        // Transform data to GeoJSON
        const geoJson = {
          type: 'FeatureCollection',
          crs: {
            type: 'name',
            properties: { name: 'urn:ogc:def:crs:OGC:1.3:CRS84' }
          },
          features: response
            .filter((quake) => quake.longitude !== null && quake.latitude !== null && !isNaN(quake.longitude) && !isNaN(quake.latitude))
            .map((quake) => ({
              type: 'Feature',
              geometry: {
                type: 'Point',
                coordinates: [parseFloat(quake.longitude), parseFloat(quake.latitude), quake.depth || 0]
              },
              properties: {
                id: quake.id || null,
                mag: quake.magnitude || 0,
                time: quake.time || 0,
                felt: quake.felt || null,
                tsunami: quake.tsunami || 0
              }
            }))
        };

        setGeoJsonData(geoJson);
      } catch (error) {
        console.error('Error fetching heatmap data:', error);
      }
    };

    fetchData();
  }, []);

  useEffect(() => {
    fetch('https://docs.mapbox.com/mapbox-gl-js/assets/earthquakes.geojson')
      .then((resp) => resp.json())
      .then((json) => {
        const { features } = json;
        const endTime = features[0].properties.time;
        const startTime = features[features.length - 1].properties.time;
        setTimeRange([startTime, endTime]);
        setEarthQuakes(json);
        selectTime(endTime);
      })
      .catch((error) => console.error('Could not load data', error));
  }, []);

  const data = useMemo(
    () => (allDays ? earthquakes : filterFeaturesByDay(earthquakes, selectedTime)),
    [earthquakes, allDays, selectedTime]
  );

  return (
    <>
      <Map initialViewState={{ latitude: 40, longitude: -100, zoom: 3 }} {...other}>
        {data && (
          <Source type="geojson" data={geoJsonData}>
            <Layer {...heatmapLayer} />
          </Source>
        )}
      </Map>
      <ControlPanel
        startTime={timeRange[0]}
        endTime={timeRange[1]}
        selectedTime={selectedTime}
        allDays={allDays}
        onChangeTime={selectTime}
        onChangeAllDays={useAllDays}
      />
    </>
  );
}

export default memo(Heatmap);

function filterFeaturesByDay(featureCollection, time) {
  const date = new Date(time);
  const year = date.getFullYear();
  const month = date.getMonth();
  const day = date.getDate();

  const features = featureCollection?.features.filter((feature) => {
    const featureDate = new Date(feature.properties?.time);
    return featureDate.getFullYear() === year && featureDate.getMonth() === month && featureDate.getDate() === day;
  });
  return { type: 'FeatureCollection', features };
}

Heatmap.propTypes = { other: PropTypes.any };
