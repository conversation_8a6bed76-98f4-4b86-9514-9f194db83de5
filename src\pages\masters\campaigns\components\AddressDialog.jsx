import React from 'react';
import { Dialog, DialogTitle, DialogContent, DialogActions, Typography, Button, Box, Grid, CircularProgress } from '@mui/material';
import LocationMap from 'components/LocationMap';
// If your LocationMap is not already a component, we can modularize that too.

const AddressDialog = ({ isOpen, onClose, isLoaded, markerPosition, onMarkerDragEnd, addressInformation = {}, campaignData = {} }) => {
  return (
    <Dialog open={isOpen} onClose={onClose} fullWidth maxWidth="sm">
      <DialogTitle>
        <Typography
          variant="h5"
          sx={{
            fontWeight: 'bold',
            textAlign: 'center',
            color: 'primary',
            pb: 2,
            borderBottom: '1px solid',
            borderColor: 'divider'
          }}
        >
          Please place the pin as accurately as possible at your exact location📍
        </Typography>
      </DialogTitle>
      <DialogContent
        sx={{
          py: 4,
          px: 3,
          backgroundColor: 'background.default'
        }}
      >
        {isLoaded ? (
          <Box
            sx={{
              height: 300,
              borderRadius: 2,
              overflow: 'hidden',
              mb: 3,
              boxShadow: 3
            }}
          >
            <LocationMap center={markerPosition} onMarkerDragEnd={onMarkerDragEnd} />
          </Box>
        ) : (
          <Box
            sx={{
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
              height: 300,
              mb: 3
            }}
          >
            <CircularProgress />
          </Box>
        )}

        <Box
          sx={{
            px: 2,
            py: 3,
            borderRadius: 2,
            backgroundColor: 'background.paper',
            boxShadow: 1
          }}
        >
          <Typography
            variant="subtitle1"
            sx={{
              fontWeight: 600,
              mb: 2,
              borderBottom: '1px solid',
              borderColor: 'divider',
              pb: 1
            }}
          >
            Address Details
          </Typography>

          <Grid container spacing={2}>
            <Grid item xs={12}>
              <Typography variant="body1" sx={{ mb: 1 }}>
                <strong>Full Address:</strong> {addressInformation?.current_address || campaignData?.current_address || 'N/A'}
              </Typography>
            </Grid>

            <Grid item xs={6}>
              <Typography variant="body1">
                <strong>Country:</strong> {addressInformation?.country || campaignData?.country || 'N/A'}
              </Typography>
            </Grid>

            <Grid item xs={6}>
              <Typography variant="body1">
                <strong>State:</strong> {addressInformation?.state || campaignData?.state || 'N/A'}
              </Typography>
            </Grid>

            <Grid item xs={6}>
              <Typography variant="body1">
                <strong>City:</strong> {addressInformation?.place_name || campaignData?.place_name || 'N/A'}
              </Typography>
            </Grid>

            <Grid item xs={6}>
              <Typography variant="body1">
                <strong>Pincode:</strong> {addressInformation?.pincode || campaignData?.pincode || 'N/A'}
              </Typography>
            </Grid>
          </Grid>
        </Box>
      </DialogContent>

      <DialogActions
        sx={{
          justifyContent: 'center',
          py: 2,
          backgroundColor: 'background.default'
        }}
      >
        <Button
          onClick={onClose}
          color="secondary"
          sx={{
            textTransform: 'none',
            borderRadius: 3,
            fontSize: '1rem',
            px: 3
          }}
        >
          Cancel
        </Button>
        <Button
          onClick={onClose}
          variant="contained"
          color="primary"
          sx={{
            textTransform: 'none',
            borderRadius: 3,
            fontSize: '1rem',
            px: 3,
            ml: 2
          }}
        >
          Confirm and Proceed
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default AddressDialog;
