import axiosServices from 'utils/axios_node';

const API_BASE_URL = `${import.meta.env.VITE_APP_APPLICATION_API_URL}/api`;

export const getAllUsers = async () => {
  try {
    const response = await axiosServices.get(`${API_BASE_URL}/users`);
    return response.data;
  } catch (error) {
    throw new Error('Failed to fetch users');
  }
};

export const addUser = async (user) => {
  try {
    const response = await axiosServices.post(`${API_BASE_URL}/users`, user);
    return response.data;
  } catch (error) {
    throw new Error('Failed to add user');
  }
};

export const editUser = async (id, user) => {
  try {
    const response = await axiosServices.put(`${API_BASE_URL}/users/${id}`, user);
    return response.data;
  } catch (error) {
    throw new Error('Failed to update user');
  }
};

export const deleteUser = async (id) => {
  try {
    const response = await axiosServices.delete(`${API_BASE_URL}/users/${id}`);
    return response.data;
  } catch (error) {
    throw new Error('Failed to delete user');
  }
};
