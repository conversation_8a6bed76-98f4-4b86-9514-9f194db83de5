import React, { Fragment, useEffect, useState } from 'react';
import PropTypes from 'prop-types';
import { useFormik } from 'formik';
import * as yup from 'yup';
import {
  Button,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Stack,
  Typography,
  Grid,
  FormHelperText,
  RadioGroup,
  FormControlLabel,
  Radio,
  Divider,
  Autocomplete,
  Chip,
  Box
} from '@mui/material';
import AnimateButton from 'components/@extended/AnimateButton';
import { FormattedMessage } from 'react-intl';
import { useParams } from 'react-router';
import { CloseOutlined } from '@ant-design/icons';
import KindDynamicInputList from './KindDynamicInput';
import { getKindDonationByCampaignId } from 'api/campaigns.service';

// Validation schema for Additional Details
const validationSchema = yup.object({
  impact_goal: yup.string().required('Impact Goal is required'),
  fund_raising_target: yup.number().when('donor_target_type', {
    is: (value) => value === 'monetary_donation',
    then: () => yup.number().required('Please specify your fund-raising target.').min(1, 'The fund-raising target must be at least 1')
  }),

  donor_target_type: yup.string().required('Donor Target Type is required'),
  no_of_volunteers: yup.number().when('volunteers_required', {
    is: 'yes',
    then: () =>
      yup.number().required('Please specify the number of volunteers required').min(1, 'The number of volunteers must be at least 1'),
    otherwise: () => yup.number().nullable()
  })
  //   skills: yup
  //     .array()
  //     .of(yup.string())
  //     .when('volunteers_required', {
  //       is: (value) => value === 'yes', // Only apply the validation when volunteers_required is 'yes'
  //       then: yup
  //         .array()
  //         .min(1, 'At least one skill must be selected') // Ensure at least one item is selected
  //         .required('Selection is required'), // Ensure the array is not empty
  //       otherwise: yup.array().notRequired() // If volunteers_required is not 'yes', the array is not required
  //     })
});

export default function TargetImpact({
  targetImpactInformation,
  setTargetImpactInformation,
  handleNext,
  handleBack,
  handleSubmit,
  userSkills,
  itemOptions,
  setErrorIndex
}) {
  // Formik setup
  const { campaignId } = useParams();
  const [inputList, setInputList] = useState(
    targetImpactInformation?.inKindList?.length > 0 ? targetImpactInformation?.inKindList : [{ item: null, quantity: '', unit: '' }]
  );

  useEffect(() => {
    const fetchKindDonation = async () => {
      try {
        const response = await getKindDonationByCampaignId(campaignId);
        const inKindList = response.map((item) => ({
          item: item.item_name,
          quantity: item.quantity,
          unit: item.unit_of_measure
        }));
        setInputList(inKindList.length > 0 ? inKindList : [{ item: null, quantity: '', unit: '' }]);
      } catch (error) {
        console.error(error);
      }
    };
    fetchKindDonation();
  }, [campaignId]);

  const formik = useFormik({
    initialValues: {
      impact_goal: targetImpactInformation.impact_goal || '',
      fund_raising_target: targetImpactInformation.fund_raising_target || 0,
      donor_target_type: targetImpactInformation.donor_target_type || 'monetary_donation',
      volunteers_required: targetImpactInformation.volunteers_required || 'no',
      no_of_volunteers: targetImpactInformation.no_of_volunteers || 0,
      skills: targetImpactInformation?.skills ? targetImpactInformation.skills.split(',') : [],
      inKindList: targetImpactInformation?.inKindList
    },
    validationSchema,
    onSubmit: (values) => {
      if (values.donor_target_type === 'in_kind') {
        values.inKindList = inputList;
      }
      setTargetImpactInformation({
        impact_goal: values.impact_goal || '',
        fund_raising_target: values.fund_raising_target || 0,
        donor_target_type: values.donor_target_type || '',
        volunteers_required: values.volunteers_required || 'no',
        no_of_volunteers: values.no_of_volunteers || 0,
        skills: values.skills?.join(',') || '',
        inKindList: values.inKindList || []
      });
      handleNext(values);
    }
  });
  return (
<Box sx={{ pl: 1 }}>      {/* <Typography variant="h5" gutterBottom sx={{ mb: 2 }}>
        Additional Information
      </Typography> */}
      <form onSubmit={formik.handleSubmit} id="validation-forms" autoComplete="off">
        <Grid container spacing={3}>
          <Grid item xs={12} sm={12}>
            <Stack spacing={1}>
              <Typography variant="h5" gutterBottom sx={{ mb: 2 }}>
                Contribution Type *
              </Typography>
              <RadioGroup
                aria-label="Donation Type"
                defaultValue={formik.values.donor_target_type}
                name="donor_target_type"
                row
                onChange={formik.handleChange}
              >
                <FormControlLabel value="monetary_donation" control={<Radio />} label="Monetary donation" />
                <FormControlLabel value="in_kind" control={<Radio />} label="In-kind" />
              </RadioGroup>
            </Stack>
          </Grid>

          <Grid item xs={12} sm={4}>
            <Stack spacing={1}>
              <TextField
                label="Impact goal"
                required
                fullWidth
                name="impact_goal"
                value={formik.values.impact_goal}
                onChange={formik.handleChange}
                error={formik.touched.impact_goal && Boolean(formik.errors.impact_goal)}
                helperText={formik.touched.impact_goal && formik.errors.impact_goal}
                margin="normal"
              />
            </Stack>
          </Grid>
          {formik.values.donor_target_type === 'monetary_donation' && (
            <Grid item xs={12} sm={4}>
              <Stack spacing={1}>
                <TextField
                  label="Fund raising target"
                  fullWidth
                  required
                  type="number"
                  name="fund_raising_target"
                  value={formik.values.fund_raising_target}
                  // onChange={(event) => {
                  //   const value = event.target.value;
                  //   if (/^\d*$/.test(value)) { // Allow only numeric values
                  //     formik.handleChange(event); // Update Formik state
                  //   }
                  // }}
                  onChange={formik.handleChange}
                  error={formik.touched.fund_raising_target && Boolean(formik.errors.fund_raising_target)}
                  helperText={formik.touched.fund_raising_target && formik.errors.fund_raising_target}
                  margin="normal"
                  inputProps={{ min: 1 }}
                />
                <Typography variant="body2" sx={{ fontStyle: 'italic', color: 'text.secondary' }}>
                  Amount in INR 0.00
                </Typography>
              </Stack>
            </Grid>
          )}
          {formik.values.donor_target_type === 'in_kind' && (
            <KindDynamicInputList inputList={inputList} itemOptions={itemOptions} setInputList={setInputList} />
          )}
          <Grid item xs={12} sm={12}>
            <Stack spacing={1}>
              <Typography variant="h5" gutterBottom sx={{ mb: 2 }}>
                Volunteers Required *
              </Typography>
              <RadioGroup defaultValue={formik.values.volunteers_required} name="volunteers_required" row onChange={formik.handleChange}>
                <FormControlLabel value="yes" control={<Radio />} label="Yes" />
                <FormControlLabel value="no" control={<Radio />} label="No" />
              </RadioGroup>
            </Stack>
          </Grid>

          {formik.values.volunteers_required === 'yes' && (
            <Fragment>
              <Grid item xs={12} sm={4}>
                <Stack spacing={1}>
                  <TextField
                    label="No. of volunteers"
                    type="number"
                    fullWidth
                    required
                    name="no_of_volunteers"
                    value={formik.values.no_of_volunteers}
                    onChange={formik.handleChange}
                    error={formik.touched.no_of_volunteers && Boolean(formik.errors.no_of_volunteers)}
                    helperText={formik.touched.no_of_volunteers && formik.errors.no_of_volunteers}
                    margin="normal"
                    inputProps={{ min: 1 }}
                  />
                </Stack>
              </Grid>
              <Grid item xs={12} sm={4}>
                <Stack spacing={1}>
                  <Autocomplete
                    multiple
                    fullWidth
                    id="tags-outlined"
                    options={userSkills}
                    value={formik.values.skills} //convert into array
                    // onBlur={formik.handleBlur} // You can uncomment this if needed
                    getOptionLabel={(option) => option} // Ensures the label of the option is displayed
                    onChange={(event, newValue) => {
                      formik.setFieldValue('skills', newValue); // Set the entire array of selected skills
                    }}
                    renderInput={(params) => <TextField {...params} name="skills" label="Skills *" />}
                    renderTags={(value, getTagProps) =>
                      value.map((option, index) => (
                        <Chip
                          {...getTagProps({ index })}
                          key={index}
                          variant="combined"
                          label={option}
                          deleteIcon={<CloseOutlined style={{ fontSize: '0.75rem' }} />}
                          sx={{ color: 'text.primary' }}
                        />
                      ))
                    }
                    freeSolo
                  />
                </Stack>
              </Grid>
            </Fragment>
          )}
          <Grid item xs={12}>
            <Stack direction="row" justifyContent={'space-between'}>
              <Button variant="contained" onClick={handleBack} sx={{ my: 3, ml: 1 }}>
                <FormattedMessage id="back" />
              </Button>
              <AnimateButton>
                <Button variant="contained" type="submit" onClick={() => handleSubmit('Draft')} sx={{ my: 3, ml: 1 }}>
                  <FormattedMessage
                    id="saveDraft"
                    onClick={() => {
                      formik.setFieldValue('status', 'Draft');
                      formik.submitForm();
                    }}
                  />
                </Button>
                <Button variant="contained" type="submit" sx={{ my: 3, ml: 1 }}>
                  <FormattedMessage id="next" />
                </Button>
              </AnimateButton>
            </Stack>
          </Grid>
        </Grid>
      </form>
    </Box>
  );
}
