// axiosServices.js
import axios from 'axios';

const API_BASE_URL = `${import.meta.env.VITE_APP_APPLICATION_API_URL}/api`;

// export const fetchAdminsService = async (ngoId, portalUserId, pageName = '') => {
//   console.log(ngoId, portalUserId, pageName);
//   try {
//     const url = ngoId
//       ? `${API_BASE_URL}/portal-users/getNgoStaffMembers?ngoId=${ngoId}&pageName=${pageName}`
//       : `${API_BASE_URL}/portal-users/getDRStaffMembers?pageName=${pageName}`;
//     const response = await axios.get(url);
//     return response.data;
//   } catch (error) {
//     console.error('Error fetching admins:', error);
//     throw error;
//   }
// };

export const fetchAdminsService = async (ngoId, portalUserId, pageName = '') => {
  try {
    let url;

    if (portalUserId) {
      url = `${API_BASE_URL}/portal-users/getAssignedStaffMembers?portalUserId=${portalUserId}&pageName=${pageName}`;
    } else if (ngoId) {
      url = `${API_BASE_URL}/portal-users/getNgoStaffMembers?ngoId=${ngoId}&pageName=${pageName}`;
    } else {
      url = `${API_BASE_URL}/portal-users/getDRStaffMembers?pageName=${pageName}`;
    }

    const response = await axios.get(url);
    return response.data;
  } catch (error) {
    console.error('Error fetching admins:', error);
    throw error;
  }
};

export const fetchRolesBytype = async (ngoId, pageName = '') => {
  try {
    const url = ngoId ? `${API_BASE_URL}/roles?type=NGO&pageName=${pageName}` : `${API_BASE_URL}/roles?type=Admin&pageName=${pageName}`;
    const response = await axios.get(url);
    return response.data;
  } catch (error) {
    console.error('Error fetching roles:', error);
    throw error;
  }
};
export const fetchAllRoles = async () => {
  try {
    const response = await axios.get(`${API_BASE_URL}/roles`);
    return response.data;
  } catch (error) {
    console.error('Error fetching roles:', error);
    throw error;
  }
};

export const addAdminService = async (adminData) => {
  try {
    const response = await axios.post(`${API_BASE_URL}/portal-users`, adminData);
    return response.data;
  } catch (error) {
    console.error('Error adding admin:', error);
    throw error;
  }
};

export const updateAdminService = async (adminId, adminData) => {
  try {
    const response = await axios.put(`${API_BASE_URL}/portal-users/${adminId}`, adminData);
    return response.data;
  } catch (error) {
    console.error('Error updating admin:', error);
    throw error;
  }
};

export const deleteAdminService = async (adminId, pageName = '') => {
  try {
    await axios.delete(`${API_BASE_URL}/portal-users/${adminId}?pageName=${pageName}`);
  } catch (error) {
    console.error('Error deleting admin:', error);
    throw error;
  }
};

export const fetchNgoByIdService = async (ngoId) => {
  try {
    const response = await axios.get(`${API_BASE_URL}/ngos/${ngoId}`);
    return response.data;
  } catch (error) {
    console.error('Error fetching NGO data:', error);
    throw error;
  }
};

export const fetchStates = async () => {
  try {
    const response = await axios.get(`${API_BASE_URL}/states`);
    return response.data;
  } catch (error) {
    console.error('Error fetching states data:', error);
    throw error;
  }
};
