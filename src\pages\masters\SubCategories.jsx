import React, { useEffect, useState } from 'react';
import CustomerTable from 'sections/apps/customer/CustomerTable';
import {
  Button,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Autocomplete
} from '@mui/material';
import { getUserPermissions } from 'utils/permissionUtils';
import { toast, ToastContainer } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import { API_BASE_URL, getAllCategories } from 'api/categories.service';
import TableActions from 'components/TableActions';
import useAuth from 'hooks/useAuth';
import { addSubcategory, deleteSubcategory, editSubcategory, fetchSubcategoriesSerice } from './apis/subcategories.service';
import dayjs from 'dayjs';

export default function SubcategoriesTable() {
  const { user } = useAuth();
  const [subcategories, setSubcategories] = useState([]);
  const [categories, setCategories] = useState([]);
  const [openDialog, setOpenDialog] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [currentSubcategory, setCurrentSubcategory] = useState(null);

  // Role-based access
  const permissions = getUserPermissions(user);
  const canAdd = permissions?.Add?.Subcategories || true;
  const canEdit = permissions?.Edit?.Subcategories || true;
  const canDelete = permissions?.Delete?.Subcategories || true;

  useEffect(() => {
    fetchSubcategories();
    fetchCategories();
  }, []);
  const fetchSubcategories = async () => {
    try {
      const response = await fetchSubcategoriesSerice();
      setSubcategories(response);
    } catch (error) {
      console.error('Failed to fetch subcategories:', error);
      toast.error('Failed to fetch subcategories');
    }
  };

  const fetchCategories = async () => {
    try {
      const response = await getAllCategories();
      setCategories(response);
    } catch (error) {
      console.error('Failed to fetch categories:', error);
      toast.error('Failed to fetch categories');
    }
  };

  const handleAddOrEdit = async () => {
    try {
      if (isEditing) {
        await editSubcategory(currentSubcategory.id, currentSubcategory);
        toast.success('Subcategory updated successfully!');
      } else {
        await addSubcategory(currentSubcategory);
        toast.success('Subcategory added successfully!');
      }
      setOpenDialog(false);
      fetchSubcategories();
    } catch (error) {
      console.error('Failed to save subcategory:', error);
      toast.error('Failed to save subcategory');
    }
  };

  const handleDelete = async (id) => {
    const confirmDelete = window.confirm('Are you sure you want to delete this subcategory?');
    if (confirmDelete) {
      try {
        await deleteSubcategory(id);
        toast.success('Subcategory deleted successfully!');
        fetchSubcategories();
      } catch (error) {
        console.error('Failed to delete subcategory:', error);
        toast.error('Failed to delete subcategory');
      }
    }
  };

  const openAddDialog = () => {
    if (!canAdd) {
      toast.error('You do not have permission to add Subcategories.');
      return;
    }
    setCurrentSubcategory({ name: '', category_id: '', status: 'active' });
    setIsEditing(false);
    setOpenDialog(true);
  };

  const openEditDialog = (subcategory) => {
    if (!canEdit) {
      toast.error('You do not have permission to edit Subcategories.');
      return;
    }
    setCurrentSubcategory(subcategory);
    setIsEditing(true);
    setOpenDialog(true);
  };

  const columns = [
    {
      accessorKey: 'name',
      header: 'Name',
      showByDefault: true,
      cell: ({ cell }) => {
        return (
          <Button variant="text" onClick={() => openEditDialog(cell.row.original)}>
            {cell.row.original.name}
          </Button>
        );
      }
    },
    {
      accessorKey: 'category_id',
      header: 'Category',
      showByDefault: true,
      cell: (row) => {
        const category = categories.find((cat) => cat.id === row.getValue());
        return category ? category.name : 'N/A';
      }
    },
    { accessorKey: 'description', header: 'Description', showByDefault: true },
    { accessorKey: 'status', header: 'Status', showByDefault: true },
    {
      accessorKey: 'createdAt',
      header: 'Created At',
      showByDefault: true,
      cell: (cell) => {
        const formattedDate = dayjs(cell.row.original.createdAt).format('DD-MM-YYYY hh:mm A');
        return <span>{formattedDate}</span>;
      }
    },
    {
      accessorKey: 'updatedAt',
      header: 'Updated At',
      showByDefault: true,
      cell: (cell) => {
        const formattedDate = dayjs(cell.row.original.updatedAt).format('DD-MM-YYYY hh:mm A');
        return <span>{formattedDate}</span>;
      }
    },
    {
      id: 'actions',
      header: 'Actions',
      cell: (cell, { row }) => <TableActions handleEditClick={openEditDialog} cell={cell} handleDeleteClick={handleDelete} />
    }
  ];

  return (
    <div>
      <CustomerTable data={subcategories} columns={columns} modalToggler={openAddDialog} category={'Subcategory'} />

      <Dialog
        open={openDialog}
        onClose={(event, reason) => {
          if (reason !== 'backdropClick' && reason !== 'escapeKeyDown') {
            setOpenDialog(false);
          }
        }}
        maxWidth="50%"
        PaperProps={{
          sx: {
            top: 0,
            position: 'absolute',
            borderRadius: '16px',
            width: '100%',
            maxWidth: '600px'
          }
        }}
      >
        <DialogTitle>{isEditing ? 'Edit Subcategory' : 'Add Subcategory'}</DialogTitle>
        <DialogContent>
          <TextField
            autoFocus
            margin="dense"
            label="Name"
            type="text"
            fullWidth
            variant="outlined"
            inputProps={{ maxLength: 200 }}
            value={currentSubcategory?.name || ''}
            onChange={(e) => setCurrentSubcategory({ ...currentSubcategory, name: e.target.value })}
            helperText={`${currentSubcategory?.name?.length || 0}/200 characters used`}
            FormHelperTextProps={{
              sx: { textAlign: 'right', fontSize: '12px', color: 'gray' }
            }}
          />
          <TextField
            multiline
            margin="dense"
            label="Description"
            type="text"
            fullWidth
            variant="outlined"
            value={currentSubcategory?.description || ''}
            onChange={(e) => setCurrentSubcategory({ ...currentSubcategory, description: e.target.value })}
          />
          <Autocomplete
            options={categories}
            getOptionLabel={(option) => option.name || ''}
            value={categories.find((cat) => cat.id === currentSubcategory?.category_id) || null}
            onChange={(event, newValue) => setCurrentSubcategory({ ...currentSubcategory, category_id: newValue?.id || '' })}
            renderInput={(params) => <TextField {...params} label="Category" margin="dense" fullWidth />}
          />
          <FormControl fullWidth margin="dense" variant="outlined">
            <InputLabel>Status</InputLabel>
            <Select
              value={currentSubcategory?.status || ''}
              onChange={(e) => setCurrentSubcategory({ ...currentSubcategory, status: e.target.value })}
              label="Status"
            >
              <MenuItem value="active">Active</MenuItem>
              <MenuItem value="inactive">Inactive</MenuItem>
            </Select>
          </FormControl>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setOpenDialog(false)} color="primary">
            Cancel
          </Button>
          <Button onClick={handleAddOrEdit} color="primary">
            {isEditing ? 'Update' : 'Add'}
          </Button>
        </DialogActions>
      </Dialog>

      <ToastContainer />
    </div>
  );
}
