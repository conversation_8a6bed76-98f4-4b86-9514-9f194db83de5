// export default SendNotification;

import React, { useEffect, useState } from 'react';
import { Button, Dialog, DialogActions, DialogContent, DialogTitle, TextField, Grid, Autocomplete, DialogContentText } from '@mui/material';
import { ToastContainer, toast } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import CustomerTable from 'sections/apps/customer/CustomerTable';
import { getUserPermissions } from 'utils/permissionUtils';
import useAuth from 'hooks/useAuth';
import * as yup from 'yup';
import { Formik } from 'formik';
import TableActions from 'components/TableActions';
import dayjs from 'dayjs';
import { getAllCategories } from 'api/categories.service';
import { fetchNotificationsService, sendNotificationService } from 'api/usernotification.service';

export default function NotificationTable() {
  const { user } = useAuth();
  const [notifications, setNotifications] = useState([]);
  const [openDialog, setOpenDialog] = useState(false);
  const [categories, setCategories] = useState([]);
  const permissions = getUserPermissions(user);
  const canAdd = permissions?.Add?.UserNotification || true;

  useEffect(() => {
    fetchNotifications();
    getAllCategories().then(setCategories);
  }, []);

  const fetchNotifications = async () => {
    try {
      const data = await fetchNotificationsService();
      setNotifications(data);
    } catch (error) {
      toast.error('Failed to fetch notifications');
    }
  };

  const openAddDialog = () => {
    if (!canAdd) {
      toast.error('You do not have permission to send notifications.');
      return;
    }
    setOpenDialog(true);
  };

  const columns = [
    { accessorKey: 'title', header: 'Title' },
    { accessorKey: 'body', header: 'Body' },
    {
      accessorKey: 'type',
      header: 'Sent to',
      cell: ({ row }) => (row.original.type === 'user' ? 'Single user' : 'All')
    },
    {
      accessorKey: 'categoryInfo.name',
      header: 'Category'
    },
    {
      accessorKey: 'createdAt',
      header: 'Sent At',
      cell: (cell) => {
        const formattedDate = dayjs(cell.row.original.createdAt).format('DD-MM-YYYY hh:mm A');
        return <span>{formattedDate}</span>;
      }
    }
  ];

  return (
    <div>
      <CustomerTable data={notifications} columns={columns} modalToggler={openAddDialog} category={'Notification'} />

      <Dialog
        open={openDialog}
        onClose={(event, reason) => {
          if (reason !== 'backdropClick' && reason !== 'escapeKeyDown') {
            setOpenDialog(false);
          }
        }}
        maxWidth="50%"
        PaperProps={{
          sx: {
            top: 0,
            position: 'absolute',
            borderRadius: '16px',
            width: '100%',
            maxWidth: '600px'
          }
        }}
      >
        <Formik
          initialValues={{ title: '', body: '', category_id: null }}
          validationSchema={yup.object().shape({
            title: yup.string().required('Title is required'),
            body: yup.string().required('Body is required'),
            category_id: yup.number().required('Category is required')
          })}
          onSubmit={async (values, { resetForm }) => {
            try {
              const payload = {
                ...values,
                type: 'all'
              };
              await sendNotificationService(payload);
              toast.success('Notification sent successfully!');
              setOpenDialog(false);
              resetForm();
              fetchNotifications();
            } catch (error) {
              toast.error('Failed to send notification');
            }
          }}
        >
          {({ values, handleChange, handleBlur, handleSubmit, setFieldValue, errors, touched }) => (
            <form onSubmit={handleSubmit}>
              <DialogTitle>Send Notification</DialogTitle>
              <DialogContent>
                <Grid container spacing={2}>
                  <Grid item xs={12} sx={{ marginTop: 1 }}>
                    <Autocomplete
                      options={categories}
                      getOptionLabel={(option) => option.name}
                      // value={values.category_id}
                      onChange={(e, newValue) => setFieldValue('category_id', newValue ? newValue.id : '')}
                      renderInput={(params) => (
                        <TextField
                          {...params}
                          label="Category"
                          error={Boolean(touched.category_id && errors.category_id)}
                          helperText={touched.category_id && errors.category_id}
                        />
                      )}
                    />
                  </Grid>
                  <Grid item xs={12}>
                    <TextField
                      label="Title"
                      name="title"
                      fullWidth
                      value={values.title}
                      onChange={handleChange}
                      onBlur={handleBlur}
                      error={Boolean(touched.title && errors.title)}
                      helperText={touched.title && errors.title}
                    />
                  </Grid>
                  <Grid item xs={12}>
                    <TextField
                      label="Body"
                      name="body"
                      fullWidth
                      multiline
                      rows={4}
                      value={values.body}
                      onChange={handleChange}
                      onBlur={handleBlur}
                      error={Boolean(touched.body && errors.body)}
                      helperText={touched.body && errors.body}
                    />
                  </Grid>
                </Grid>
              </DialogContent>
              <DialogActions>
                <Button onClick={() => setOpenDialog(false)} color="secondary">
                  Cancel
                </Button>
                <Button type="submit" color="primary" variant="contained">
                  Send
                </Button>
              </DialogActions>
            </form>
          )}
        </Formik>
      </Dialog>

      <ToastContainer />
    </div>
  );
}
