import { useEffect, useState } from 'react';
import { Link as RouterLink, useNavigate, useSearchParams } from 'react-router-dom';

// material-ui
import Box from '@mui/material/Box';
import Button from '@mui/material/Button';
import FormControl from '@mui/material/FormControl';
import FormHelperText from '@mui/material/FormHelperText';
import Grid from '@mui/material/Grid';
import Link from '@mui/material/Link';
import InputAdornment from '@mui/material/InputAdornment';
import InputLabel from '@mui/material/InputLabel';
import OutlinedInput from '@mui/material/OutlinedInput';
import Stack from '@mui/material/Stack';
import Typography from '@mui/material/Typography';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  List,
  ListItem,
  ListItemText,
  DialogActions,
  Autocomplete,
  TextField,
  Select,
  MenuItem,
  FormControlLabel,
  RadioGroup,
  Radio,
  Checkbox,
  CircularProgress,
  Tooltip
} from '@mui/material';

// third party
import * as Yup from 'yup';
import { Formik, Form } from 'formik';

// project import
import IconButton from 'components/@extended/IconButton';
import AnimateButton from 'components/@extended/AnimateButton';

import useAuth from 'hooks/useAuth';
import useScriptRef from 'hooks/useScriptRef';
import { openSnackbar } from 'api/snackbar';
import { strengthColor, strengthIndicator } from 'utils/password-strength';

// assets
import EyeOutlined from '@ant-design/icons/EyeOutlined';
import EyeInvisibleOutlined from '@ant-design/icons/EyeInvisibleOutlined';
import { patchNGOInfo, updateUsingPatchNGO } from 'sections/apps/profiles/profile.service';
import { NGO_TYPE_LIST } from 'utils/statusconstans';
import { addNgoUser } from 'api/ngos.service';
import { API_BASE_URL, DOCUMENTS_BASE_URL } from 'api/campaigns.service';
import { getAllNgoTypes } from 'api/ngotypes.service';
import { sendverificationNumber, sendverificationNumberForReg, verifyOtpReg, verifyPan } from 'sections/apps/profiles/account/tabs.service';
import { ToastContainer, toast } from 'react-toastify';
import { CheckCircleOutlined } from '@ant-design/icons';
import { useFormikContext } from 'formik';

// ============================|| JWT - REGISTER ||============================ //

export default function AuthRegister() {
  const { register } = useAuth();
  const [ngos, setNgos] = useState([]);
  const [matchedNgos, setMatchedNgos] = useState([]);
  const [showNgoPopup, setShowNgoPopup] = useState(false);
  const [showConfirmationDialog, setShowConfirmationDialog] = useState(false);
  const [selectedNGO, setSelectedNgo] = useState(false);
  const [ngoTypes, setNgoTypes] = useState([]);
  const [payloadData, setPayloadData] = useState({});
  const [verifiedPanData, setVerifiedPanData] = useState(null);
  const [showNameMismatchDialog, setShowNameMismatchDialog] = useState(false);
  const [panVerifiedName, setPanVerifiedName] = useState('');

  const [verificationStatus, setVerificationStatus] = useState({
    isSendingOtp: false,
    otpSentForPhone: false,
    isVerfyingOTP: false,
    isVerfyingPAN: false,
    isPhoneVerified: false,
    isPanVerified: false
  });

  const scriptedRef = useScriptRef();
  const navigate = useNavigate();

  const fetchNgoTypes = async () => {
    try {
      const response = await getAllNgoTypes();
      setNgoTypes(response);
    } catch (error) {
      console.error('Failed to fetch ngo types:', error);
      //   toast.error('Failed to fetch ngo types');
    }
  };

  const [level, setLevel] = useState();
  const [showPassword, setShowPassword] = useState(false);
  const handleClickShowPassword = () => {
    setShowPassword(!showPassword);
  };

  const handleMouseDownPassword = (event) => {
    event.preventDefault();
  };
  const handleNgoSelection = (ngo) => {
    setSelectedNgo(ngo);
    // setShowNgoPopup(false);
    // setShowConfirmationDialog(true);
  };

  const checkMatchedNgo = (selectedNgo) => {
    return matchedNgos?.ngoDetails?.find(
      (ngo) => ngo.name.toLowerCase() === selectedNgo.name.toLowerCase() && ngo.email.toLowerCase() === payloadData.email.toLowerCase()
    );
  };
  const handleConfirmNgoAssociation = async () => {
    if (selectedNGO) {
      const matchedNgo = checkMatchedNgo(selectedNGO);

      if (matchedNgos.portalUserDetails?.email == payloadData.email) {
        setShowConfirmationDialog(false);
        openSnackbar({
          open: true,
          message: 'This NGO is already registered. Please use your email and password to login.',
          variant: 'alert',
          alert: { color: 'info' },
          autoHideDuration: 5000
        });
        setTimeout(() => {
          navigate('/login', { replace: true });
        }, 7000);
      } else if (matchedNgo && !matchedNgos.portalUserDetails) {
        try {
          setShowConfirmationDialog(true);
          const updatedPayloadData = {
            ...payloadData,
            ngo_id: selectedNGO.id
          };

          await addNgoUser(updatedPayloadData);
          // await updateUsingPatchNGO(selectedNGO.id, { source: 'Matched', claimed_ngo: 'yes' });
        } catch (err) {
          if (err?.message.includes('is already taken')) {
            openSnackbar({
              open: true,
              message: 'This Email is Already Taken. Please try with a different email.',
              variant: 'alert',
              alert: { color: 'warning' }
            });
          } else if (err?.message.includes('We are reviewing your')) {
            openSnackbar({
              open: true,
              message: 'We are reviewing your NGO profile and will get back to you soon.',
              variant: 'warning',
              alert: { color: 'danger' }
            });
          } else {
            openSnackbar({
              open: true,
              message: 'Something went wrong. Please try again.',
              variant: 'alert',
              alert: { color: 'danger' }
            });
          }
        }
      } else {
        try {
          const updatedPayloadData = {
            ...payloadData,
            ngo_id: selectedNGO.id
          };

          await addNgoUser(updatedPayloadData);
          // await updateUsingPatchNGO(selectedNGO.id, { source: 'Matched', claimed_ngo: 'yes' });
          // openSnackbar({
          //   open: true,
          //   message: 'Registration Successful!',
          //   variant: 'alert',
          //   alert: { color: 'success' }
          // });

          setTimeout(() => {
            navigate('/login', { replace: true });
          }, 5000);
        } catch (err) {
          if (err?.message.includes('is already')) {
            openSnackbar({
              open: true,
              message: 'This Email is already taken. Please try with a different email.',
              variant: 'alert',
              alert: { color: 'warning' }
            });
          } else if (err?.message.includes('We are reviewing your')) {
            openSnackbar({
              open: true,
              message: 'We are reviewing your NGO profile and will get back to you soon.',
              variant: 'alert',
              alert: { color: 'warning' }
            });
          } else {
            openSnackbar({
              open: true,
              message: 'Something went wrong. Please try again.',
              variant: 'alert',
              alert: { color: 'danger' }
            });
          }
        }
      }
    }
  };

  //otp and pan verification

  const handleVerifyNumber = async (point_of_contact_mobile_number) => {
    setVerificationStatus((prev) => ({
      ...prev,
      isSendingOtp: true
    }));
    try {
      const response = await sendverificationNumberForReg(point_of_contact_mobile_number); // API Call
      if (response.status) {
        toast.success('A OTP has been sent. Please check your mobile phone');
        setVerificationStatus((prev) => ({
          ...prev,
          otpSentForPhone: true,
          isSendingOtp: false
        }));
      } else {
        toast.error('Failed to send otp. Try again.');
      }
    } catch (error) {
      toast.error('An error occurred. Please try again.');
    }
  };

  const verifyNumber = async (point_of_contact_mobile_number, otp) => {
    setVerificationStatus((prev) => ({
      ...prev,
      isVerfyingOTP: true
    }));
    try {
      const response = await verifyOtpReg(point_of_contact_mobile_number, otp); // API Call
      if (response.status) {
        toast.success('Mobile number verified successfully.');

        setVerificationStatus((prev) => ({
          ...prev,
          isPhoneVerified: true
        }));
        setVerificationStatus((prev) => ({
          ...prev,
          isVerfyingOTP: false,
          otpSentForPhone: false
        }));
      } else {
        toast.error('Failed to verify otp. Try again.');
      }
    } catch (error) {
      toast.error('An error occurred. Please try again.');
    } finally {
      setVerificationStatus((prev) => ({
        ...prev,
        isVerfyingOTP: false
      }));
    }
  };

  const changePassword = (value) => {
    const temp = strengthIndicator(value);
    setLevel(strengthColor(temp));
  };

  const [searchParams] = useSearchParams();
  const auth = searchParams.get('auth');

  useEffect(() => {
    changePassword('');
    fetchNgoTypes();
  }, []);

  return (
    <>
      <Formik
        initialValues={{
          name: '',
          point_of_contact_name: '',
          point_of_contact_mobile_number: '',
          pan: '',
          email: '',
          password: '',
          darpan_id: '',
          otp_phone: '',
          role_id: '12',
          ngo_type: '',
          acceptTerms: false,
          acceptNotifications: false
        }}
        validationSchema={Yup.object().shape({
          // name: Yup.string().max(255).required('NGO legal name is required'),
          point_of_contact_name: Yup.string().max(255).required('Contact name is required'),
          email: Yup.string().email('Must be a valid email').max(255).required('Contact email is required'),
          point_of_contact_mobile_number: Yup.string()
            .min(10, 'Mobile number must be at least 10 characters')
            .max(10, 'Mobile number must be at least 10 characters')
            .required('Mobile number is required'),
          pan: Yup.string()
            .required('PAN number is required')
            .length(10, 'PAN number must be exactly 10 characters')
            .test('pan-format', 'First 5 characters must be letters (A-Z)', (value) => /^[A-Z]{5}/.test(value || ''))
            .test('pan-digits', 'Characters 6 to 9 must be digits (0-9)', (value) => /^[A-Z]{5}[0-9]{4}/.test(value || ''))
            .test('pan-last', 'Last character must be a letter (A-Z)', (value) => /^[A-Z]{5}[0-9]{4}[A-Z]$/.test(value || ''))
            .test('pan-fourth-char', '4th character must be C, A, T, or D', (value) => /^[A-Z]{3}[CATD]/.test(value || '')),

          darpan_id: Yup.string()
            .matches(/^[A-Za-z0-9]{2}\/[A-Za-z0-9]{4}\/\d{7}$/, 'Darpan ID must be in the format XX/YYYY/1111111')
            .required('Darpan ID is required'),
          ngo_type: Yup.string().required('NGO type is required'),
          password: Yup.string()
            .trim() // Removes leading and trailing spaces automatically
            .required('Password is required')
            .min(8, 'Password must be greater than 8 characters')
            .test('no-leading-trailing-whitespace', 'Password cannot start or end with spaces', (value) => value === value.trim())
            .max(20, 'Password must be less than 20 characters')
            .matches(/[A-Z]/, 'Password must contain at least one uppercase letter')
            .matches(/[a-z]/, 'Password must contain at least one lowercase letter')
            .matches(/[0-9]/, 'Password must contain at least one number')
            .matches(/[@$!%*?&]/, 'Password must contain at least one special character'),
          acceptNotifications: Yup.bool().oneOf([true], 'You must select this option to receive updates and notifications.'),
          acceptTerms: Yup.bool().oneOf([true], 'You must accept the terms of service and privacy policy')
        })}
        onSubmit={async (values, { setErrors, setStatus, setSubmitting }) => {
          if (!values.acceptTerms) {
            setErrors({ acceptTerms: 'You must accept the terms and conditions' });
            return;
          }
          if (!values.acceptNotifications) {
            setErrors({ acceptNotifications: 'You must select this option to receive updates and notifications.' });
            return;
          }
          if (!verificationStatus.isPhoneVerified) {
            toast.error('Please verify your mobile number before proceeding.');
            return;
          }
          if (!verificationStatus.isPanVerified) {
            toast.error('Please verify your PAN number before proceeding.');
            return;
          }

          try {
            values.email = values.email.trim();
            values.ngo_status = 'New';
            values.last_status = 'New';
            values.panNgoName = panVerifiedName;
            const response = await register(values);

            if (response) {
              setNgos(response?.ngos?.ngoDetails || []);

              setMatchedNgos(response?.matchedNgos || []);
            }

            setPayloadData({
              fullname: values.name,
              panNgoName: panVerifiedName,
              pan: values.pan,
              name: values.point_of_contact_name,
              email: values.email,
              password: values.password,
              role_id: values.role_id,
              darpan_id: values.darpan_id,
              point_of_contact_mobile_number: values.point_of_contact_mobile_number,
              status: 'Active'
            });
            if (response) { 
              if (ngos && ngos.length === 0 && response?.status) {
                setShowNgoPopup(false);
                setStatus({ success: true });
                setSubmitting(false);
                openSnackbar({
                  open: true,
                  message: "You've successfully registered.",
                  variant: 'alert',

                  alert: {
                    color: 'success'
                  }
                });
                setTimeout(() => {
                  navigate('/login', { replace: true });
                }, 5000);
              } else {
                setShowNgoPopup(true);
                setSubmitting(false);
              }
            }
          } catch (err) {
            openSnackbar({
              open: true,
              message: err?.message,
              variant: 'alert',

              alert: {
                color: 'primary'
              }
            });
            console.error(err);

            setStatus({ success: false });
            setErrors({ submit: err.message });
            setSubmitting(false);
          }
        }}
      >
        {({ errors, handleBlur, handleChange, handleSubmit, isSubmitting, touched, values, setFieldValue }) => {
          const handleDarpanIdChange = (e) => {
            let { name, value } = e.target;

            if (name === 'darpan_id') {
              // Allow only alphanumeric characters
              let input = value.replace(/[^a-zA-Z0-9]/g, '');

              let formatted = '';
              if (input.length <= 2) {
                formatted = input;
              } else if (input.length <= 6) {
                formatted = `${input.slice(0, 2)}/${input.slice(2)}`;
              } else {
                formatted = `${input.slice(0, 2)}/${input.slice(2, 6)}/${input.slice(6, 13)}`;
              }

              value = formatted;
            }

            setFieldValue(name, value);
          };
          const verifyPanNumber = async (pan, mobile_number) => {
            if (!verificationStatus.isPhoneVerified) {
              toast.error('Please verify your mobile number before verifying PAN number.');
              return;
            }

            if (!/^[A-Z]{3}[CATD]/.test(pan)) {
              toast.error('4th character must be C, A, T, or D');
              return;
            }

            setVerificationStatus((prev) => ({
              ...prev,
              isVerfyingPAN: true
            }));
            try {
              const response = await verifyPan(pan, mobile_number); // API Call
              if (response.status) {
                setVerifiedPanData(response?.data?.data);

                const panName = response?.data?.data?.full_name || '';
                const formName = values.name?.trim().toLowerCase();
                const fullPanName = panName?.trim().toLowerCase();

                setPanVerifiedName(panName);
                setShowNameMismatchDialog(true);
                toast.success('PAN number verified successfully.');
                setFieldValue('name', panName);
                setVerificationStatus((prev) => ({
                  ...prev,
                  isVerfyingPAN: false,
                  isPanVerified: true
                }));
              } else {
                toast.error(response?.message || 'Failed to verify PAN Number. Try again.');
              }
            } catch (error) {
              toast.error('An error occurred. Please try again.');
            } finally {
              setVerificationStatus((prev) => ({
                ...prev,
                isVerfyingPAN: false
              }));
            }
          };

          return (
            <Form noValidate onSubmit={handleSubmit} autoComplete="off">
              <Grid container spacing={3}>
                <Grid item xs={12} md={12}>
                  <OutlinedInput
                    style={{ backgroundColor: '#fff' }}
                    fullWidth
                    error={Boolean(touched.point_of_contact_mobile_number && errors.point_of_contact_mobile_number)}
                    id="mobile_number-signup"
                    value={values.point_of_contact_mobile_number}
                    name="point_of_contact_mobile_number"
                    onBlur={handleBlur}
                    placeholder="Mobile Number*"
                    inputProps={{
                      maxLength: 10
                    }}
                    disabled={verificationStatus.otpSentForPhone || verificationStatus.isPhoneVerified}
                    onKeyDown={(e) => {
                      if (!/[0-9]/.test(e.key) && e.key !== 'Backspace' && e.key !== 'Delete') {
                        e.preventDefault(); // block non-numeric input except backspace and delete
                      }
                    }}
                    onChange={(e) => {
                      let numericValue = e.target.value.replace(/[^0-9]/g, ''); // remove non-numeric characters
                      if (numericValue.length <= 10) {
                        setFieldValue('point_of_contact_mobile_number', numericValue); // update only if length is <= 10
                      }
                    }}
                    endAdornment={
                      <InputAdornment position="end">
                        {verificationStatus.isPhoneVerified ? (
                          <Tooltip title="Number verified" arrow>
                            <CheckCircleOutlined style={{ color: 'green', fontSize: 20 }} />
                          </Tooltip>
                        ) : (
                          <Button
                            variant="contained"
                            sx={{
                              fontSize: 12,
                              fontWeight: 'bold',
                              minWidth: 'auto',
                              height: '32px',
                              color: 'white',
                              border: '1px solid',
                              borderColor: 'primary.main',
                              borderRadius: 2,
                              padding: '2px 8px',
                              whiteSpace: 'nowrap',
                              textTransform: 'none',
                              '&:hover': {
                                backgroundColor: '#f5f5f5'
                              }
                            }}
                            disabled={verificationStatus.otpSentForPhone || values.point_of_contact_mobile_number.length !== 10}
                            onClick={() => {
                              handleVerifyNumber(values.point_of_contact_mobile_number);
                              // startResendTimer();
                            }}
                          >
                            {verificationStatus.isSendingOtp ? <CircularProgress color="secondary" size={16} /> : 'Verify Number'}
                          </Button>
                        )}
                      </InputAdornment>
                    }
                  />

                  {touched.point_of_contact_mobile_number && errors.point_of_contact_mobile_number && (
                    <FormHelperText error id="helper-text-mobile_number-signup">
                      {errors.point_of_contact_mobile_number}
                    </FormHelperText>
                  )}
                </Grid>
                {verificationStatus.otpSentForPhone && (
                  <Grid item xs={12} md={12}>
                    <OutlinedInput
                      style={{ backgroundColor: '#fff', marginTop: '8px' }}
                      fullWidth
                      name="otp_phone"
                      placeholder="Enter OTP"
                      onBlur={handleBlur}
                      inputProps={{
                        maxLength: 6
                      }}
                      onKeyDown={(e) => {
                        if (!/[0-9]/.test(e.key) && e.key !== 'Backspace' && e.key !== 'Delete') {
                          e.preventDefault(); // block non-numeric input except backspace and delete
                        }
                      }}
                      onChange={(e) => {
                        let onlyNums = e.target.value.replace(/[^0-9]/g, ''); // remove non-numeric characters
                        if (onlyNums.length <= 6) {
                          setFieldValue('otp_phone', onlyNums); // update only if length is <= 6
                        }
                      }}
                      endAdornment={
                        <InputAdornment position="end">
                          <Button
                            variant="contained"
                            sx={{
                              fontSize: 12,
                              fontWeight: 'bold',
                              minWidth: 'auto',
                              height: '32px',
                              color: 'white',
                              border: '1px solid',
                              borderColor: 'primary.main',
                              borderRadius: 2,
                              padding: '2px 8px',
                              whiteSpace: 'nowrap',
                              textTransform: 'none',
                              '&:hover': {
                                backgroundColor: '#f5f5f5'
                              }
                            }}
                            disabled={values.otp_phone?.length != 6}
                            onClick={() => {
                              verifyNumber(values.point_of_contact_mobile_number, values.otp_phone);
                            }}
                          >
                            {verificationStatus.isVerfyingOTP ? <CircularProgress color="secondary" size={16} /> : ' Verify OTP'}
                          </Button>
                        </InputAdornment>
                      }
                    />
                  </Grid>
                )}
                <Grid item xs={12} md={12}>
                  <OutlinedInput
                    style={{ backgroundColor: '#fff' }}
                    fullWidth
                    error={Boolean(touched.pan && errors.pan)}
                    id="pan-signup"
                    value={values.pan}
                    name="pan"
                    onBlur={handleBlur}
                    onChange={handleChange}
                    placeholder="PAN Number*"
                    disabled={verificationStatus.isPanVerified}
                    endAdornment={
                      <InputAdornment position="end">
                        {verificationStatus.isPanVerified ? (
                          <Tooltip title="PAN verified" arrow>
                            <CheckCircleOutlined style={{ color: 'green', fontSize: 20 }} />{' '}
                          </Tooltip>
                        ) : (
                          <Button
                            variant="contained"
                            disabled={values.pan.length != 10}
                            sx={{
                              fontSize: 12,
                              fontWeight: 'bold',
                              minWidth: 'auto',
                              height: '32px',
                              color: 'white',
                              border: '1px solid',
                              borderColor: 'primary.main',
                              borderRadius: 2,
                              padding: '2px 8px',
                              whiteSpace: 'nowrap',
                              textTransform: 'none',
                              '&:hover': {
                                backgroundColor: '#f5f5f5'
                              }
                            }}
                            onClick={() => {
                              verifyPanNumber(values.pan, values.point_of_contact_mobile_number);
                            }}
                          >
                            {verificationStatus.isVerfyingPAN ? <CircularProgress color="secondary" size={16} /> : 'Verify PAN'}
                          </Button>
                        )}
                      </InputAdornment>
                    }
                    inputProps={{}}
                  />
                  {touched.pan && errors.pan && (
                    <FormHelperText error id="helper-text-pan-signup">
                      {errors.pan}
                    </FormHelperText>
                  )}
                </Grid>
                {panVerifiedName && (
                  <Grid item xs={12} md={12}>
                    <Stack spacing={1}>
                      {/* <InputLabel htmlFor="name-signup">Enter NGO's legal name*</InputLabel> */}
                      <OutlinedInput
                        id="name-login"
                        type="name"
                        value={values.name}
                        name="name"
                        onBlur={handleBlur}
                        onChange={handleChange}
                        fullWidth
                        placeholder="Enter NGO's Legal Name*"
                        disabled={!!panVerifiedName}
                        style={{ backgroundColor: '#fff' }}
                        error={Boolean(touched.name && errors.name)}
                      />
                    </Stack>
                    {touched.name && errors.name && (
                      <FormHelperText error id="helper-text-name-signup">
                        {errors.name}
                      </FormHelperText>
                    )}
                  </Grid>
                )}
                <Grid item xs={6} md={6}>
                  <Stack spacing={1}>
                    {/* <InputLabel htmlFor="name-signup">Select NGO Type</InputLabel> */}
                    <Select
                      style={{ backgroundColor: '#fff' }}
                      value={values.ngo_type}
                      displayEmpty
                      name="ngo_type"
                      renderValue={(selected) => {
                        if (selected.length === 0) {
                          return <Box sx={{ color: 'secondary.400' }}>Select NGO Type*</Box>;
                        }
                        return selected;
                      }}
                      //   placeholder='Select NGO Type'
                      onChange={handleChange}
                      error={Boolean(errors.ngo_type && touched.ngo_type)}
                      disabled={!verificationStatus.isPhoneVerified}
                    >
                      {ngoTypes && ngoTypes.map((ngotype) => <MenuItem value={ngotype?.name}>{ngotype?.name}</MenuItem>)}
                    </Select>
                  </Stack>
                  {touched.ngo_type && errors.ngo_type && (
                    <FormHelperText error id="helper-text-name-signup">
                      {errors.ngo_type}
                    </FormHelperText>
                  )}
                </Grid>

                <Grid item xs={6} md={6}>
                  <Stack spacing={1}>
                    {/* <InputLabel htmlFor="point_of_contact_name-signup">Contact Person's Name*</InputLabel> */}
                    <OutlinedInput
                      style={{ backgroundColor: '#fff' }}
                      fullWidth
                      error={Boolean(touched.point_of_contact_name && errors.point_of_contact_name)}
                      id="point_of_contact_name-signup"
                      type="point_of_contact_name"
                      value={values.point_of_contact_name}
                      name="point_of_contact_name"
                      onBlur={handleBlur}
                      onChange={handleChange}
                      placeholder="Contact Person's Name*"
                      inputProps={{}}
                      disabled={!verificationStatus.isPhoneVerified}
                    />
                  </Stack>
                  {touched.point_of_contact_name && errors.point_of_contact_name && (
                    <FormHelperText error id="helper-text-lastname-signup">
                      {errors.point_of_contact_name}
                    </FormHelperText>
                  )}
                </Grid>
                <Grid item xs={6}>
                  <Stack spacing={1}>
                    {/* <InputLabel htmlFor="email-signup">Email Address*</InputLabel> */}
                    <OutlinedInput
                      style={{ backgroundColor: '#fff' }}
                      fullWidth
                      error={Boolean(touched.email && errors.email)}
                      id="email-login"
                      type="email"
                      value={values.email}
                      name="email"
                      onBlur={handleBlur}
                      onChange={handleChange}
                      placeholder="Email Address*"
                      inputProps={{}}
                      disabled={!verificationStatus.isPhoneVerified}
                    />
                  </Stack>
                  {touched.email && errors.email && (
                    <FormHelperText error id="helper-text-email-signup">
                      {errors.email}
                    </FormHelperText>
                  )}
                </Grid>
                <Grid item xs={6}>
                  <Stack spacing={1}>
                    {/* <InputLabel htmlFor="darpan_id-signup">darpan_id Number*</InputLabel> */}
                    <OutlinedInput
                      style={{ backgroundColor: '#fff' }}
                      fullWidth
                      error={Boolean(touched.darpan_id && errors.darpan_id)}
                      id="darpan_id-signup"
                      value={values.darpan_id}
                      name="darpan_id"
                      onBlur={handleBlur}
                      onChange={handleDarpanIdChange}
                      placeholder="XX/YYYY/1111111*"
                      inputProps={{}}
                      disabled={!verificationStatus.isPhoneVerified}
                    />
                  </Stack>
                  {touched.darpan_id && errors.darpan_id && (
                    <FormHelperText error id="helper-text-darpan_id-signup">
                      {errors.darpan_id}
                    </FormHelperText>
                  )}
                </Grid>

                <Grid item xs={12}>
                  <Stack spacing={1}>
                    {/* <InputLabel htmlFor="password-signup">Password*</InputLabel> */}
                    <OutlinedInput
                      style={{ backgroundColor: '#fff' }}
                      fullWidth
                      error={Boolean(touched.password && errors.password)}
                      id="password-signup"
                      type={showPassword ? 'text' : 'password'}
                      value={values.password}
                      name="password"
                      onBlur={handleBlur}
                      onChange={(e) => {
                        handleChange(e);
                        changePassword(e.target.value);
                      }}
                      endAdornment={
                        <InputAdornment position="end">
                          <IconButton
                            aria-label="toggle password visibility"
                            onClick={handleClickShowPassword}
                            onMouseDown={handleMouseDownPassword}
                            edge="end"
                            color="secondary"
                          >
                            {showPassword ? <EyeOutlined /> : <EyeInvisibleOutlined />}
                          </IconButton>
                        </InputAdornment>
                      }
                      placeholder="Password*"
                      inputProps={{}}
                      disabled={!verificationStatus.isPhoneVerified}
                    />
                  </Stack>
                  {touched.password && errors.password && (
                    <FormHelperText error id="helper-text-password-signup">
                      {errors.password}
                    </FormHelperText>
                  )}
                </Grid>

                <Grid item xs={12}>
                  <FormControlLabel
                    control={
                      <Checkbox
                        color="secondary"
                        name="acceptTerms"
                        onBlur={handleBlur}
                        onChange={handleChange}
                        checked={values.acceptTerms}
                      />
                    }
                    label={
                      <Typography variant="body2">
                        <span style={{ color: '#4d4d4d' }}>By Signing up, you agree to our&nbsp;</span>
                        <Link
                          target={'_blank'}
                          variant="subtitle2"
                          component={RouterLink}
                          color="#4d4d4d"
                          style={{ textDecoration: 'underline' }}
                          to={`${DOCUMENTS_BASE_URL}/TheZenSocio T&C_Donor and NGO.pdf`}
                        >
                          Terms of Service
                        </Link>
                        &nbsp;and&nbsp;
                        <Link
                          target={'_blank'}
                          variant="subtitle2"
                          component={RouterLink}
                          color="#4d4d4d"
                          style={{ textDecoration: 'underline' }}
                          to={`${DOCUMENTS_BASE_URL}/TheZenSocio_Privacy Policy.pdf`}
                        >
                          Privacy Policy
                        </Link>
                      </Typography>
                    }
                  />
                  {touched.acceptTerms && errors.acceptTerms && (
                    <FormHelperText error id="helper-text-accept-terms">
                      {errors.acceptTerms}
                    </FormHelperText>
                  )}
                </Grid>

                <Grid item xs={12} style={{ paddingTop: 0 }}>
                  <FormControlLabel
                    control={
                      <Checkbox
                        color="secondary"
                        name="acceptNotifications"
                        onBlur={handleBlur}
                        onChange={handleChange}
                        checked={values.acceptNotifications}
                      />
                    }
                    label={
                      <Typography variant="body2">
                        <span style={{ color: '#4d4d4d' }}>Receive updates and notifications via WhatsApp and email</span>
                      </Typography>
                    }
                  />
                  {touched.acceptNotifications && errors.acceptNotifications && (
                    <FormHelperText error id="helper-text-accept-terms">
                      {errors.acceptNotifications}
                    </FormHelperText>
                  )}
                </Grid>

                {errors.submit && (
                  <Grid item xs={12}>
                    <FormHelperText error>{errors.submit}</FormHelperText>
                  </Grid>
                )}
                <Grid item xs={12}>
                  <AnimateButton>
                    <Button
                      disableElevation
                      disabled={isSubmitting}
                      fullWidth
                      size="large"
                      type="submit"
                      variant="contained"
                      color="primary"
                      style={{ backgroundColor: '#4d4d4d' }}
                    >
                      Create Account
                    </Button>
                  </AnimateButton>
                </Grid>
              </Grid>
            </Form>
          );
        }}
      </Formik>

      <Dialog
        open={showNgoPopup}
        onClose={() => setShowNgoPopup(false)}
        PaperProps={{
          sx: {
            borderRadius: '16px',
            width: '40%'
          }
        }}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>Confirm if same NGO as {payloadData?.fullname}</DialogTitle>
        <DialogContent>
          <FormControl component="fieldset">
            <RadioGroup
              aria-label="ngo-selection"
              name="ngo-selection"
              value={selectedNGO?.id || ''}
              onChange={(e) => {
                const selectedId = e.target.value;
                const selectedNgo = ngos.find((ngo) => String(ngo.id) === selectedId);
                setSelectedNgo(selectedNgo);
              }}
            >
              {ngos?.map((ngo) => (
                <FormControlLabel
                  key={ngo.id}
                  value={String(ngo.id)} // Ensure both value and selectedNgo.id are strings
                  control={<Radio />}
                  label={
                    <Box>
                      <Typography variant="subtitle1" style={{ fontWeight: 500 }}>
                        {ngo.name}
                      </Typography>
                      <Typography variant="body2" style={{ color: '#555' }}>
                        {ngo.current_address}
                      </Typography>
                    </Box>
                  }
                />
              ))}
            </RadioGroup>
          </FormControl>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setShowNgoPopup(false)} color="secondary">
            Back
          </Button>
          <Button
            onClick={() => {
              if (selectedNGO) {
                setShowNgoPopup(false);
                setShowConfirmationDialog(true);
                handleConfirmNgoAssociation();
              } else {
                openSnackbar({
                  open: true,
                  message: 'Please select NGO before proceeding.',
                  variant: 'alert',
                  alert: { color: 'warning' }
                });
              }
            }}
            color="primary"
            variant="contained"
          >
            Confirm and Proceed
          </Button>
        </DialogActions>
      </Dialog>

      <Dialog
        open={showConfirmationDialog}
        onClose={() => setShowConfirmationDialog(false)}
        PaperProps={{
          sx: {
            borderRadius: '16px'
          }
        }}
      >
        <DialogTitle>We will reach you soon</DialogTitle>
        <DialogContent>
          <Typography variant="h6">
            We have identified that you are a registered NGO. Our backend team will reach out to you shortly to complete the activation
            process. Once activated, you’ll be able to access the Doright application. Thank you for your patience!
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button
            onClick={() => {
              setShowConfirmationDialog(false);
              navigate('/login', { replace: true });
            }}
            color="primary"
          >
            Okay
          </Button>
        </DialogActions>
      </Dialog>

      <ToastContainer />
    </>
  );
}
