import React, { useEffect, useState } from 'react';
import axios from 'axios';
import CustomReactTable from 'components/CustomerDataGrid';
import {
  Button,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  TextField,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  FormHelperText,
  Box,
  Tabs,
  Tab,
  Stack,
  Tooltip,
  IconButton
} from '@mui/material';
import { toast, ToastContainer } from 'react-toastify';
import CustomerTable from 'sections/apps/customer/CustomerTable';
import { getSessionStorageItem, getUserPermissions } from 'utils/permissionUtils';
import 'react-toastify/dist/ReactToastify.css';
import { API_BASE_URL } from 'api/categories.service';
import { deleteDocumentList, getDocumentsMasterList, insertDocumentList, updateDocumentList } from './apis/documents.service';
import TableActions from 'components/TableActions';
import useAuth from 'hooks/useAuth';
import CustomerTableGrouping from 'sections/apps/customer/CustomerTableGrouping';
import { getAllNgoTypes } from 'api/ngotypes.service';
import dayjs from 'dayjs';
import MainCard from 'components/MainCard';
import { DeleteOutlined, PlusOutlined } from '@ant-design/icons';

export default function DocumentsTable() {
  const { user } = useAuth();
  const [documents, setDocuments] = useState([]);
  const [openDialog, setOpenDialog] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [currentDocument, setCurrentDocument] = useState(null);
  const currentUser = getSessionStorageItem('user');
  const [ngoTypes, setNgoTypes] = useState([]);
  const [formErrors, setFormErrors] = useState({});
  const [selectedTab, setSelectedTab] = useState(0);

  //role based acess
  const permissions = getUserPermissions(user);
  const canAdd = permissions?.Add?.Document || false;
  const canEdit = permissions?.Edit?.Document || false;
  const canDelete = permissions?.Delete?.Document || false;

  useEffect(() => {
    fetchNgoTypes();
    fetchDocuments();
  }, []);

  const fetchDocuments = async () => {
    try {
      const response = await getDocumentsMasterList();
      setDocuments(response);
    } catch (error) {
      console.error('Failed to fetch documents:', error);
      toast.error('Failed to fetch documents');
    }
  };

  const fetchNgoTypes = async () => {
    try {
      const response = await getAllNgoTypes();
      setNgoTypes(response);
    } catch (error) {
      console.error('Failed to fetch documents:', error);
      toast.error('Failed to fetch documents');
    }
  };

  const validateForm = () => {
    const errors = {};
    if (!currentDocument?.name) errors.name = 'Name is required';
    if (!currentDocument?.description) errors.description = 'Description is required';
    if (!currentDocument?.size) errors.size = 'Size is required';
    if (!currentDocument?.type) errors.type = 'Entity type is required';
    if (!currentDocument?.document_type) errors.document_type = 'NGO Type is required';
    if (!currentDocument?.mandatory) errors.mandatory = 'Mandatory field is required';
    if (!currentDocument?.filetype) errors.filetype = 'File Type is required';
    if (!currentDocument?.status) errors.filetype = 'Status is required';

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };
  const handleAddOrEdit = async () => {
    if (!validateForm()) {
      toast.error('Please fill in all required fields.');
      return;
    }
    try {
      if (isEditing) {
        let documentInfo = { ...currentDocument };
        documentInfo.updatedBy = currentUser.id;
        await updateDocumentList(currentDocument.id, documentInfo);
        toast.success('Document updated successfully!');
      } else {
        let documentInfo = { ...currentDocument };
        documentInfo.createdBy = currentUser.id;
        await insertDocumentList(documentInfo);
        toast.success('Document added successfully!');
      }
      setOpenDialog(false);
      fetchDocuments();
    } catch (error) {
      console.error('Failed to save document:', error);
      toast.error('Failed to save document');
    }
  };

  const handleDelete = async (document) => {
    if (!canDelete) {
      toast.error('You do not have permission to delete Documents.');
      return;
    }
    const confirmDelete = window.confirm('Are you sure you want to delete this document?');
    if (confirmDelete) {
      try {
        const updatedDocument = { ...document, isDeleted: 'yes' };
        // await deleteDocumentList(id);
        await updateDocumentList(updatedDocument?.id, updatedDocument);
        toast.success('Document deleted successfully!');
        fetchDocuments();
      } catch (error) {
        console.error('Failed to delete document:', error);
        toast.error('Failed to delete document');
      }
    }
  };

  const openAddDialog = () => {
    if (!canAdd) {
      toast.error('You do not have permission to Add Documents.');
      return;
    }
    setCurrentDocument({ name: '', description: '', size: '', type: '', status: 'Active' });
    setIsEditing(false);
    setFormErrors({});

    setOpenDialog(true);
  };

  const openEditDialog = (document) => {
    if (!canEdit) {
      toast.error('You do not have permission to edit Documents.');
      return;
    }
    setCurrentDocument(document);
    setIsEditing(true);
    setFormErrors({});

    setOpenDialog(true);
  };
  const columns = [
    {
      accessorKey: 'name',
      header: 'Name',
      showByDefault: true,
      cell: ({ cell }) => {
        return (
          <Button className="text-align-left" variant="text" onClick={() => openEditDialog(cell.row.original)}>
            {cell.row.original.name}
          </Button>
        );
      }
    },
    { accessorKey: 'description', header: 'Description', showByDefault: true },
    { accessorKey: 'size', header: 'Size', showByDefault: true },
    { accessorKey: 'type', header: 'Type', showByDefault: true },
    { accessorKey: 'mandatory', header: 'Mandatory', showByDefault: true },
    { accessorKey: 'isDeleted', header: 'Deleted?', showByDefault: true },
    // { accessorKey: 'document_type', header: 'Ngo Type', showByDefault: true },
    { accessorKey: 'filetype', header: 'Filetype', showByDefault: true },
    { accessorKey: 'status', header: 'Status', showByDefault: true },

    {
      accessorKey: 'createdAt',
      header: 'Created At',
      showByDefault: true,
      cell: (cell) => {
        const formattedDate = dayjs(cell.row.original.createdAt).format('DD-MM-YYYY hh:mm A');
        return <span>{formattedDate}</span>;
      }
    },
    {
      accessorKey: 'updatedAt',
      header: 'Updated At',
      showByDefault: true,
      cell: (cell) => {
        const formattedDate = dayjs(cell.row.original.updatedAt).format('DD-MM-YYYY hh:mm A');
        return <span>{formattedDate}</span>;
      }
    },
    {
      id: 'actions',
      header: 'Actions',
      cell: (cell) => {
        return (
          <Box display="flex" alignItems="center">
            <TableActions handleEditClick={openEditDialog} cell={cell} handleDeleteClick={handleDelete} showDelete={false} />

            <Tooltip title="Delete">
              <IconButton
                color="error"
                size="medium"
                onClick={() => {
                  handleDelete(cell.row.original);
                }}
                style={{ marginLeft: '8px' }}
              >
                <DeleteOutlined />
              </IconButton>
            </Tooltip>
          </Box>
        );
      }
    }
  ];

  const handleChange = (field, value) => {
    setCurrentDocument({ ...currentDocument, [field]: value });
    setFormErrors({ ...formErrors, [field]: '' }); // Clear error when user types/selects
  };

  const handleTabChange = (event, newValue) => {
    setSelectedTab(newValue);
  };
  const filteredDocuments = documents.filter((doc) => {
    const documentTypes = doc.document_type.split(',').map((type) => type.trim());
    return documentTypes.includes(ngoTypes[selectedTab]?.name);
  });
  const documentCounts = ngoTypes.map((ngo) => {
    return documents.filter((doc) => {
      const documentTypes = doc.document_type.split(',').map((type) => type.trim());
      return documentTypes.includes(ngo.name);
    }).length;
  });
  return (
    <Box>
      <MainCard>
        <Stack spacing={3}>
          <Stack direction="row" justifyContent="space-between" alignItems="center">
            <Tabs value={selectedTab} onChange={handleTabChange} variant="scrollable" scrollButtons="auto">
              {ngoTypes.map((ngo, index) => (
                <Tab key={ngo.id} label={`${ngo.name} (${documentCounts[index]})`} />
              ))}
            </Tabs>
            <Stack direction="row" alignItems="center" spacing={2}>
              {canAdd && (
                <Button variant="contained" size="small" startIcon={<PlusOutlined />} color="primary" onClick={openAddDialog}>
                  Add Document
                </Button>
              )}
            </Stack>
          </Stack>
        </Stack>
        <CustomerTable data={filteredDocuments} columns={columns} modalToggler={openAddDialog} category={'Document'} />{' '}
      </MainCard>
      <Dialog
        open={openDialog}
        onClose={(event, reason) => {
          if (reason !== 'backdropClick' && reason !== 'escapeKeyDown') {
            setOpenDialog(false);
          }
        }}
        maxWidth="50%"
        PaperProps={{
          sx: {
            top: 0,
            position: 'absolute',
            borderRadius: '16px',
            width: '100%',
            maxWidth: '600px'
          }
        }}
      >
        <DialogTitle>{isEditing ? 'Edit Document' : 'Add Document'}</DialogTitle>
        <DialogContent>
          <TextField
            autoFocus
            margin="dense"
            label="Name"
            type="text"
            fullWidth
            variant="outlined"
            value={currentDocument?.name || ''}
            onChange={(e) => handleChange('name', e.target.value)}
            error={!!formErrors.name}
            helperText={formErrors.name}
          />
          <TextField
            margin="dense"
            label="Description"
            type="text"
            fullWidth
            variant="outlined"
            value={currentDocument?.description || ''}
            onChange={(e) => handleChange('description', e.target.value)}
            error={!!formErrors.description}
            helperText={formErrors.description}
          />
          <TextField
            margin="dense"
            label="Size (in MB's)"
            type="text"
            fullWidth
            variant="outlined"
            value={currentDocument?.size || ''}
            onChange={(e) => handleChange('size', e.target.value)}
            error={!!formErrors.size}
            helperText={formErrors.size}
          />
          <FormControl fullWidth margin="dense" variant="outlined" error={!!formErrors.type}>
            <InputLabel>Entity</InputLabel>
            <Select value={currentDocument?.type || ''} onChange={(e) => handleChange('type', e.target.value)} label="Entity">
              <MenuItem value="NGO">NGO</MenuItem>
              {/* <MenuItem value="Admin">DR_Management</MenuItem> */}
            </Select>
            <FormHelperText>{formErrors.type}</FormHelperText>
          </FormControl>
          <FormControl fullWidth margin="dense" variant="outlined" error={!!formErrors.document_type}>
            <InputLabel>NGO Type</InputLabel>
            <Select
              multiple
              value={currentDocument?.document_type?.split(',') || []} // Convert string to array for display
              onChange={(e) => handleChange('document_type', e.target.value.join(','))}
              label="NGO Type"
              renderValue={(selected) => selected.join(', ')} // Display selected values as comma-separated
            >
              {ngoTypes && ngoTypes.map((ngotype) => <MenuItem value={ngotype?.name}>{ngotype?.name}</MenuItem>)}
              {/* <MenuItem value="Society">Society</MenuItem>
      <MenuItem value="Trust">Trust</MenuItem> */}
            </Select>
            <FormHelperText>{formErrors.document_type}</FormHelperText>
          </FormControl>

          <FormControl fullWidth margin="dense" variant="outlined" error={!!formErrors.mandatory}>
            <InputLabel>Mandatory</InputLabel>
            <Select value={currentDocument?.mandatory || ''} onChange={(e) => handleChange('mandatory', e.target.value)} label="Mandatory">
              <MenuItem value="yes">Yes</MenuItem>
              <MenuItem value="no">No</MenuItem>
            </Select>
            <FormHelperText>{formErrors.mandatory}</FormHelperText>
          </FormControl>

          <FormControl fullWidth margin="dense" variant="outlined" error={!!formErrors.filetype}>
            <InputLabel>File Type</InputLabel>
            <Select
              multiple
              value={currentDocument?.filetype?.split(',') || []} // Convert string to array for display
              onChange={(e) => handleChange('filetype', e.target.value.join(','))}
              label="File Type"
              renderValue={(selected) => selected.join(', ')} // Display selected values as comma-separated
            >
              <MenuItem value="PDF">PDF</MenuItem>
              <MenuItem value="Image">Image</MenuItem>
            </Select>
            <FormHelperText>{formErrors.filetype}</FormHelperText>
          </FormControl>
          <FormControl fullWidth margin="dense" variant="outlined" error={!!formErrors.status}>
            <InputLabel>Status</InputLabel>
            <Select value={currentDocument?.status || ''} onChange={(e) => handleChange('status', e.target.value)} label="Status">
              <MenuItem value="Active">Active</MenuItem>
              <MenuItem value="Inactive">Inactive</MenuItem>
            </Select>
            <FormHelperText>{formErrors.status}</FormHelperText>
          </FormControl>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setOpenDialog(false)} color="primary">
            Cancel
          </Button>
          <Button onClick={handleAddOrEdit} color="primary">
            {isEditing ? 'Update' : 'Add'}
          </Button>
        </DialogActions>
      </Dialog>

      <ToastContainer />
    </Box>
  );
}
