import axiosServices from 'utils/axios_node';

const API_BASE_URL = `${import.meta.env.VITE_APP_APPLICATION_API_URL}/api`;

export const getCampaignImages = async (id) => {
  try {
    const url = id ? `${API_BASE_URL}/campaign-images/${id}` : `${API_BASE_URL}/campaign-images`;
    const response = await axiosServices.get(url);
    return response?.data;
  } catch (error) {
    console.error('Error fetching Campaign images data data');
    return error?.message;
  }
};
export const addCampaignImageService = async (payload) => {
  try {
    const response = await axiosServices.post(`${API_BASE_URL}/campaign-images`, payload);
    return response;
  } catch (error) {
    console.error('Error CampaignImage data');
    return error?.message;
  }
};
export const updateCampaignImageService = async (id, payload) => {
  try {
    const response = await axiosServices.put(`${API_BASE_URL}/campaign-images/${id}`, payload);
    return response;
  } catch (error) {
    console.error('Error campaignImage data');
    return error?.message;
  }
};

export const updateCampaignPromoImageService = async (id, payload) => {
  try {
    const response = await axiosServices.put(`${API_BASE_URL}/campaign-images/promoImages/${id}`, payload);
    return response;
  } catch (error) {
    console.error('Error campaignImage data');
    return error?.message;
  }
};

export const deleteCampaignImageService = async (id) => {
  try {
    const response = await axiosServices.delete(`${API_BASE_URL}/campaign-images/${id}`);
    return response;
  } catch (error) {
    console.error('Error campaignImage data');
    return error?.message;
  }
};
export const getNGOImages = async (id) => {
  try {
    const url = id ? `${API_BASE_URL}/ngo-images/${id}` : `${API_BASE_URL}/ngo-images`;
    const response = await axiosServices.get(url);
    return response?.data;
  } catch (error) {
    console.error('Error fetching ngo images data data');
    return error?.message;
  }
};
export const addNGOImageService = async (payload) => {
  try {
    const response = await axiosServices.post(`${API_BASE_URL}/ngo-images`, payload);
    return response;
  } catch (error) {
    console.error('Error NGOImage data');
    return error?.message;
  }
};
export const updateNGOImageService = async (id, payload) => {
  try {
    const response = await axiosServices.put(`${API_BASE_URL}/ngo-images/${id}`, payload);
    return response;
  } catch (error) {
    console.error('Error NGOImage data');
    return error?.message;
  }
};
export const deleteNGOImageService = async (id) => {
  try {
    const response = await axiosServices.delete(`${API_BASE_URL}/ngo-images/${id}`);
    return response;
  } catch (error) {
    console.error('Error NGOImage data');
    return error?.message;
  }
};

export const getProductImages = async (id) => {
  try {
    const url = id ? `${API_BASE_URL}/product-images/${id}` : `${API_BASE_URL}/product-images`;
    const response = await axiosServices.get(url);
    return response?.data;
  } catch (error) {
    console.error('Error fetching product images data data');
    return error?.message;
  }
};
export const addProductImageService = async (payload) => {
  try {
    const response = await axiosServices.post(`${API_BASE_URL}/product-images`, payload);
    return response;
  } catch (error) {
    console.error('Error product image  data');
    return error?.message;
  }
};
export const updateProductImageService = async (id, payload) => {
  try {
    const response = await axiosServices.put(`${API_BASE_URL}/product-images/${id}`, payload);
    return response;
  } catch (error) {
    console.error('Error product image data');
    return error?.message;
  }
};
export const deleteProductImageService = async (id) => {
  try {
    const response = await axiosServices.delete(`${API_BASE_URL}/product-images/${id}`);
    return response;
  } catch (error) {
    console.error('Error product image data');
    return error?.message;
  }
};

export const getCategoryImages = async (id) => {
  try {
    const url = id ? `${API_BASE_URL}/category-images/${id}` : `${API_BASE_URL}/category-images`;
    const response = await axiosServices.get(url);
    return response?.data;
  } catch (error) {
    console.error('Error fetching category images data data');
    return error?.message;
  }
};

export const addCategoryImageService = async (payload) => {
  try {
    const response = await axiosServices.post(`${API_BASE_URL}/category-images`, payload);
    return response;
  } catch (error) {
    console.error('Error category image data');
    return error?.message;
  }
};

export const updateCategoryImageService = async (id, payload) => {
  try {
    const response = await axiosServices.put(`${API_BASE_URL}/category-images/${id}`, payload);
    return response;
  } catch (error) {
    console.error('Error category image data');
    return error?.message;
  }
};

export const deleteCategoryImageService = async (id) => {
  try {
    const response = await axiosServices.delete(`${API_BASE_URL}/category-images/${id}`);
    return response;
  } catch (error) {
    console.error('Error category image data');
    return error?.message;
  }
};
