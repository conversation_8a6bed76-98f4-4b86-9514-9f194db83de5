import React from 'react';
import { Grid, Paper, Typography, Box } from '@mui/material';
import { useNavigate } from 'react-router-dom';

import { UsergroupAddOutlined, BankOutlined } from '@ant-design/icons';

const options = [
  {
    label: 'Campaign',
    description: 'Launch a donation drive campaign to collect funds for a cause',
    icon: <BankOutlined />,
    route: '/masters/campaigns/add/campaign'
  },
  {
    label: 'Event',
    description: 'Create an event requiring volunteer participation, physical or virtual.',
    icon: <UsergroupAddOutlined />,
    route: '/masters/campaigns/add/event'
  }
];

export default function InitiativeSelector() {
  const navigate = useNavigate();

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h5" sx={{ fontWeight: 600, mb: 1 }}>
        Create New Initiative
      </Typography>
      <Typography variant="subtitle1" color="text.secondary" sx={{ mb: 3 }}>
        Choose the type of initiative you want to create
      </Typography>

      <Grid container spacing={3}>
        {options.map((option) => (
          <Grid item xs={12} md={6} key={option.label}>
            <Paper
              elevation={option.selected ? 4 : 1}
              onClick={() => navigate(option.route)}
              sx={{
                cursor: 'pointer',
                p: 3,
                borderRadius: 2,
                border: '2px solid transparent',
                '&:hover': {
                  borderColor: 'primary.main',
                  boxShadow: 4
                },
                display: 'flex',
                alignItems: 'flex-start',
                gap: 2,
                transition: 'all 0.3s ease'
              }}
            >
              <Box>{option.icon}</Box>
              <Box>
                <Typography variant="h6" sx={{ fontWeight: 600 }}>
                  {option.label}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  {option.description}
                </Typography>
              </Box>
            </Paper>
          </Grid>
        ))}
      </Grid>
    </Box>
  );
}
