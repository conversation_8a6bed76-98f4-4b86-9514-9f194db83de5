import React, { useEffect, useState } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import axios from 'axios';
import {
  Autocomplete,
  Box,
  Button,
  Container,
  Divider,
  FormControl,
  Grid,
  InputLabel,
  MenuItem,
  Select,
  TextField,
  Typography,
  useTheme
} from '@mui/material';
import { Editor } from 'react-draft-wysiwyg';
import { ContentState, EditorState, convertFromRaw, convertToRaw } from 'draft-js';
import 'react-draft-wysiwyg/dist/react-draft-wysiwyg.css';
import { toast, ToastContainer } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import { API_BASE_URL } from 'api/categories.service';
import MainCard from 'components/MainCard';
import { EMAIL_TITLE_LIST } from 'utils/statusconstans';
import { addEmailTemplate, editEmailTemplate, getSingleEmailTemplate } from './email.service';

export default function EmailTemplateForm() {
  const { templateId } = useParams();
  const navigate = useNavigate();
  const theme = useTheme();

  const [template, setTemplate] = useState({
    title: '',
    subject: '',
    status: 'Active',
    template: ''
  });

  const [editorState, setEditorState] = useState(() => EditorState.createEmpty());
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (templateId) {
      fetchTemplate(templateId);
    }
  }, [templateId]);

  const fetchTemplate = async (id) => {
    try {
      setLoading(true);
      const fetchedTemplate = await getSingleEmailTemplate(id);

      setTemplate({
        title: fetchedTemplate.title,
        subject: fetchedTemplate.subject,
        status: fetchedTemplate.status,
        template: fetchedTemplate.template
      });

      const contentState = fetchedTemplate.template
        ? convertFromRaw(JSON.parse(fetchedTemplate.template))
        : ContentState.createFromText('');

      setEditorState(EditorState.createWithContent(contentState));
    } catch (error) {
      toast.error('Failed to fetch email template');
    } finally {
      setLoading(false);
    }
  };

  const handleSave = async () => {
    try {
      const updatedTemplate = {
        ...template,
        template: JSON.stringify(convertToRaw(editorState.getCurrentContent()))
      };

      if (templateId) {
        await editEmailTemplate(templateId, updatedTemplate);
        toast.success('Email template updated successfully!');
      } else {
        await addEmailTemplate(updatedTemplate);
        toast.success('Email template added successfully!');
      }
      setTimeout(() => {
        navigate('/masters/emails');
      }, 800);
      //   navigate('/masters/emails');
    } catch (error) {
      console.error('Failed to save email template:', error);
      toast.error('Failed to save email template');
    }
  };

  return (
    <MainCard>
      <Typography fontSize={20}>{templateId ? 'Edit Email Template' : 'Add Email Template'}</Typography>
      <Divider />
      <Grid container spacing={2} marginTop={1}>
        <Grid item xs={12}>
          <Autocomplete
            id="title"
            value={template.title}
            onChange={(e, nextValue) => {
              setTemplate({ ...template, title: nextValue });
            }}
            options={EMAIL_TITLE_LIST.map((item) => item)}
            renderInput={(params) => (
              <TextField
                {...params}
                placeholder="Select Title"
                sx={{ '& .MuiAutocomplete-input.Mui-disabled': { WebkitTextFillColor: theme.palette.text.primary } }}
              />
            )}
          />
        </Grid>
        <Grid item xs={12}>
          <TextField
            margin="dense"
            label="Subject"
            type="text"
            fullWidth
            required
            variant="outlined"
            value={template.subject}
            onChange={(e) => setTemplate({ ...template, subject: e.target.value })}
          />
        </Grid>
        <Grid item xs={12}>
          <FormControl fullWidth margin="dense" variant="outlined">
            <InputLabel>Status</InputLabel>
            <Select required value={template.status} onChange={(e) => setTemplate({ ...template, status: e.target.value })} label="Status">
              <MenuItem value="Active">Active</MenuItem>
              <MenuItem value="Inactive">Inactive</MenuItem>
            </Select>
          </FormControl>
        </Grid>
        <Grid item xs={12}>
          <Box style={{ marginTop: '16px' }} border={1} padding={2}>
            <Editor
              editorState={editorState}
              toolbarClassName="toolbarClassName"
              wrapperClassName="wrapperClassName"
              editorClassName="editorClassName"
              toolbar={{
                options: ['inline', 'blockType', 'fontSize', 'list', 'textAlign', 'colorPicker', 'link', 'embedded', 'remove', 'history']
              }}
              onEditorStateChange={setEditorState}
            />
          </Box>
        </Grid>
        <Grid item xs={12}>
          <Button onClick={() => navigate('/masters/emails')} color="primary">
            Cancel
          </Button>
          <Button onClick={handleSave} color="primary">
            {templateId ? 'Update' : 'Add'}
          </Button>
        </Grid>
      </Grid>
      <ToastContainer />
    </MainCard>
  );
}
