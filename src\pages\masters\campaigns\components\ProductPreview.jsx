import {
  Avatar,
  Box,
  Button,
  Card,
  CardContent,
  CardMedia,
  Chip,
  CircularProgress,
  Divider,
  FormControl,
  Grid,
  InputLabel,
  List,
  ListItem,
  MenuItem,
  Select,
  Stack,
  Typography,
  useMediaQuery
} from '@mui/material';
import React, { Fragment, useEffect, useState } from 'react';
import { ToastContainer, toast } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import { Editor } from 'react-draft-wysiwyg';
import { ContentState, EditorState, convertToRaw } from 'draft-js';
import 'react-draft-wysiwyg/dist/react-draft-wysiwyg.css';
import MainCard from 'components/MainCard';
import FeedsCard from 'sections/widget/data/FeedsCard';
import { addNotifications, getNotifications } from 'pages/masters/apis/notification.service';
import { BASE_URL } from 'sections/apps/profiles/profile.service';
import { getSingleProduct, patchProduct } from 'pages/masters/products/product.service';
import {
  AppstoreOutlined,
  DollarCircleOutlined,
  MoneyCollectOutlined,
  ShoppingCartOutlined,
  TagOutlined,
  WalletOutlined
} from '@ant-design/icons';
import ListItemDividerComponent from './ListItemDividerComponent';
import useAuth from 'hooks/useAuth';
import { useParams } from 'react-router';
import defaultImages from 'assets/images/users/default.png';

import avatarImage from 'src/assets/images/profile/user-profile-1.png';

export default function ProductPreview() {
  const matchDownMD = useMediaQuery((theme) => theme.breakpoints.down('md'));
  const avatarImage = () => {
    return defaultImages;
  };
  const { user } = useAuth();
  const { productId } = useParams();
  const [commentseditorState, setCommentsEditorState] = useState(() => EditorState.createEmpty());
  const [loading, setLoading] = useState(false);
  const [comments, setComments] = useState([]);
  const [productDetails, setProductDetails] = useState([]);

  const fetchComments = async (productId, ngoId) => {
    try {
      if (productId) {
        const response = await getNotifications(ngoId, 'products', productId);
        setComments(response || []);
      }
    } catch (error) {
      console.error('Failed to fetch comments:', error);
      toast.error('Failed to fetch comments');
    }
  };

  const fetchProductDetails = async (productId) => {
    try {
      if (productId) {
        const response = await getSingleProduct(productId);
        setProductDetails(response || []);
      }
    } catch (error) {
      console.error('Failed to fetch product details:', error);
      toast.error('Failed to fetch product details');
    }
  };

  useEffect(() => {
    if (productDetails?.id) {
      fetchComments(productDetails?.id, productDetails?.ngo_id);
    }
    fetchProductDetails(productId);
  }, [productDetails?.id, productId]);

  const handleCommentSubmit = async (e) => {
    e.preventDefault();

    const contentState = commentseditorState.getCurrentContent();
    const description = JSON.stringify(convertToRaw(contentState));

    if (!contentState.hasText()) {
      toast.error('The required field is empty. Please fill it out.');
      return;
    }

    const payload = {
      type_id: productDetails?.id,
      description,
      messageRead: 'no',
      type: 'products',
      sender_id: user?.id,
      ngo_id: productDetails?.ngo_id
    };

    try {
      setLoading(true);
      const response = await addNotifications(payload);
      if (response.status) {
        toast.success('Comment added successfully!');
        setCommentsEditorState(EditorState.createEmpty());
        fetchComments(productDetails?.id);
      } else {
        throw new Error('Unexpected response');
      }
    } catch (error) {
      toast.error('Failed to add comment');
      console.error('Error adding comment:', error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <Grid container spacing={3}>
      <Grid item xs={12} sm={5} md={4} xl={3.5}>
        <Grid container spacing={3}>
          <Grid item xs={12}>
            <MainCard>
              <Stack spacing={2.5} alignItems="center">
                {productDetails?.name ? (
                  <CardMedia
                    component="img"
                    alt={productDetails?.name}
                    height="180"
                    width="10"
                    image={`${BASE_URL}/fetchProductImages/${productDetails?.imageName}`}
                    style={{ objectFit: 'fill' }}
                  />
                ) : (
                  // <Avatar alt="Avatar 1" size="xl" src={avatarImage(`./default.png`)} />
                  <Typography height="180" width="10" fontWeight={'bold'}>
                    No Image
                  </Typography>
                )}

                <Typography variant="h5">{productDetails?.name ? productDetails?.name : '-'}</Typography>
              </Stack>
              <Divider sx={{ my: 2 }} />
              <Box
                sx={{
                  borderRadius: 2,
                  padding: 1
                }}
              >
                <Stack spacing={2}>
                  <Stack direction="row" alignItems="center" spacing={1}>
                    <WalletOutlined style={{ fontSize: '1.5rem', color: '#1976d2' }} />
                    <Typography variant="body1">
                      <strong>Price:</strong> ₹{productDetails?.price ? productDetails?.price : '-'}
                    </Typography>
                  </Stack>
                  <Stack direction="row" alignItems="center" spacing={1}>
                    <TagOutlined style={{ fontSize: '1.5rem', color: '#43a047' }} />
                    <Typography variant="body1">
                      <strong>Discounted Price:</strong>{' '}
                      {productDetails?.discountedPrice && productDetails?.discountedPrice != 0
                        ? `₹${productDetails?.discountedPrice}`
                        : '-'}
                    </Typography>
                  </Stack>

                  <Stack direction="row" alignItems="center" spacing={1}>
                    <ShoppingCartOutlined style={{ fontSize: '1.5rem', color: '#f44336' }} />
                    <Typography variant="body1">
                      <strong>In Stock:</strong>
                      {productDetails?.isStock === 'Yes' ? 'Yes' : productDetails?.isStock === 'No' ? 'No' : '-'}
                    </Typography>
                  </Stack>
                </Stack>
              </Box>
            </MainCard>
          </Grid>
          <Grid item xs={12}>
            <MainCard title="Comments">
              <FeedsCard comments={comments} />
            </MainCard>
          </Grid>
        </Grid>
      </Grid>

      <Grid item xs={12} sm={7} md={8} xl={8}>
        <Grid item xs={12}>
          <Grid item xs={12}>
            <MainCard title="Product info">
              <List sx={{ py: 0 }}>
                <ListItem divider>
                  <Grid container spacing={matchDownMD ? 0.5 : 3}>
                    <ListItemDividerComponent name="Description" value={productDetails?.description ? productDetails?.description : '-'} />
                    <ListItemDividerComponent
                      name="Discount"
                      value={productDetails?.discount && productDetails?.discount != 0 ? `${productDetails?.discount}%` : '-'}
                    />
                  </Grid>
                </ListItem>
                <ListItem>
                  <Grid container spacing={matchDownMD ? 0.5 : 3}>
                    <ListItemDividerComponent
                      name="Unit of measure"
                      value={productDetails?.unit_of_measure ? productDetails?.unit_of_measure : '-'}
                    />

                    <ListItemDividerComponent
                      name="Category"
                      value={productDetails?.collectionInfo?.name ? productDetails?.collectionInfo?.name : '-'}
                    />
                    {!user?.ngo_id && (
                      <ListItemDividerComponent
                        name="NGO name"
                        value={productDetails?.ngoInfo?.name ? productDetails?.ngoInfo?.name : '-'}
                      />
                    )}
                  </Grid>
                </ListItem>
              </List>
            </MainCard>
          </Grid>
        </Grid>

        {user?.roleInfo?.name.startsWith('DR') && (
          <Grid item xs={12} marginTop={3}>
            <Grid container spacing={3}>
              <Grid item xs={12}>
                <MainCard title="Add Comments">
                  <form onSubmit={handleCommentSubmit}>
                    <Grid container spacing={2}>
                      <Grid item xs={12}>
                        <Box style={{ marginTop: '16px' }} border={'1px solid #ccc'} padding={2} minHeight={250}>
                          <Editor
                            editorState={commentseditorState}
                            toolbarClassName="toolbarClassName"
                            wrapperClassName="wrapperClassName"
                            editorClassName="editorClassName"
                            toolbar={{
                              options: ['inline', 'fontSize', 'link'],
                              fontSize: {
                                options: [8, 10, 12, 14, 16]
                              },
                              inline: {
                                inDropdown: false,
                                className: undefined,
                                component: undefined,
                                dropdownClassName: undefined,
                                options: ['bold', 'italic', 'underline', 'strikethrough']
                              }
                            }}
                            onEditorStateChange={setCommentsEditorState}
                          />
                        </Box>
                      </Grid>
                      <Grid item xs={12}>
                        <div style={{ display: 'flex', justifyContent: 'end' }}>
                          <Button
                            type="submit"
                            variant="contained"
                            color="primary"
                            disabled={loading}
                            startIcon={loading && <CircularProgress size={20} color="inherit" />}
                          >
                            {loading ? 'Sending...' : 'Submit'}
                          </Button>
                        </div>
                      </Grid>
                    </Grid>
                  </form>
                </MainCard>
              </Grid>
            </Grid>
          </Grid>
        )}
      </Grid>

      <ToastContainer autoClose={6000} />
    </Grid>
  );
}
