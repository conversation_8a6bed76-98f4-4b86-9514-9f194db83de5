import React, { useEffect, useState } from 'react';
import {
  Button,
  <PERSON>alog,
  DialogActions,
  DialogContent,
  DialogTitle,
  TextField,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Box,
  Tooltip,
  IconButton
} from '@mui/material';
import CustomerTable from 'sections/apps/customer/CustomerTable';
import { getUserPermissions } from 'utils/permissionUtils';
import { toast, ToastContainer } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import { getAllCategories, addCategory, updateCategory, deleteCategory } from 'api/categories.service';
import TableActions from 'components/TableActions';
import useAuth from 'hooks/useAuth';
import dayjs from 'dayjs';
import { FileImageOutlined } from '@ant-design/icons';
import { useNavigate } from 'react-router';

export default function CategoriesTable() {
  const { user } = useAuth();
  const [categories, setCategories] = useState([]);
  const [openDialog, setOpenDialog] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [currentCategory, setCurrentCategory] = useState(null);
  const navigate = useNavigate();

  const permissions = getUserPermissions(user);
  const canAdd = permissions?.Add?.Categories || true;
  const canEdit = permissions?.Edit?.Categories || false;
  const canDelete = permissions?.Delete?.Categories || false;

  useEffect(() => {
    fetchCategories();
  }, []);

  const fetchCategories = async () => {
    try {
      const response = await getAllCategories();
      setCategories(response);
    } catch (error) {
      console.error('Failed to fetch categories:', error);
      toast.error('Failed to fetch categories');
    }
  };

  const handleAddOrEdit = async () => {
    try {
      if (isEditing) {
        await updateCategory(currentCategory.id, currentCategory);
        toast.success('Category updated successfully!');
      } else {
        await addCategory(currentCategory);
        toast.success('Category added successfully!');
      }
      setOpenDialog(false);
      fetchCategories();
    } catch (error) {
      console.error('Failed to save category:', error);
      toast.error('Failed to save category');
    }
  };

  const handleDelete = async (id) => {
    const confirmDelete = window.confirm('Are you sure you want to delete this category?');
    if (confirmDelete) {
      try {
        await deleteCategory(id);
        toast.success('Category deleted successfully!');
        fetchCategories();
      } catch (error) {
        console.error('Failed to delete category:', error);
        toast.error('Failed to delete category');
      }
    }
  };

  const openAddDialog = () => {
    if (!canAdd) {
      toast.error('You do not have permission to Add Categories.');
      return;
    }
    setCurrentCategory({ id: 0, name: '', status: 'Active' });
    setIsEditing(false);
    setOpenDialog(true);
  };

  const openEditDialog = (category) => {
    if (!canEdit) {
      toast.error('You do not have permission to edit Categories.');
      return;
    }
    setCurrentCategory(category);
    setIsEditing(true);
    setOpenDialog(true);
  };

  const handleViewImages = (categoryId) => {
    navigate(`/masters/categories/category-images/${categoryId}`);
  };

  const columns = [
    {
      accessorKey: 'name',
      header: 'Name',
      showByDefault: true,
      cell: ({ cell }) => (
        <Button variant="text" onClick={() => openEditDialog(cell.row.original)}>
          {cell.row.original.name}
        </Button>
      )
    },
    { accessorKey: 'ratio', header: 'Ratio', showByDefault: true },
    { accessorKey: 'description', header: 'Description', showByDefault: true },
    { accessorKey: 'status', header: 'Status', showByDefault: true },
    {
      accessorKey: 'createdAt',
      header: 'Created At',
      showByDefault: true,
      cell: (cell) => {
        const formattedDate = dayjs(cell.row.original.createdAt).format('DD-MM-YYYY hh:mm A');
        return <span>{formattedDate}</span>;
      }
    },
    {
      accessorKey: 'updatedAt',
      header: 'Updated At',
      showByDefault: true,
      cell: (cell) => {
        const formattedDate = dayjs(cell.row.original.updatedAt).format('DD-MM-YYYY hh:mm A');
        return <span>{formattedDate}</span>;
      }
    },

    {
      id: 'actions',
      header: 'Actions',
      cell: (cell) => {
        const category = cell?.row?.original;

        return (
          <Box display="flex" flexDirection="row">
            <TableActions handleEditClick={openEditDialog} cell={cell} handleDeleteClick={handleDelete} />
            <Tooltip title="View Images">
              <IconButton
                color="success"
                size="medium"
                onClick={() => {
                  handleViewImages(category?.id);
                }}
                style={{ marginLeft: '8px' }}
              >
                <FileImageOutlined />
              </IconButton>
            </Tooltip>
          </Box>
        );
      }
    }
  ];

  return (
    <div>
      <CustomerTable data={categories} columns={columns} modalToggler={openAddDialog} category="Category" />

      <Dialog
        open={openDialog}
        onClose={(event, reason) => {
          if (reason !== 'backdropClick' && reason !== 'escapeKeyDown') {
            setOpenDialog(false);
          }
        }}
        maxWidth="50%"
        PaperProps={{
          sx: {
            top: 0,
            position: 'absolute',
            borderRadius: '16px',
            width: '100%',
            maxWidth: '600px'
          }
        }}
      >
        <DialogTitle>{isEditing ? 'Edit Category' : 'Add Category'}</DialogTitle>
        <DialogContent>
          <TextField
            autoFocus
            margin="dense"
            label="Name"
            type="text"
            fullWidth
            variant="outlined"
            value={currentCategory?.name || ''}
            onChange={(e) => setCurrentCategory({ ...currentCategory, name: e.target.value })}
            inputProps={{ maxLength: 200 }}
          />
          <TextField
            multiline
            margin="dense"
            label="Description"
            type="text"
            fullWidth
            variant="outlined"
            value={currentCategory?.description || ''}
            onChange={(e) => setCurrentCategory({ ...currentCategory, description: e.target.value })}
          />
          <TextField
            autoFocus
            margin="dense"
            label="Ratio"
            type="text"
            fullWidth
            variant="outlined"
            value={currentCategory?.ratio || ''}
            onChange={(e) => setCurrentCategory({ ...currentCategory, ratio: e.target.value })}
            inputProps={{ maxLength: 4 }}
          />
          <FormControl fullWidth margin="dense" variant="outlined">
            <InputLabel>Status</InputLabel>
            <Select
              value={currentCategory?.status || ''}
              onChange={(e) => setCurrentCategory({ ...currentCategory, status: e.target.value })}
              label="Status"
            >
              <MenuItem value="Active">Active</MenuItem>
              <MenuItem value="Inactive">Inactive</MenuItem>
            </Select>
          </FormControl>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setOpenDialog(false)} color="primary">
            Cancel
          </Button>
          <Button onClick={handleAddOrEdit} color="primary">
            {isEditing ? 'Update' : 'Add'}
          </Button>
        </DialogActions>
      </Dialog>

      <ToastContainer />
    </div>
  );
}
