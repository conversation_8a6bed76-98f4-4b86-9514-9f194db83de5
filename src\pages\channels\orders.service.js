import axiosServices from 'utils/axios_node';

const API_BASE_URL = `${import.meta.env.VITE_APP_APPLICATION_API_URL}/api`;

//orders function
export const fetchOrdersService = async (params = {}) => {
  try {
    const { user_id, ngo_id, product_id } = params;

    let queryString = '';

    if (user_id) queryString += `user_id=${user_id}&`;
    if (ngo_id) queryString += `ngo_id=${ngo_id}&`;
    if (product_id) queryString += `product_id=${product_id}&`;

    if (queryString.endsWith('&')) queryString = queryString.slice(0, -1);

    const url = queryString ? `${API_BASE_URL}/orders?${queryString}` : `${API_BASE_URL}/orders`;

    // Fetch orders with the constructed URL
    const response = await axiosServices.get(url);
    return response?.data;
  } catch (error) {
    console.error('Error fetching orders data', error);
    return error?.message;
  }
};

export const addOrderService = async (payload) => {
  try {
    const response = await axiosServices.post(`${API_BASE_URL}/orders`, payload);
    return response;
  } catch (error) {
    console.error('Error adding orders data');
    return error?.message;
  }
};
export const updateOrderService = async (id, payload) => {
  try {
    const response = await axiosServices.put(`${API_BASE_URL}/orders/${id}`, payload);
    return response;
  } catch (error) {
    console.error('Error updating orders data');
    return error?.message;
  }
};
export const patchOrderService = async (id, payload) => {
  try {
    const response = await axiosServices.patch(`${API_BASE_URL}/orders/${id}`, payload);
    return response;
  } catch (error) {
    console.error('Error updating orders data');
    return error?.message;
  }
};
export const deleteOrderService = async (id, payload) => {
  try {
    const response = await axiosServices.put(`${API_BASE_URL}/orders/${id}`, payload);
    return response;
  } catch (error) {
    console.error('Error deleting orders data');
    return error?.message;
  }
};
