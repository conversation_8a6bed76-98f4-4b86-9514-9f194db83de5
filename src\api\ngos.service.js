import axiosServices from 'utils/axios_node';
import mapboxgl from 'mapbox-gl';

export const API_BASE_URL = `${import.meta.env.VITE_APP_APPLICATION_API_URL}/api`;

export const getAllNgos = async () => {
  try {
    const response = await axiosServices.get(`${API_BASE_URL}/ngos`);
    return response?.data;
  } catch (error) {
    console.error('Failed to fetch NGOs:', error);
    toast.error('Failedto fetch that data');
  }
};
export const getNgoById = async (id, pageName = '') => {
  try {
    const pageNameParam = pageName ? `?pageName=${pageName}` : '';
    const response = await axiosServices.get(`${API_BASE_URL}/ngos/${id}${pageNameParam}`);
    return response?.data;
  } catch (error) {
    console.error('Failed to fetch NGOs:', error);
  }
};

export const addNgoUser = async (payload) => {
  payload.fullname = payload.name;
  payload.status = 'Inactive';
  const response = await axiosServices.post(`${API_BASE_URL}/portal-users`, payload);
  return response;
};

export const patchCampaign = async (campaignId, campaignData) => {
  const response = await axiosServices.put(`${API_BASE_URL}/campaigns/${campaignId}`, campaignData);
  return response?.data;
};

export const getGrades = async () => {
  try {
    const response = await axiosServices.get(`${API_BASE_URL}/grades`);
    return response.data;
  } catch (error) {
    throw new Error('Failed to fetch grades');
  }
};

export const getNgos = async (params) => {
  try {
    const response = await axiosServices.get(`${API_BASE_URL}/ngos`, { params });
    return response.data;
  } catch (error) {
    throw new Error('Failed to fetch NGOs');
  }
};
export const allNgoList = async () => {
  try {
    const response = await axiosServices.get(`${API_BASE_URL}/ngos/allNgos`);
    return response.data;
  } catch (error) {
    throw new Error('Failed to fetch NGOs');
  }
};
export const getNgoBySearchTerm = async (query, ngo_id, portalUserId = null) => {
  try {
    const params = new URLSearchParams();
    params.append('search', query);
    if (ngo_id) {
      params.append('ngo_id', ngo_id);
    }
    if (portalUserId) {
      params.append('portalUserId', portalUserId);
    }

    const response = await axiosServices.get(`${API_BASE_URL}/ngos/search?${params.toString()}`);
    return response.data;
  } catch (error) {
    throw new Error('Failed to fetch NGOs');
  }
};

export const getAdmins = async () => {
  try {
    const response = await axiosServices.get(`${API_BASE_URL}/portal-users/role/Admin`);
    return response.data;
  } catch (error) {
    throw new Error('Failed to fetch admins');
  }
};

export const assignAdminToNgos = async (adminId, ngoIdArray) => {
  try {
    const response = await axiosServices.patch(`${API_BASE_URL}/ngos/assignAdmin/${adminId}`, {
      ngoIdArray: ngoIdArray
    });
    return response.data;
  } catch (error) {
    throw new Error('Failed to assign to staff');
  }
};
export const deleteMultipleNgos = async (ngoIdArray) => {
  try {
    const response = await axiosServices.post(`${API_BASE_URL}/ngos/deleteMultipleNgos`, { ngoIdArray });
    return response.data;
  } catch (error) {
    throw new Error('Failed to delete selected NGOs');
  }
};

export const assignfeaturedToNgos = async (ngoId, featureMode) => {
  try {
    const response = await axiosServices.patch(`${API_BASE_URL}/ngos/mark/featured?ngoId=${ngoId}&featureMode=${featureMode}`);
    return response;
  } catch (error) {
    throw new Error('Failed to mark as featured');
  }
};

export const assignGradeToNgo = async (ngoId, grade, rating) => {
  try {
    const response = await axiosServices.patch(`${API_BASE_URL}/ngos/docs/${ngoId}`, {
      grade: grade,
      rating: rating
    });
    return response;
  } catch (error) {
    throw new Error('Failed to assign grade');
  }
};

export const deleteNgo = async (id) => {
  try {
    const response = await axiosServices.delete(`${API_BASE_URL}/ngos/${id}`);
    return response;
  } catch (error) {
    throw new Error('Failed to delete NGO');
  }
};

export const inactiveNgo = async (id) => {
  try {
    const response = await axiosServices.patch(`${API_BASE_URL}/ngos/inactiveNgo/${id}`);
    return response.data;
  } catch (error) {
    console.error(error);
    throw new Error('Failed to to make NGO inactive');
  }
};
export const getFeaturedNgos = async () => {
  try {
    const response = await axiosServices.get(`${API_BASE_URL}/featured`);
    return response?.data;
  } catch (error) {
    throw new Error('Failed to fetch featured NGO');
  }
};
