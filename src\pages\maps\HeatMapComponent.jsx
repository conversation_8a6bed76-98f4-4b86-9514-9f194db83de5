import React, { useEffect, useState, useMemo } from 'react';
import PropTypes from 'prop-types';
import { useTheme } from '@mui/material/styles';
import MainCard from 'components/MainCard';
import MapContainerStyled from 'components/third-party/map/MapContainerStyled';
import Map, { Source, Layer } from 'react-map-gl';
import axios from 'axios';
import heatmapLayer from 'sections/maps/heatmap/map-style';
import { getData } from 'api/map.service';

const mapConfiguration = {
  mapboxAccessToken: import.meta.env.VITE_APP_MAPBOX_ACCESS_TOKEN,
  minZoom: 1
};

export default function HeatmapComponent({ headerName, url }) {
  const [geoJsonData, setGeoJsonData] = useState(null);

  useEffect(() => {
    const fetchData = async () => {
      try {
        const response = await getData(url);
        // const earthquakes = response.data;

        // Transform data to GeoJSON
        const geoJson = {
          type: 'FeatureCollection',
          crs: {
            type: 'name',
            properties: { name: 'urn:ogc:def:crs:OGC:1.3:CRS84' }
          },
          features: response
            .filter((quake) => quake.longitude !== null && quake.latitude !== null && !isNaN(quake.longitude) && !isNaN(quake.latitude))
            .map((quake) => ({
              type: 'Feature',
              geometry: {
                type: 'Point',
                coordinates: [parseFloat(quake.longitude), parseFloat(quake.latitude), quake.depth || 0]
              },
              properties: {
                id: quake.id || null,
                mag: quake.magnitude || 0,
                time: quake.time || 0,
                felt: quake.felt || null,
                tsunami: quake.tsunami || 0
              }
            }))
        };

        setGeoJsonData(geoJson);
      } catch (error) {
        console.error('Error fetching heatmap data:', error);
      }
    };

    fetchData();
  }, [url]);

  const mapData = useMemo(() => geoJsonData, [geoJsonData]);

  return (
    <MainCard title={headerName}>
      <MapContainerStyled>
        {mapData ? (
          <Map initialViewState={{ latitude: 40, longitude: -100, zoom: 5 }} {...mapConfiguration}>
            <Source type="geojson" data={mapData}>
              <Layer {...heatmapLayer} />
            </Source>
          </Map>
        ) : (
          <p>Loading heatmap...</p>
        )}
      </MapContainerStyled>
    </MainCard>
  );
}

HeatmapComponent.propTypes = {
  headerName: PropTypes.string.isRequired,
  url: PropTypes.string.isRequired
};
const transformToGeoJson = (earthquakes) => {
  return {
    type: 'FeatureCollection',
    crs: {
      type: 'name',
      properties: { name: 'urn:ogc:def:crs:OGC:1.3:CRS84' }
    },
    features: earthquakes
      .filter((quake) => quake.longitude !== null && quake.latitude !== null && !isNaN(quake.longitude) && !isNaN(quake.latitude))
      .map((quake) => ({
        type: 'Feature',
        geometry: {
          type: 'Point',
          // Include depth if available, default to 0 if missing
          coordinates: [parseFloat(quake.longitude), parseFloat(quake.latitude), quake.depth || 0]
        },
        properties: {
          id: quake.id || null,
          mag: quake.magnitude || 0, // Rename to match expected 'mag'
          time: quake.time || 0,
          felt: quake.felt || null, // Add additional properties with default values
          tsunami: quake.tsunami || 0
        }
      }))
  };
};
