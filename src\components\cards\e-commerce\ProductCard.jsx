import PropTypes from 'prop-types';
import { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';

// Material-UI
import { useTheme } from '@mui/material/styles';
import Button from '@mui/material/Button';
import CardContent from '@mui/material/CardContent';
import CardMedia from '@mui/material/CardMedia';
import Chip from '@mui/material/Chip';
import Divider from '@mui/material/Divider';
import Grid from '@mui/material/Grid';
import Rating from '@mui/material/Rating';
import Stack from '@mui/material/Stack';
import Typography from '@mui/material/Typography';
import Box from '@mui/material/Box';

// Ant Design Icons
import { HeartOutlined, HeartFilled, InfoCircleOutlined, TagOutlined } from '@ant-design/icons';

// Project imports
import MainCard from 'components/MainCard';
import IconButton from 'components/@extended/IconButton';
import SkeletonProductPlaceholder from 'components/cards/skeleton/ProductPlaceholder';
import { openSnackbar } from 'api/snackbar';
import { addToCart } from 'api/cart';
import { getImageUrl } from 'utils/getImageUrl';

export default function ProductCard({ item, imageUrl, ngoName, collectionName }) {
  const {
    id,
    name,
    description,
    rating,
    isStock,
    sizes,
    colors,
    discountedPrice,
    discount,
    status,
    price,
    unit,
    unit_of_measure,
    sold_units,
    available_unit
  } = item;
  const theme = useTheme();
  const [wishlisted, setWishlisted] = useState(false);
  const [isLoading, setLoading] = useState(true);

  const addCart = () => {
    addToCart({
      id,
      name,
      image: getImageUrl(imageUrl),
      price,
      discountedPrice,
      quantity: 1
    });
    openSnackbar({
      open: true,
      message: 'Added to Cart Successfully!',
      variant: 'alert',
      alert: { color: 'success' }
    });
  };

  const addToFavourite = () => {
    setWishlisted(!wishlisted);
    openSnackbar({
      open: true,
      message: wishlisted ? 'Removed from Favorites' : 'Added to Favorites',
      variant: 'alert',
      alert: { color: 'success' }
    });
  };

  useEffect(() => {
    setLoading(false);
  }, []);

  return (
    <>
      {isLoading ? (
        <SkeletonProductPlaceholder />
      ) : (
        <MainCard
          content={false}
          boxShadow
          sx={{ '&:hover': { transform: 'scale(1.02)', transition: 'all .4s ease-in-out' }, borderRadius: '16px' }}
        >
          <Box sx={{ width: 300, margin: 'auto' }}>
            <CardMedia
              component="img"
              height="200"
              image={imageUrl}
              alt={name}
              sx={{ borderRadius: 2, opacity: isStock === 'Yes' ? 1 : 0.5, objectFit: 'fill' }}
            />
          </Box>
          <Stack
            direction="row"
            justifyContent="space-between"
            alignItems="center"
            sx={{ padding: 1, position: 'absolute', top: 0, left: 0, width: '100%' }}
          >
            {isStock !== 'Yes' && <Chip label="Out of Stock" color="error" size="small" />}
            {discount && <Chip icon={<TagOutlined />} label={`-${discount}%`} color="success" size="small" />}

            <IconButton onClick={addToFavourite} color="secondary">
              {wishlisted ? <HeartFilled style={{ color: theme.palette.error.main }} /> : <HeartOutlined />}
            </IconButton>
          </Stack>
          <CardContent>
            <Stack spacing={1}>
              <Grid item xs={12}>
                <Stack direction="row" justifyContent="space-between" alignItems="center">
                  <Typography
                    component={Link}
                    to={`/apps/e-commerce/product-details/${id}`}
                    color="text.primary"
                    variant="h5"
                    sx={{ overflow: 'hidden', textOverflow: 'ellipsis', whiteSpace: 'nowrap', display: 'block', textDecoration: 'none' }}
                  >
                    {name}
                  </Typography>
                  <Chip label={collectionName} color="primary" size="small" />
                </Stack>
                <Typography variant="body2" color="text.secondary">
                  {description}
                </Typography>
              </Grid>

              <Stack direction="row" justifyContent="space-between" alignItems="center">
                <Box>
                  <Typography variant="h6" color="primary.main">
                    ${discountedPrice}
                  </Typography>
                  {price && price !== discountedPrice && (
                    <Typography variant="body2" sx={{ textDecoration: 'line-through' }}>
                      ${price}
                    </Typography>
                  )}
                </Box>
                {/* <Button
                  variant="contained"
                  onClick={addCart}
                  disabled={isStock !== 'Yes'}
                  sx={{ '&.Mui-disabled': { color: theme.palette.grey[500] } }}
                >
                  {isStock !== 'Yes' ? 'Out of Stock' : 'Add to Cart'}
                </Button> */}
              </Stack>
              <Divider sx={{ my: 1 }} />
            </Stack>
          </CardContent>
        </MainCard>
      )}
    </>
  );
}

ProductCard.propTypes = {
  id: PropTypes.number.isRequired,
  name: PropTypes.string.isRequired,
  description: PropTypes.string.isRequired,
  isStock: PropTypes.string.isRequired,

  discountedPrice: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  discount: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  ngo_id: PropTypes.number.isRequired,
  status: PropTypes.string.isRequired,
  collection_id: PropTypes.number.isRequired,
  price: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  unit_of_measure: PropTypes.string.isRequired
};
