{"name": "mantis-material-react", "version": "3.3.0", "private": true, "dependencies": {"@ant-design/colors": "^7.1.0", "@ant-design/icons": "^5.4.0", "@auth0/auth0-react": "^2.2.4", "@date-io/date-fns": "^3.0.0", "@date-io/dayjs": "^3.0.0", "@date-io/luxon": "^3.0.0", "@date-io/moment": "^3.0.0", "@dnd-kit/core": "^6.1.0", "@dnd-kit/modifiers": "^7.0.0", "@dnd-kit/sortable": "^8.0.0", "@emotion/cache": "^11.13.1", "@emotion/react": "^11.13.0", "@emotion/styled": "^11.13.0", "@fontsource/inter": "^5.0.20", "@fontsource/poppins": "^5.0.14", "@fontsource/public-sans": "^5.0.18", "@fontsource/roboto": "^5.0.14", "@fullcalendar/common": "^5.11.5", "@fullcalendar/core": "^6.1.15", "@fullcalendar/daygrid": "^6.1.15", "@fullcalendar/interaction": "^6.1.15", "@fullcalendar/list": "^6.1.15", "@fullcalendar/react": "^6.1.15", "@fullcalendar/timegrid": "^6.1.15", "@fullcalendar/timeline": "^6.1.15", "@hello-pangea/dnd": "^16.6.0", "@mui/base": "5.0.0-beta.40", "@mui/lab": "^5.0.0-alpha.173", "@mui/material": "^5.16.7", "@mui/system": "^5.16.6", "@mui/x-charts": "^7.8.0", "@mui/x-data-grid-pro": "^7.22.1", "@mui/x-date-pickers": "^7.22.1", "@mui/x-tree-view": "^7.12.0", "@react-google-maps/api": "^2.20.3", "@react-pdf/renderer": "^3.4.4", "@supabase/supabase-js": "^2.45.0", "@svgr/webpack": "^8.1.0", "@tanstack/match-sorter-utils": "^8.19.4", "@tanstack/react-query": "^5.51.21", "@tanstack/react-table": "^8.20.1", "@tanstack/react-virtual": "^3.8.4", "@vitejs/plugin-react": "^4.3.1", "amazon-cognito-identity-js": "^6.3.12", "apexcharts": "3.46.0", "autosuggest-highlight": "^3.3.4", "axios": "^1.7.3", "buffer": "^6.0.3", "chance": "^1.1.12", "crypto-browserify": "^3.12.0", "crypto-js": "^4.2.0", "currency.js": "^2.0.4", "date-fns": "^3.6.0", "dayjs": "^1.11.13", "draft-js": "^0.11.7", "emoji-picker-react": "^4.11.1", "env-cmd": "^10.1.0", "firebase": "^10.12.5", "formik": "^2.4.6", "framer-motion": "^11.3.21", "history": "^5.3.0", "immutability-helper": "^3.1.1", "jsonwebtoken": "^9.0.2", "jwt-decode": "4.0.0", "lodash": "^4.17.21", "mapbox-gl": "^3.7.0", "moment": "^2.30.1", "notistack": "^3.0.1", "process": "^0.11.10", "react": "^18.3.1", "react-apexcharts": "^1.4.1", "react-beautiful-dnd": "^13.1.1", "react-compare-slider": "^3.1.0", "react-copy-to-clipboard": "^5.1.0", "react-csv": "^2.2.2", "react-device-detect": "^2.2.3", "react-dom": "^18.3.1", "react-draft-wysiwyg": "^1.15.0", "react-draggable": "^4.4.6", "react-dropzone": "^14.2.3", "react-fast-marquee": "^1.6.5", "react-google-recaptcha": "^3.1.0", "react-infinite-scroll-component": "^6.1.0", "react-intersection-observer": "^9.13.0", "react-intl": "^6.6.8", "react-map-gl": "^7.1.7", "react-number-format": "^5.4.0", "react-organizational-chart": "^2.2.1", "react-otp-input": "^3.1.1", "react-quill": "^2.0.0", "react-router": "^6.26.0", "react-router-dom": "^6.26.0", "react-slick": "^0.30.2", "react-spring": "^9.7.4", "react-syntax-highlighter": "^15.5.0", "react-table-sticky": "^1.1.3", "react-timer-hook": "^3.0.7", "react-to-print": "^2.15.1", "react-toastify": "^10.0.6", "react-window": "^1.8.10", "react-zoom-pan-pinch": "^3.6.1", "simplebar-react": "^3.2.6", "slick-carousel": "^1.8.1", "stream-browserify": "^3.0.0", "stylis-plugin-rtl": "^2.1.1", "swr": "^2.2.5", "use-places-autocomplete": "^4.0.1", "util": "^0.12.5", "uuid": "^10.0.0", "vite": "^5.3.5", "vite-jsconfig-paths": "^2.0.1", "web-vitals": "^4.2.2", "yup": "^1.4.0"}, "scripts": {"start": "vite", "build": "vite build", "build-stage": "env-cmd -f .env.qa vite build", "build-fat": "env-cmd -f .env.fat vite build", "build-prod": "env-cmd -f .env.prod vite build", "preview": "vite preview", "lint": "eslint \"src/**/*.{js,jsx,ts,tsx}\"", "lint:fix": "eslint --fix \"src/**/*.{js,jsx,ts,tsx}\"", "prettier": "prettier --write \"src/**/*.{js,jsx,ts,tsx}\""}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "babel": {"presets": ["@babel/preset-react"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@babel/core": "^7.24.0", "@babel/eslint-parser": "^7.24.7", "@babel/plugin-proposal-private-property-in-object": "^7.21.11", "eslint": "^8.57.0", "eslint-config-prettier": "^9.1.0", "eslint-config-react-app": "^7.0.1", "eslint-plugin-flowtype": "^8.0.3", "eslint-plugin-import": "^2.29.1", "eslint-plugin-jsx-a11y": "^6.9.0", "eslint-plugin-prettier": "^5.2.1", "eslint-plugin-react": "^7.35.0", "eslint-plugin-react-hooks": "^4.6.2", "immutable": "^4.3.7", "prettier": "^3.3.3", "react-error-overlay": "6.0.11"}, "packageManager": "yarn@4.4.1"}