import React, { useState, useEffect, Fragment } from 'react';
import { useParams } from 'react-router';
import PropTypes from 'prop-types';
import { useFormik } from 'formik';
import * as yup from 'yup';
import {
  Button,
  TextField,
  Box,
  Stack,
  Typography,
  Grid,
  CircularProgress,
  Chip,
  InputAdornment,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions
} from '@mui/material';
import AnimateButton from 'components/@extended/AnimateButton';
import { FormattedMessage } from 'react-intl';
import { GoogleMap, useJsApiLoader, Marker } from '@react-google-maps/api';
import { getPlaceDetails, getStateFromCoordinates } from 'api/campaigns.service';
import { fetchLocationDetails, fetchSuggestions } from 'sections/apps/profiles/account/tabs.service';
import MainCard from 'components/MainCard';
import UploadSingleFile from 'components/third-party/dropzone/SingleFile';
import LocationMap from 'components/LocationMap';
import { getSessionStorageItem } from 'utils/permissionUtils';
const GOOGLE_MAPS_API_KEY = 'AIzaSyDAlmZjT27PfFPOFsUVixpv6jPPtwkRVcs';

// Validation schema for Location Details
const validationSchema = yup.object({
  //   pincode: yup
  //     .string()
  //     .required('Pincode is required')
  //     .matches(/^\d{5,6}$/, 'Pincode must be 5 or 6 digits'),
  //   state: yup.string().required('State is required'),
  //   latitude: yup
  //     .number()
  //     .required('Latitude is required')
  //     .min(-90, 'Latitude must be between -90 and 90')
  //     .max(90, 'Latitude must be between -90 and 90'),
  //   longitude: yup
  //     .number()
  //     .required('Longitude is required')
  //     .min(-180, 'Longitude must be between -180 and 180')
  //     .max(180, 'Longitude must be between -180 and 180')
});

export default function LocationInformation({
  handleNext,
  handleBack,
  locationDetails,
  setLocationDetails,
  campaignDetails,
  handleSubmit
}) {
  // Formik setup
  const [selectedLocation, setSelectedLocation] = useState(null);
  const [showEditDetails, setShowEditDetails] = useState(false);

  const [isLoadingLocate, setIsLoadingLocate] = useState(false);
  const [suggestions, setSuggestions] = useState([]);
  const [editableLocation, setEditableLocation] = useState(null);

  const [isLoadingSuggestion, setIsLoadingSuggestion] = useState(false);
  const { campaignId } = useParams();
  const [error, setError] = useState('');
  const socialMediaLinks = [
    {
      label: 'Facebook URL',
      formName: 'facebook_url'
    },
    {
      label: 'Instagram URL',
      formName: 'instagram_url'
    },
    {
      label: 'Youtube URL',
      formName: 'youtube_url'
    },
    {
      label: 'Twitter URL',
      formName: 'twitter_url'
    }
  ];

  //google map
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [markerPosition, setMarkerPosition] = useState({ lat: 22.199166, lng: 78.476681 });

  const userInfo = getSessionStorageItem('user');
  const isNgo = userInfo.ngo_id;

  const { isLoaded } = useJsApiLoader({
    id: 'google-map-script',
    googleMapsApiKey: GOOGLE_MAPS_API_KEY
  });

  useEffect(() => {
    if (locationDetails?.latitude && locationDetails?.longitude) {
      setMarkerPosition({ lat: locationDetails.latitude, lng: locationDetails.longitude });
    }
  }, [locationDetails]);

  const handleDialogClose = () => {
    setIsDialogOpen(false);
  };

  const handleSearchSubmit = async (searchQuery) => {
    if (!searchQuery) {
      window.alert('Please enter address');
      return;
    }
    setIsLoadingLocate(true);

    try {
      const geocoder = new window.google.maps.Geocoder();
      geocoder.geocode({ address: searchQuery }, (results, status) => {
        if (status === 'OK') {
          setSuggestions(results);
          setError('');
        } else {
          setError('Please provide a proper location details!');
        }
        setIsLoadingLocate(false);
      });
    } catch (error) {
      setError('Failed to retrieve suggestions. Please try again.');
      setIsLoadingLocate(false);
    }
  };
  const handleSuggestionClick = async (suggestion, setFieldValue) => {
    setIsLoadingSuggestion(true);

    try {
      // Using Google Maps Geocoder
      const { lat, lng } = suggestion.geometry.location;
      const geocoder = new window.google.maps.Geocoder();

      geocoder.geocode({ location: { lat: lat(), lng: lng() } }, (results, status) => {
        if (status === 'OK') {
          const addressComponents = results[0].address_components;

          const country = addressComponents.find((component) => component.types.includes('country'))?.long_name || '';
          const state = addressComponents.find((component) => component.types.includes('administrative_area_level_1'))?.long_name || '';
          const city = addressComponents.find((component) => component.types.includes('locality'))?.long_name || '';
          const pincode = addressComponents.find((component) => component.types.includes('postal_code'))?.long_name || '';
          const address = results[0].formatted_address || '';

          // Update form fields
          setFieldValue('country', country);
          setFieldValue('state', state);
          setFieldValue('city', city);
          setFieldValue('pincode', pincode);
          setFieldValue('latitude', lat());
          setFieldValue('longitude', lng());

          // Update location details
          setLocationDetails((prev) => ({
            ...prev,
            country,
            state,
            city,
            pincode,
            latitude: lat(),
            longitude: lng(),
            address
          }));

          // Update marker position
          setMarkerPosition({ lat: lat(), lng: lng() });
          setEditableLocation({
            country,
            state,
            city,
            pincode,
            latitude: lat(),
            longitude: lng(),
            address
          });
          setIsDialogOpen(true);

          setError('');
        } else {
          setError('Failed to retrieve location details. Please try again.');
        }
        setIsLoadingSuggestion(false);
      });
    } catch (error) {
      setError('Failed to retrieve location details. Please try again.');
      setIsLoadingSuggestion(false);
    }
  };

  const handleConfirm = () => {
    setIsDialogOpen(false);
  };

  const handleMarkerDragEnd = (event) => {
    setMarkerPosition(event.latLng);
    const geocoder = new window.google.maps.Geocoder();
    geocoder.geocode({ location: event.latLng }, (results, status) => {
      if (status === 'OK') {
        const addressComponents = results[0].address_components;
        const country = addressComponents.find((component) => component.types.includes('country'))?.long_name || '';
        const state = addressComponents.find((component) => component.types.includes('administrative_area_level_1'))?.long_name || '';
        const city = addressComponents.find((component) => component.types.includes('locality'))?.long_name || '';
        const pincode = addressComponents.find((component) => component.types.includes('postal_code'))?.long_name || '';
        const address = results[0].formatted_address || '';

        setLocationDetails((prev) => ({
          ...prev,
          country,
          state,
          city,
          pincode,
          latitude: event.latLng.lat(),
          longitude: event.latLng.lng(),
          address: address
        }));

        setEditableLocation((prev) => ({
          ...prev,
          country,
          state,
          city,
          pincode,
          latitude: event.latLng.lat(),
          longitude: event.latLng.lng(),
          address: address
        }));
        formik.setFieldValue('country', country);
        formik.setFieldValue('state', state);
        formik.setFieldValue('city', city);
        formik.setFieldValue('pincode', pincode);
        formik.setFieldValue('latitude', event.latLng.lat());
        formik.setFieldValue('longitude', event.latLng.lng());
        formik.setFieldValue('address', address);
      }
    });
  };

  useEffect(() => {
    if (campaignId) {
      setShowEditDetails(true);
    } else {
      setShowEditDetails(false);
    }
  }, [campaignId]);
  const formik = useFormik({
    initialValues: {
      city: locationDetails.city || '',
      pincode: locationDetails.pincode || '',
      state: locationDetails.state || '',
      latitude: locationDetails.latitude || '',
      longitude: locationDetails.longitude || '',
      facebook_url: locationDetails.facebook_url || '',
      instagram_url: locationDetails.instagram_url || '',
      youtube_url: locationDetails.youtube_url || '',
      twitter_url: locationDetails.twitter_url || '',
      promotional_hashtag: locationDetails.promotional_hashtag || '',
      address: locationDetails?.address || '',
      country: locationDetails?.country || '',
      files: [],
      meeting_link: locationDetails?.meeting_link || ''
    },
    validationSchema,
    onSubmit: (values) => {
      setLocationDetails({
        city: values.city || '',
        pincode: values.pincode || '',
        state: values.state || '',
        latitude: values.latitude || '',
        longitude: values.longitude || '',
        facebook_url: values.facebook_url || '',
        instagram_url: values.instagram_url || '',
        youtube_url: values.youtube_url || '',
        twitter_url: values.twitter_url || '',
        promotional_hashtag: values.promotional_hashtag || '',
        address: values?.address || '',
        country: values?.country || '',
        files: values?.files || [],
        meeting_link: values?.meeting_link || ''
      });
      handleSubmit(values);
    }
  });

  return (
    <Box sx={{ pl: 1 }}>
      {' '}
      <form onSubmit={formik.handleSubmit} autoComplete="off">
        <Grid container spacing={3}>
          <Grid container item xs={6} md={6} spacing={2}>
            <Grid item xs={12} sm={12}>
              <Typography variant="h5" gutterBottom sx={{ mb: 2 }}>
                Media Information
              </Typography>
              <Grid item xs={12} sm={12} style={{ marginBottom: '1rem' }}>
                <Stack spacing={1}>
                  <TextField
                    id="name"
                    label="Promotional Hashtag"
                    name="promotional_hashtag"
                    value={formik.values.promotional_hashtag}
                    onChange={(e) => {
                      formik.handleChange(e);
                      setLocationDetails((prev) => ({
                        ...prev,
                        [e.target.name]: e.target.value
                      }));
                    }}
                    error={formik.touched.promotional_hashtag && Boolean(formik.errors.promotional_hashtag)}
                    helperText={formik.touched.promotional_hashtag && formik.errors.promotional_hashtag}
                    fullWidth
                    autoComplete="off"
                  />
                </Stack>
              </Grid>
              {socialMediaLinks.map((sml, index) => (
                <Grid key={index} item xs={12} sm={12} style={{ marginBottom: '1rem' }}>
                  <Stack spacing={1}>
                    <TextField
                      id={sml.formName}
                      label={sml.label}
                      name={sml.formName}
                      value={formik.values[sml.formName]}
                      onChange={(e) => {
                        formik.handleChange(e);
                        setLocationDetails((prev) => ({
                          ...prev,
                          [e.target.name]: e.target.value
                        }));
                      }}
                      fullWidth
                      autoComplete="off"
                    />
                  </Stack>
                </Grid>
              ))}
            </Grid>
          </Grid>
          <Grid container item xs={6} md={6} spacing={2}>
            <Grid item xs={12}>
              {campaignDetails?.format === 'Virtual' && (
                <Fragment>
                  <Typography variant="h5" gutterBottom sx={{ mb: 2 }}>
                    Virtual Meeting Information
                  </Typography>
                  <Grid item xs={12} sm={12} style={{ marginBottom: '1rem' }}>
                    <Stack spacing={1}>
                      <TextField
                        multiline
                        rows={4}
                        id="meeting_link"
                        label="Meeting Information"
                        name="meeting_link"
                        value={formik.values.meeting_link}
                        onChange={(e) => {
                          formik.handleChange(e);
                          setLocationDetails((prev) => ({
                            ...prev,
                            [e.target.name]: e.target.value
                          }));
                        }}
                        // error={formik.touched.name && Boolean(formik.errors.name)}
                        // helperText={formik.touched.name && formik.errors.name}
                        fullWidth
                        autoComplete="off"
                      />
                    </Stack>
                  </Grid>
                </Fragment>
              )}
              {campaignDetails?.format === 'Physical' && (
                <Fragment>
                  <Typography variant="h5" gutterBottom sx={{ mb: 2 }}>
                    Address Information
                  </Typography>
                  <Grid item xs={12}>
                    <TextField
                      fullWidth
                      multiline
                      rows={2}
                      label="Address"
                      required
                      name="address"
                      value={formik.values?.address}
                      onChange={formik.handleChange}
                      InputLabelProps={{ shrink: true }}
                      InputProps={{
                        endAdornment: (
                          <InputAdornment position="end">
                            <Button
                              variant="contained"
                              color="primary"
                              onClick={() => handleSearchSubmit(formik.values?.address)}
                              disabled={isLoadingLocate}
                            >
                              {isLoadingLocate ? <CircularProgress size={20} color="inherit" /> : 'Pin This Location'}
                            </Button>
                          </InputAdornment>
                        )
                      }}
                    />
                  </Grid>

                  <MainCard>
                    {error && <Typography color="error">{error}</Typography>}

                    {/* Display suggestions if available */}
                    {suggestions.length > 0 && (
                      <Box style={{ display: 'flex', flexWrap: 'wrap', gap: '8px', marginBottom: '10px', marginTop: '10px' }}>
                        {suggestions.map((suggestion, index) => (
                          <Chip
                            key={index}
                            label={suggestion.formatted_address}
                            onClick={() => !isLoadingSuggestion && handleSuggestionClick(suggestion, formik.setFieldValue)}
                            clickable
                            color="primary"
                            disabled={isLoadingSuggestion}
                          />
                        ))}
                        {isLoadingSuggestion && (
                          <Box style={{ display: 'flex', justifyContent: 'center', marginTop: '10px' }}>
                            <CircularProgress size={24} />
                          </Box>
                        )}
                      </Box>
                    )}

                    {locationDetails?.country && (
                      <Grid container spacing={2} marginTop={2}>
                        <Grid item xs={12} sm={6}>
                          <TextField
                            fullWidth
                            label="Country"
                            required
                            name="country"
                            value={formik.values.country || ''}
                            onChange={formik.handleChange}
                          />
                        </Grid>
                        <Grid item xs={12} sm={6}>
                          <TextField
                            fullWidth
                            label="State"
                            name="state"
                            required
                            value={formik.values.state || ''}
                            onChange={formik.handleChange}
                          />
                        </Grid>
                        <Grid item xs={12} sm={6}>
                          <TextField
                            fullWidth
                            label="City"
                            name="city"
                            required
                            value={formik.values.city || ''}
                            onChange={formik.handleChange}
                          />
                        </Grid>
                        <Grid item xs={12} sm={6}>
                          <TextField
                            fullWidth
                            label="Pincode"
                            name="pincode"
                            required
                            value={formik.values.pincode || ''}
                            onChange={formik.handleChange}
                          />
                        </Grid>
                      </Grid>
                    )}
                  </MainCard>
                </Fragment>
              )}
            </Grid>
          </Grid>
          <Grid item container spacing={3}>
            <Grid item xs={12} sm={6}>
              <Typography variant="h5" gutterBottom sx={{ mb: 2 }}>
                Banner Image
              </Typography>
              <Stack spacing={1.5} alignItems="center">
                <UploadSingleFile
                  setFieldValue={formik.setFieldValue}
                  file={formik.values.files}
                  error={formik.touched.files && !!formik.errors.files}
                />
              </Stack>
              {formik.touched.files && formik.errors.files && (
                <FormHelperText error id="standard-weight-helper-text-password-login">
                  {formik.errors.files}
                </FormHelperText>
              )}
            </Grid>
          </Grid>
          <Grid item xs={12}>
            <Stack direction="row" justifyContent={'space-between'}>
              <Button variant="contained" onClick={handleBack} sx={{ my: 3, ml: 1 }}>
                <FormattedMessage id="back" />
              </Button>
              <AnimateButton>
                <Button variant="contained" type="submit" onClick={() => handleSubmit('Draft')} sx={{ my: 3, ml: 1 }}>
                  <FormattedMessage
                    id="saveDraft"
                    onClick={() => {
                      formik.setFieldValue('status', 'Draft');
                      formik.submitForm();
                    }}
                  />
                </Button>

                <Button variant="contained" type="submit" sx={{ my: 3, ml: 1, textTransform: 'none' }}>
                  <FormattedMessage id="submitForReview" />
                </Button>
              </AnimateButton>
            </Stack>
          </Grid>
        </Grid>
      </form>
      <Dialog open={isDialogOpen} onClose={handleDialogClose}>
        <DialogTitle>
          <Typography
            variant="h5"
            sx={{
              fontWeight: 'bold',
              textAlign: 'center',
              color: 'primary.main',
              pb: 2,
              borderBottom: '1px solid',
              borderColor: 'divider'
            }}
          >
            Confirm Location
          </Typography>
        </DialogTitle>
        <DialogContent
          sx={{
            py: 4,
            px: 3,
            backgroundColor: 'background.default'
          }}
        >
          {isLoaded ? (
            <Box
              sx={{
                height: 300,
                borderRadius: 2,
                overflow: 'hidden',
                mb: 3,
                boxShadow: 3
              }}
            >
              <LocationMap center={markerPosition} onMarkerDragEnd={handleMarkerDragEnd} />
            </Box>
          ) : (
            <Box
              sx={{
                display: 'flex',
                justifyContent: 'center',
                alignItems: 'center',
                height: 300,
                mb: 3
              }}
            >
              <CircularProgress />
            </Box>
          )}

          <Box
            sx={{
              px: 2,
              py: 3,
              borderRadius: 2,
              backgroundColor: 'background.paper',
              boxShadow: 1
            }}
          >
            <Typography
              variant="subtitle1"
              sx={{
                fontWeight: 600,
                mb: 2,
                borderBottom: '1px solid',
                borderColor: 'divider',
                pb: 1
              }}
            >
              Address Details
            </Typography>

            <Grid container spacing={2}>
              <Grid item xs={12}>
                <Typography variant="body1" sx={{ mb: 1 }}>
                  <strong>Full Address:</strong> {locationDetails.address || 'N/A'}
                </Typography>
              </Grid>

              <Grid item xs={6}>
                <Typography variant="body1">
                  <strong>Country:</strong> {locationDetails.country || 'N/A'}
                </Typography>
              </Grid>

              <Grid item xs={6}>
                <Typography variant="body1">
                  <strong>State:</strong> {locationDetails.state || 'N/A'}
                </Typography>
              </Grid>

              <Grid item xs={6}>
                <Typography variant="body1">
                  <strong>City:</strong> {locationDetails.city || 'N/A'}
                </Typography>
              </Grid>

              <Grid item xs={6}>
                <Typography variant="body1">
                  <strong>Pincode:</strong> {locationDetails.pincode || 'N/A'}
                </Typography>
              </Grid>
            </Grid>
          </Box>
        </DialogContent>
        <DialogActions
          sx={{
            justifyContent: 'center',
            py: 2,
            backgroundColor: 'background.default'
          }}
        >
          <Button
            onClick={handleDialogClose}
            color="secondary"
            sx={{
              textTransform: 'none',
              borderRadius: 3,
              fontSize: '1rem',
              px: 3
            }}
          >
            Cancel
          </Button>
          <Button
            onClick={handleConfirm}
            variant="contained"
            color="primary"
            sx={{
              textTransform: 'none',
              borderRadius: 3,
              fontSize: '1rem',
              px: 3,
              ml: 2
            }}
          >
            Confirm and Proceed
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
}
