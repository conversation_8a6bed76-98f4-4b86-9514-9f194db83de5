import React, { useState, useEffect, Fragment } from 'react';
import { Tabs, Tab, Box, Typography, Divider, <PERSON>ack, <PERSON><PERSON>, Stepper, <PERSON>, StepLabel } from '@mui/material';
import { toast, ToastContainer } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import dayjs from 'dayjs';
import { useNavigate, useParams } from 'react-router';

import MainCard from 'components/MainCard';
import AnimateButton from 'components/@extended/AnimateButton';
import { getCampaignById, getPlaceSuggestions, addCampaign, updateCampaign } from 'api/campaigns.service';
import { getAllCategories } from 'api/categories.service';
import { getAllNGOs, getAllUserSkills } from '../ngos/ngo.service';
import { calculateProfileCompletion, generateSlug, getConvertedFileName } from 'utils/permissionUtils';
import ProfileCard from 'sections/apps/profiles/user/ProfileCard';
import LocationInformation from './components/LocationInformation';
import TargetImpact from './components/TargetImpact';
import useAuth from 'hooks/useAuth';
import CampaignDetails from './components/CampaignDetails';
import Preview from './components/Preview';
import { addItems, getAllItems } from '../apis/items.service';
import { getNgoById } from 'api/ngos.service';
import RSVPDetailsComponent from '../components/RSVPDetailsComponent';
import RSVPSummaryComponent from '../components/RSVPCommunicationDetails';
import { fetchAllRSVPInfo } from '../apis/rvsp.service';
import { addSkillService } from '../apis/skills.service';
import { ALLOW_SUBMIT_CAMPAIGN_STATUS } from 'utils/statusconstans';

const steps = ['Campaign Details', 'Target & Impact', 'Address, Media & Promotion'];

export default function CampaignForm({ isEdit = false }) {
  const [activeStep, setActiveStep] = useState(0);
  const [errorIndex, setErrorIndex] = useState(null);
  const [categories, setCategories] = useState([]);
  const [ngos, setNgos] = useState([]);
  const [ngoInfo, setNgoInfo] = useState([]);
  const [ngoList, setNgoList] = useState([]);
  const [rsvpInfo, setRSVpInfo] = useState([]);
  const [activeTab, setActiveTab] = useState(0); // Track active tab
  const [nestedTab, setNestedTab] = useState(0);

  const { campaignId } = useParams();
  const { user } = useAuth();
  const navigate = useNavigate();

  const [campaignDetails, setCampaignDetails] = useState({});
  const [locationDetails, setLocationDetails] = useState({});
  const [targetImpactInformation, setTargetImpactInformation] = useState({});
  const [profileCompletePercentage, setProfileCompletePercentage] = useState(null);
  const [userSkills, setUserSkills] = useState([]);
  const [itemOptions, setItemOptions] = useState([]);
  const [suggestions, setSuggestions] = useState([]);

  useEffect(() => {
    fetchCategories();
    fetchNGOS();
    fetchUserSkills();
    fetchItems();
    fetchSingleNgoById();
    if (campaignId) fetchRsvpInformations();

    if (isEdit && campaignId) {
      fetchCampaign(campaignId);
    }
  }, [isEdit, campaignId]);

  useEffect(() => {
    const fetchNgoDataAndCalculate = async () => {
      if (user?.ngo_id) {
        try {
          const ngodata = await getNgoById(user.ngo_id, 'CampaignForm');
          const profilepercentage = await calculateProfileCompletion(ngodata);
          setProfileCompletePercentage(profilepercentage);
        } catch (error) {
          console.error('Error fetching NGO data:', error);
        }
      } else {
        setProfileCompletePercentage(0);
      }
    };

    fetchNgoDataAndCalculate();
  }, [user?.ngo_id]);

  const handleSubmit = async (values) => {
    let locationNewDetails = locationDetails;
    if (values !== 'Draft') {
      locationNewDetails = values;
    }
    try {
      let formData = new FormData();

      if (locationNewDetails?.files && locationNewDetails?.files?.length > 0) {
        const convertedFileName = getConvertedFileName(locationNewDetails?.files[0]?.name);
        formData.append('fileName', `${convertedFileName}`);
        formData.append('file', locationNewDetails?.files[0], `${convertedFileName}`);
      }
      const campaignData1 = {
        // ...locationDetails,
        ...targetImpactInformation,
        name: campaignDetails?.name,
        description: campaignDetails?.description,
        campaign_start_date: campaignDetails?.campaign_start_date,
        campaign_end_date: campaignDetails?.campaign_end_date,
        sameday_event: campaignDetails?.sameday_event,
        category_id: campaignDetails?.category_id,
        format: campaignDetails?.format,
        ngo_id: campaignDetails?.ngo_id,
        ratio:campaignDetails?.ratio,

        city: locationNewDetails?.city || '',
        pincode: locationNewDetails?.pincode || '',
        state: locationNewDetails?.state || '',
        latitude: locationNewDetails?.latitude || '',
        longitude: locationNewDetails?.longitude || '',
        facebook_url: locationNewDetails?.facebook_url || '',
        instagram_url: locationNewDetails?.instagram_url || '',
        youtube_url: locationNewDetails?.youtube_url || '',
        twitter_url: locationNewDetails?.twitter_url || '',
        promotional_hashtag: locationNewDetails?.promotional_hashtag || '',
        address: locationNewDetails?.address || '',
        country: locationNewDetails?.country || '',
        files: locationNewDetails?.files || [],
        meeting_link: locationNewDetails?.meeting_link || '',

        createdBy: user?.id,
        event_time: campaignDetails?.event_time ? campaignDetails?.event_time : null,
        skills: targetImpactInformation?.skills?.toString(),
        status: values === 'Draft' ? 'Draft' : 'In Review',
        inKindList: JSON.stringify(targetImpactInformation?.inKindList || [])
      };

      const existingSlugs = itemOptions.map((item) => item.slug);

      const newCustomItems = targetImpactInformation?.inKindList?.filter((userItem) => {
        const userItemSlug = generateSlug(userItem?.item);
        return !existingSlugs.includes(userItemSlug);
      });

      const newItemsToBeAdded = newCustomItems?.map((customItem) => ({
        name: customItem.item,
        description: customItem.item,
        status: 'Active'
      }));

      for (const item of newItemsToBeAdded) {
        await addItems(item);
      }
      delete campaignData1.fileName;

      const existingSkills = targetImpactInformation?.skills ? targetImpactInformation.skills.split(',') : [];
      const newSkills = existingSkills.filter((skill) => !userSkills.includes(skill));

      for (const newSkill of newSkills) {
        await addSkillService({ name: newSkill, status: 'Active' });
      }

      Object.keys(campaignData1).forEach((key) => {
        if (campaignData1[key] !== null) {
          formData.append(key, campaignData1[key]);
        }
      });
      if (isEdit) {
        formData.delete('updatedBy');
        formData.append('updatedBy', user?.id);
      }
      if (isEdit) {
        await updateCampaign(campaignId, formData, 'CampaignForm');
        toast.success('Campaign updated successfully!');
      } else {
        await addCampaign(formData, 'CampaignForm');
        toast.success('Campaign added successfully!');
      }
      // setActiveStep(0);
      setTimeout(() => {
        navigate('/masters/campaigns');
      }, 1000);
    } catch (error) {
      console.error('Failed to save campaign:', error);
      toast.error('Failed to save campaign');
    }
  };

  const fetchCampaign = async (id) => {
    try {
      const response = await getCampaignById(id);
      setCampaignDetails(response);
      setLocationDetails(response);
      setTargetImpactInformation(response);
    } catch (error) {
      console.error('Failed to fetch campaign:', error);
      toast.error('Failed to fetch campaign');
    }
  };
  const fetchSingleNgoById = async () => {
    try {
      const response = await getNgoById(user?.ngo_id);
      setNgoInfo(response);
    } catch (error) {
      console.error('Failed to fetch ngo:', error);
      toast.error('Failed to fetch ngo');
    }
  };

  const fetchCategories = async () => {
    try {
      const response = await getAllCategories('CampaignForm');
      setCategories(response);
    } catch (error) {
      console.error('Failed to fetch categories:', error);
      toast.error('Failed to fetch categories');
    }
  };

  const fetchItems = async () => {
    try {
      const response = await getAllItems('CampaignForm');
      setItemOptions(response);
    } catch (error) {
      console.error('Failed to fetch items:', error);
      toast.error('Failed to fetch items');
    }
  };

  const fetchNGOS = async () => {
    try {
      const response = await getAllNGOs();
      setNgos(response?.ngos);
    } catch (error) {
      console.error('Failed to fetch ngos:', error);
      toast.error('Failed to fetch ngos');
    }
  };
  const fetchRsvpInformations = async () => {
    try {
      const response = await fetchAllRSVPInfo(campaignId);
      setRSVpInfo(response);
    } catch (error) {
      console.error('Failed to fetch rsvp info:', error);
      toast.error('Failed to fetch rsvp info');
    }
  };

  const fetchUserSkills = async () => {
    try {
      const response = await getAllUserSkills('CampaignForm');
      if (response.length > 0) {
        setUserSkills(response.map((re) => re.name) || []);
        return;
      }
      setUserSkills([]);
    } catch (error) {
      console.error('Failed to fetch user skills:', error);
      toast.error(error?.message || 'Failed to fetch user skills');
    }
  };

  const handleNext = () => {
    if (activeStep < steps.length - 1) {
      setActiveStep((prevActiveStep) => prevActiveStep + 1);
      setErrorIndex(null);
    }
  };

  const handleBack = () => {
    if (activeStep > 0) {
      setActiveStep((prevActiveStep) => prevActiveStep - 1);
    }
  };

  const handleTabChange = (event, newValue) => {
    setActiveTab(newValue);
  };
  const handleNestedTabChange = (event, newValue) => {
    setNestedTab(newValue);
  };

  const getStepContent = (step) => {
    switch (step) {
      case 0:
        return (
          <CampaignDetails
            campaignDetails={campaignDetails}
            setCampaignDetails={setCampaignDetails}
            handleNext={handleNext}
            setErrorIndex={setErrorIndex}
            categories={categories}
            ngos={ngos}
            ngoList={ngoList}
            handleSubmit={handleSubmit}
            setNgoList={setNgoList}
          />
        );
      case 1:
        return (
          <TargetImpact
            targetImpactInformation={targetImpactInformation}
            setTargetImpactInformation={setTargetImpactInformation}
            setErrorIndex={setErrorIndex}
            handleNext={handleNext}
            handleBack={handleBack}
            handleSubmit={handleSubmit}
            userSkills={userSkills}
            itemOptions={itemOptions}
          />
        );
      case 2:
        return (
          <LocationInformation
            handleNext={handleNext}
            handleBack={handleBack}
            locationDetails={locationDetails}
            campaignDetails={campaignDetails}
            suggestions={suggestions}
            handleSubmit={handleSubmit}
            setLocationDetails={setLocationDetails}
          />
        );
      default:
        throw new Error('Unknown step');
    }
  };
  return (
    <Fragment>
      <Box sx={{ marginBottom: 2 }}>
        {user?.ngo_id && ngoInfo && ngoInfo?.ngo_status !== "Verified" && profileCompletePercentage && profileCompletePercentage < 100 && <ProfileCard ngoInfo={ngoInfo} />}
      </Box>
      <MainCard>
        <Tabs value={activeTab} onChange={handleTabChange}>
          <Tab label="Preview" />
          <Tab label="Campaign Form" />
          {isEdit && <Tab label="RSVP Information" />}
        </Tabs>
        <Divider sx={{ borderWidth: 1, borderColor: '#e1e1e1', mb: 2 }} />
        {activeTab === 0 && (
          <Preview
            locationDetails={locationDetails}
            suggestions={suggestions}
            handleSearchSubmit={(searchTerm) => setSuggestions(searchTerm)}
            campaignDetails={campaignDetails}
            targetImpactInformation={targetImpactInformation}
            handleSubmit={handleSubmit}
            categories={categories}
          />
        )}
        {activeTab === 1 && (
          <>
            <Stepper activeStep={activeStep} sx={{ pt: 3, pb: 5 }}>
              {steps.map((label, index) => (
                <Step key={label}>
                  <StepLabel error={index === errorIndex}>{label}</StepLabel>
                </Step>
              ))}
            </Stepper>
            <>{getStepContent(activeStep)}</>
          </>
        )}
        {activeTab === 2 && (
          <>
            <Tabs value={nestedTab} onChange={handleNestedTabChange}>
              <Tab label="RSVP Details" />
              <Tab label="Communication Details" />
            </Tabs>
            <Divider sx={{ borderWidth: 1, borderColor: '#e1e1e1' }} />
            {nestedTab === 0 && <RSVPDetailsComponent campaignId={campaignId} />}
            {nestedTab === 1 && <RSVPSummaryComponent data={rsvpInfo || []} />}
          </>
        )}
      </MainCard>
      <ToastContainer />
    </Fragment>
  );
}
