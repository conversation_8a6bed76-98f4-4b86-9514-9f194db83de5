import PropTypes from 'prop-types';
import { Link } from 'react-router-dom';
import ButtonBase from '@mui/material/ButtonBase';

// project import
import Logo from './LogoMain';
import LogoIcon from './LogoIcon';
import { APP_DEFAULT_PATH } from 'config';
import useAuth from 'hooks/useAuth';
import Logo1 from 'assets/images/DRLogo1.png';
import DorightIcon from 'assets/images/do-right-logo.jpg';

export default function LogoSection({ reverse, isIcon, sx, to }) {
  const { isLoggedIn } = useAuth();
  return (
    <ButtonBase disableRipple {...(isLoggedIn && { component: Link, to: !to ? "/" : to, sx })}>
      {/* {isIcon ? <LogoIcon /> : <Logo reverse={reverse} />} */}
      {isIcon ? (
        <img src={DorightIcon} style={{ height: 70, marginTop: '1rem' }} />
      ) : (
        <img src={Logo1} style={{ height: 70, marginTop: '2rem', marginBottom:"1rem" }} />
      )}
    </ButtonBase>
  );
}

LogoSection.propTypes = { reverse: PropTypes.bool, isIcon: PropTypes.bool, sx: PropTypes.any, to: PropTypes.any };
