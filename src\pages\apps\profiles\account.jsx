import { useEffect, useState } from 'react';
import { useLocation, Link, Outlet, useNavigate } from 'react-router-dom';

// material-ui
import Box from '@mui/material/Box';
import Tab from '@mui/material/Tab';
import Tabs from '@mui/material/Tabs';
import { ToastContainer, toast } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';

// project import
import MainCard from 'components/MainCard';
import Breadcrumbs from 'components/@extended/Breadcrumbs';
import { APP_DEFAULT_PATH } from 'config';

// assets
import ContainerOutlined from '@ant-design/icons/ContainerOutlined';
import FileTextOutlined from '@ant-design/icons/FileTextOutlined';
import LockOutlined from '@ant-design/icons/LockOutlined';
import SettingOutlined from '@ant-design/icons/SettingOutlined';
import TeamOutlined from '@ant-design/icons/TeamOutlined';
import UserOutlined from '@ant-design/icons/UserOutlined';
import { BankOutlined, CommentOutlined, FileImageOutlined, MessageOutlined, QuestionOutlined } from '@ant-design/icons';
import { FileOutlined } from '@ant-design/icons';
import useAuth from 'hooks/useAuth';
import {
  Alert,
  Button,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
  Grid,
  Stack,
  TextField,
  Typography
} from '@mui/material';
import { getNgoById } from 'api/ngos.service';
import { updateUsingPatchNGO } from 'sections/apps/profiles/profile.service';
import { calculateProfileCompletion } from 'utils/permissionUtils';
import { APP_DEFAULT_PATH_NGO } from 'config';
import { addProfileQueries } from 'sections/apps/profiles/account/tabs.service';

// ==============================|| PROFILE - ACCOUNT ||============================== //

export default function AccountProfile() {
  const { pathname } = useLocation();
  const { user } = useAuth();
  const [ngoDetails, setNgoDetails] = useState(null);
  const [profileCompletePercentage, setProfileCompletePercentage] = useState(0);
  const [description, setDescription] = useState('');
  const [informationDialogOpen, setInformationDialogOpen] = useState(false);

  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [isProfileModificationOpen, setProfileModificationOpen] = useState(false);

  const navigate = useNavigate();
  useEffect(() => {
    fetchNGODetails(user?.ngo_id);
  }, [user]);

  const fetchNGODetails = async (ngo_id) => {
    try {
      const ngoRecords = await getNgoById(ngo_id);
      setNgoDetails(ngoRecords);
      fetchNgoDataAndCalculate(ngoRecords);
    } catch (error) {
      console.error(error);
    }
  };
  const fetchNgoDataAndCalculate = async (ngoDetails) => {
    try {
      const profilepercentage = await calculateProfileCompletion(ngoDetails);
      setProfileCompletePercentage(profilepercentage);
      // if (profilepercentage == 100) {
      //   setIsDialogOpen(true);
      // }
      const ngoRecords = await getNgoById(user?.ngo_id);
      if (profilepercentage == 100 && (ngoRecords?.ngo_status == 'New' || ngoRecords?.ngo_status == 'Pending')) {
        setIsDialogOpen(true);
      }
    } catch (error) {
      console.error('Error fetching NGO data:', error);
    }
  };

  const handleDialogClose = () => {
    setIsDialogOpen(false);
  };

  const handleProfileChangeSubmit = async () => {
    if(!description) {
        window.alert("Please enter description");
        return;
    }
    try {
      const payload = {
        ngo_id: user?.ngo_id,
        description: description,
        status: 'Pending',
        type: 'profile-queries'
      };

      const response = await addProfileQueries(payload);
      if (response) {
        setProfileModificationOpen(false);
        setInformationDialogOpen(true);
      }
    } catch (error) {
      console.error(error);
      toast.error('Something went wrong!', error);
    }
  };

  const handleSubmitReview = async (e) => {
    const ngoRecords = await getNgoById(user?.ngo_id);
    const profilepercentage = await calculateProfileCompletion(ngoRecords);
    if (profilepercentage < 100) {
      toast.error('Your profile is currently not 100% complete. Please update it to ensure all required information is accurately provided.');
      return;
    }
    e.preventDefault();
    if (window.confirm('Are you sure you want to submit profile for review')) {
      try {
        await updateUsingPatchNGO(user?.ngo_id, { ngo_status: 'In Review', last_status: 'In Review' }, 'NGOProfile');
        toast.success('Your profile has been submitted for review.');
        fetchNGODetails(user?.ngo_id);
        setTimeout(() => {
          window.location.reload();
        }, 1000);
        navigate("/apps/profiles/account/ngoprofile",{ replace: true })
        return;
      } catch (error) {
        console.error('Error updating ngo details:', error);
        toast.error('Failed to update NGO details');
      }
    }
  };

  let selectedTab = 0;
  let breadcrumbTitle = '';
  let breadcrumbHeading = '';
  switch (pathname) {
    case '/apps/profiles/account/basic':
      breadcrumbTitle = 'Basic Information';
      breadcrumbHeading = 'Basic Information';
      selectedTab = 1;
      break;
    case '/apps/profiles/account/personal':
      breadcrumbTitle = 'About NGO';
      breadcrumbHeading = 'About NGO';
      selectedTab = 2;
      break;
    case '/apps/profiles/account/documents':
      breadcrumbTitle = 'Documents';
      breadcrumbHeading = 'Documents';
      selectedTab = 3;
      break;

    case '/apps/profiles/account/socials':
      breadcrumbTitle = 'Socials';
      breadcrumbHeading = 'Socials';
      selectedTab = 4;
      break;
    // case '/apps/profiles/account/images':
    //   breadcrumbTitle = 'Images';
    //   breadcrumbHeading = 'Images';
    //   selectedTab = 5;
    //   break;
    case '/apps/profiles/account/bank-accounts':
      breadcrumbTitle = 'Bank-Accounts';
      breadcrumbHeading = 'Bank-Accounts';
      selectedTab = 5;
      break;

    case '/apps/profiles/account/ngoprofile':
    default:
      breadcrumbTitle = 'Profile';
      breadcrumbHeading = 'Profile';
      selectedTab = 0;
  }

  const [value, setValue] = useState(selectedTab);

  const handleChange = (event, newValue) => {
    setValue(newValue);
  };

  let breadcrumbLinks = [
    { title: 'Home', to: APP_DEFAULT_PATH_NGO },
    { title: 'NGO Profile', to: '/apps/profiles/account/ngoprofile' },
    { title: breadcrumbTitle }
  ];
  if (selectedTab === 0) {
    breadcrumbLinks = [
      { title: 'Home', to: APP_DEFAULT_PATH_NGO },
      { title: 'NGO Profile', to: '/apps/profiles/account/ngoprofile' },
      { title: breadcrumbTitle }
    ];
  }

  useEffect(() => {
    if (pathname === '/apps/profiles/account/ngoprofile') {
      setValue(0);
    } else {
      setValue(selectedTab);
    }
  }, [pathname]);

  return (
    <>
      <Breadcrumbs custom heading={breadcrumbHeading} links={breadcrumbLinks} />
      <MainCard border={false} boxShadow>
        {!user?.ngo_id && (
          <Box
            sx={{
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center',
              backgroundColor: '#f5f5f5', // Light gray background
              padding: '12px 16px',
              borderRadius: '8px',
              boxShadow: '0px 2px 6px rgba(0, 0, 0, 0.1)', // Subtle shadow
              border: '1px solid #ddd',
              mb: 2
            }}
          >
            <Typography variant="h6" sx={{ fontWeight: 'bold', color: 'primary' }}>
              NGO Name: <span style={{ color: '#1976d2' }}>{ngoDetails?.name || 'N/A'}</span>
            </Typography>
            <Typography variant="h6" sx={{ fontWeight: 'bold', color: '#333' }}>
              Source: <span style={{ color: '#d32f2f' }}>{ngoDetails?.source || 'N/A'}</span>
            </Typography>
          </Box>
        )}

        <Box
          sx={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            mb: 1
          }}
        >
          <Tabs
            className="ngo-tabs"
            value={value}
            onChange={handleChange}
            variant="scrollable"
            scrollButtons="auto"
            aria-label="account profile tab"
          >
            <Tab label="Profile" component={Link} to="/apps/profiles/account/ngoprofile" icon={<UserOutlined />} iconPosition="start" />

            {ngoDetails && !(ngoDetails?.ngo_status === 'Verified' || ngoDetails?.ngo_status === 'In Review') && (
              <Tab
                label="Basic Information"
                component={Link}
                to="/apps/profiles/account/basic"
                icon={<UserOutlined />}
                iconPosition="start"
                //   disabled={ngoDetails?.ngo_status === 'Verified'}
              />
            )}
            {ngoDetails && !(ngoDetails?.ngo_status === 'Verified' || ngoDetails?.ngo_status === 'In Review') && (
              <Tab
                label="About NGO"
                component={Link}
                to="/apps/profiles/account/personal"
                icon={<FileTextOutlined />}
                iconPosition="start"
                //   disabled={ngoDetails?.ngo_status === 'Verified'}
              />
            )}
            {ngoDetails &&!(ngoDetails?.ngo_status === 'Verified' || ngoDetails?.ngo_status === 'In Review') && (
              <Tab
                label="Documents"
                component={Link}
                to="/apps/profiles/account/documents"
                icon={<FileOutlined />}
                iconPosition="start"
                //   disabled={ngoDetails?.ngo_status === 'Verified'}
              />
            )}
            {ngoDetails &&!(ngoDetails?.ngo_status === 'Verified' || ngoDetails?.ngo_status === 'In Review') && (
              <Tab
                label="Socials"
                component={Link}
                to="/apps/profiles/account/socials"
                icon={<MessageOutlined />}
                iconPosition="start"
                //   disabled={ngoDetails?.ngo_status === 'Verified'}
              />
            )}
            {ngoDetails &&!(ngoDetails?.ngo_status === 'Verified' || ngoDetails?.ngo_status === 'In Review') && (
              <Tab
                label="Bank Details"
                component={Link}
                to="/apps/profiles/account/bank-accounts"
                icon={<BankOutlined />}
                iconPosition="start"
                //   disabled={ngoDetails?.ngo_status === 'Verified'}
              />
            )}
            {ngoDetails &&(ngoDetails?.ngo_status === 'Verified' || ngoDetails?.ngo_status === 'In Review') && (
              <Tab
                label="Your Queries"
                component={Link}
                to="/apps/profiles/account/profile-queries"
                icon={<QuestionOutlined />}
                iconPosition="start"
                //   disabled={ngoDetails?.ngo_status === 'Verified'}
              />
            )}

            {user?.roleInfo.name.startsWith('DR') && (
              <Tab
                label="Add Comments"
                component={Link}
                to="/apps/profiles/account/add-comments"
                icon={<CommentOutlined />}
                iconPosition="start"
                // disabled={ngoDetails?.ngo_status === 'Verified'}
              />
            )}
          </Tabs>

          <Box sx={{ ml: 'auto', display: 'flex', gap: 1 }}>
            {ngoDetails && ngoDetails.ngo_status !== 'Verified' && ngoDetails?.ngo_status !== 'In Review' && (
              <Button onClick={handleSubmitReview} variant="contained" color="primary" size="small">
                Submit for Onboarding
              </Button>
            )}
          </Box>
          {((ngoDetails && ngoDetails?.ngo_status === 'Verified') || ngoDetails?.ngo_status === 'In Review') && (
            <Box sx={{ display: 'flex', justifyContent: 'flex-end', alignItems: 'center', gap: 2 }}>
              <Box sx={{ maxWidth: 500 }}>
                <Alert severity="warning">
                  {`Note: Your profile is locked as its current status is ${ngoDetails?.ngo_status ? ngoDetails?.ngo_status : ''} . To request changes, click "Edit Profile", provide the details you
                  wish to update, and explain the reason for the changes.`}
                </Alert>
              </Box>

              <Button onClick={() => setProfileModificationOpen(true)} variant="contained" color="primary" size="small">
                Edit Profile
              </Button>
            </Box>
          )}
        </Box>
        <Box sx={{ mt: 2.5 }}>
          <Outlet />
        </Box>
      </MainCard>
      <Dialog
        open={isDialogOpen}
        onClose={handleDialogClose}
        maxWidth="50%"
        PaperProps={{
          sx: {
            top: 0,
            position: 'absolute',
            borderRadius: '16px',
            width: '100%',
            maxWidth: '600px'
          }
        }}
      >
        <DialogTitle>Your Profile is Ready for Review</DialogTitle>
        <DialogContent>
          <DialogContentText>
            Your profile is 100% complete. You can submit it for review now if all the information is correct. If you want to recheck the
            details, click Cancel and manually submit later.
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleDialogClose} color="secondary">
            Cancel
          </Button>
          <Button onClick={handleSubmitReview} color="primary" variant="contained">
            Submit for Onboarding
          </Button>
        </DialogActions>
      </Dialog>
      <Dialog
        open={isProfileModificationOpen}
        onClose={(event, reason) => {
          if (reason !== 'backdropClick' && reason !== 'escapeKeyDown') {
            setProfileModificationOpen(false);
          }
        }}
        maxWidth="50%"
        PaperProps={{
          sx: {
            top: 0,
            position: 'absolute',
            borderRadius: '16px',
            width: '100%',
            maxWidth: '600px'
          }
        }}
      >
        <DialogTitle>Request Profile Modifications</DialogTitle>
        <DialogContent>
          <DialogContentText>
            Please specify the fields you want to modify in the profile and provide a reason for the requested changes.
          </DialogContentText>
          <TextField
            fullWidth
            label="Description"
            variant="outlined"
            margin="normal"
            multiline
            rows={7}
            // value={description}
            onChange={(e) => setDescription(e.target.value)}
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setProfileModificationOpen(false)} color="secondary">
            Cancel
          </Button>
          <Button onClick={handleProfileChangeSubmit} color="primary" variant="contained">
            Submit
          </Button>
        </DialogActions>
      </Dialog>
      <Dialog
        open={informationDialogOpen}
        onClose={(event, reason) => {
          if (reason !== 'backdropClick' && reason !== 'escapeKeyDown') {
            setInformationDialogOpen(false);
          }
        }}
        maxWidth="50%"
        PaperProps={{
          sx: {
            top: 0,
            position: 'absolute',
            borderRadius: '16px',
            width: '100%',
            maxWidth: '600px'
          }
        }}
      >
        <DialogTitle>Modification Request Submitted</DialogTitle>
        <DialogContent>
          <DialogContentText>
            Your request has been successfully submitted. Our team will review your modifications and get back to you shortly. Please wait
            for further communication regarding the status of your request.
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setInformationDialogOpen(false)} color="primary" variant="contained">
            OK
          </Button>
        </DialogActions>
      </Dialog>

      <ToastContainer autoClose={6000} />
    </>
  );
}
