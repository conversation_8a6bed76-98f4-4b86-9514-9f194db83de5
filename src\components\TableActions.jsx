import { Stack, Tooltip } from '@mui/material';
import IconButton from 'components/@extended/IconButton';
// assets
import DeleteOutlined from '@ant-design/icons/DeleteOutlined';
import EditOutlined from '@ant-design/icons/EditOutlined';
import { DashboardOutlined, FlagOutlined } from '@ant-design/icons';

const TableActions = ({ handleEditClick, cell, handleDeleteClick, showDelete = true, campaginInfo, showEdit = true }) => {
  return (
    <Stack direction="row" alignItems="center" spacing={0}>
      {/* <Tooltip title="View">
            <IconButton color={row.getIsExpanded() ? 'error' : 'secondary'} onClick={row.getToggleExpandedHandler()}>
              {collapseIcon}
            </IconButton>
          </Tooltip> */}
      {showEdit && (
        <Tooltip title="Edit">
          <IconButton color="primary" onClick={() => handleEditClick(cell.row.original)}>
            <EditOutlined />
          </IconButton>
        </Tooltip>
      )}

      {showDelete && (
        <Tooltip title="Delete">
          <IconButton color="error" onClick={() => handleDeleteClick(cell.row.original.id)}>
            <DeleteOutlined />
          </IconButton>
        </Tooltip>
      )}
      {campaginInfo?.showCampaignMileStones && (
        <Tooltip title="Add MileStone">
          <IconButton color="success" onClick={() => campaginInfo?.openMileStonesDialog(cell.row.original)}>
            <FlagOutlined />
          </IconButton>
        </Tooltip>
      )}
      {campaginInfo?.showCampaignDashboard && (
        <Tooltip title="Dashboard">
          <IconButton color="error" onClick={() => campaginInfo?.handleCampaignDashboardClick(cell.row.original.id)}>
            <DashboardOutlined />
          </IconButton>
        </Tooltip>
      )}
    </Stack>
  );
};

export default TableActions;
