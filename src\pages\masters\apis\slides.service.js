import axiosServices from 'utils/axios_node';

const API_BASE_URL = `${import.meta.env.VITE_APP_APPLICATION_API_URL}/api`;

export const fetchSlidesService = async () => {
  const response = await axiosServices.get(`${API_BASE_URL}/slides`);
  return response.data;
};

export const addSlideService = async (slide) => {
  const response = await axiosServices.post(`${API_BASE_URL}/slides`, slide);
  return response.data;
};

export const updateSlideService = async (id, slide) => {
  const response = await axiosServices.put(`${API_BASE_URL}/slides/${id}`, slide);
  return response.data;
};

export const deleteSlideService = async (id) => {
  const response = await axiosServices.delete(`${API_BASE_URL}/slides/${id}`);
  return response.data;
};
