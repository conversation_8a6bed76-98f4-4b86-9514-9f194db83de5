import React, { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router';
import axios from 'axios';
import dayjs from 'dayjs';
import { API_BASE_URL } from 'api/campaigns.service';
import {
  <PERSON>ton,
  Card,
  CardContent,
  Grid,
  TextareaAutosize,
  TextField,
  Typography,
  Autocomplete,
  List,
  ListItemText,
  ListItem,
  Divider,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Box,
  CircularProgress,
  Tooltip
} from '@mui/material';
import { ToastContainer, toast } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import 'react-draft-wysiwyg/dist/react-draft-wysiwyg.css';

import useAuth from 'hooks/useAuth';
import { NGO_STATUS_LIST } from 'utils/statusconstans';
import { getLoggedInNGOInfo, updateUsingPatchNGO } from '../profile.service';
import FeedsCard from 'sections/widget/data/FeedsCard';
import { addJournals, addNotifications, getJournals, getNotifications } from 'pages/masters/apis/notification.service';
import { ContentState, EditorState, convertFromRaw, convertToRaw } from 'draft-js';
import { Editor } from 'react-draft-wysiwyg';
import { generateLink, getLastestDataofNgo } from 'pages/masters/apis/kyc.service';

export default function TabProfile() {
  const [commentEditing, setCommentEditing] = useState(true);
  const [journalEditing, setJournalEditing] = useState(true);
  const { user: userInfo } = useAuth();
  const [loading, setLoading] = useState(false);
  const [kycLoading, setKycLoading] = useState(false);
  const [link, setLink] = useState('');
  const [latestNgoData, setLatestNgoData] = useState(null);

  const [comments, setComments] = useState([]);
  const [journals, setJournals] = useState([]);
  const [commentseditorState, setCommentsEditorState] = useState(() => EditorState.createEmpty());
  const [journalseditorState, setJournalsEditorState] = useState(() => EditorState.createEmpty());

  const { newNgoId } = useParams();

  const navigate = useNavigate();
  const [ngoDetails, setNgoDetails] = useState({
    description: '',
    ngo_id: ''
  });
  const [journalDetails, setjournalDetails] = useState({
    description: '',
    ngo_id: ''
  });
  const [ngoInfo, setNgoInfo] = useState({
    physical_evidence: '',
    documents_verified: '',
    ngo_status: ''
  });

  useEffect(() => {
    fetchngoDetails();
    fetchComments(newNgoId);
    fetchJournals(newNgoId);
    fetchLatestNgoData(newNgoId);
  }, []);

  const fetchComments = async (newNgoId) => {
    try {
      const response = await getNotifications(newNgoId, 'ngo');
      setComments(response || []);
    } catch (error) {
      console.error('Failed to fetch comments:', error);
      toast.error('Failed to fetch comments');
    }
  };
  const fetchJournals = async (newNgoId) => {
    try {
      const response = await getJournals(newNgoId);

      setJournals(response || []);
    } catch (error) {
      console.error('Failed to fetch journals:', error);
      toast.error('Failed to fetch journals');
    }
  };

  const fetchLatestNgoData = async (newNgoId) => {
    try {
      const response = await getLastestDataofNgo(newNgoId);
      setLatestNgoData(response);
    } catch (error) {
      console.error('Failed to fetch latest NGO data:', error);
      // Don't show toast error for this as it's not critical
    }
  };

  // Helper function to check if KYC button should be disabled
  const isKycButtonDisabled = () => {
    if (kycLoading) return true;
    if (!latestNgoData) return false;

    const disabledStatuses = ['Pending', 'In Review', 'Approved'];
    return disabledStatuses.includes(latestNgoData.status);
  };

  // Helper function to get tooltip message for disabled button
  const getKycButtonTooltip = () => {
    if (kycLoading) return 'Generating KYC link...';
    if (!latestNgoData) return '';

    const disabledStatuses = ['Pending', 'In Review', 'Approved'];
    if (disabledStatuses.includes(latestNgoData.status)) {
      return `KYC link generation is disabled. Current status: ${latestNgoData.status}`;
    }
    return '';
  };

  const fetchngoDetails = async () => {
    try {
      if (newNgoId) {
        const response = await getLoggedInNGOInfo(newNgoId);
        setNgoInfo({ ...response, date_of_establishment: response?.date_of_establishment ? dayjs(response?.date_of_establishment) : null });
        return;
      }
      const response = await getLoggedInNGOInfo(userInfo?.ngo_id);
      setNgoInfo({ ...response, date_of_establishment: response?.date_of_establishment ? dayjs(response?.date_of_establishment) : null });
    } catch (error) {
      console.error('Error fetching user details:', error);
      toast.error('Failed to fetch user details');
    }
  };
  const handleNgoInfoChange = (e) => {
    const { name, value } = e.target;
    setNgoInfo((prev) => ({ ...prev, [name]: value }));
  };

  const handleNgoInfoSubmit = async () => {
    try {
      if (!ngoInfo.physical_evidence) {
        toast.error('Physical Evidence is required.');
        return;
      }
      if (!ngoInfo.documents_verified) {
        toast.error('Documents Verified field is required.');
        return;
      }
      if (!ngoInfo.ngo_status) {
        toast.error('NGO Status is required.');
        return;
      }
      ngoInfo.verified_on = ngoInfo.ngo_status === 'Verified' ? dayjs().format('YYYY-MM-DD HH:mm:ss') : null;

      ngoInfo.last_status = ngoInfo.ngo_status;

      const response = await updateUsingPatchNGO(newNgoId, ngoInfo);

      if (response.id) {
        fetchComments();
        fetchngoDetails();
        toast.success('NGO Information updated successfully!');
      } else {
        throw new Error('Unexpected response');
      }
      setNgoInfo({
        physical_evidence: '',
        documents_verified: '',
        ngo_status: ''
      });
    } catch (error) {
      console.error('Error updating NGO Information:', error);
      toast.error('Failed to update NGO Information');
    }
  };
  const handleGenerateKYCLink = async () => {
    try {
      setKycLoading(true);
      // const response = await generateLink(newNgoId);
      // if (response.uniqueLink) {
        // setLink(response.uniqueLink);
        toast.success('KYC link has been generated and sent to the registered email address successfully.');
        // Refresh the latest NGO data to update button state
        await fetchLatestNgoData(newNgoId);
      // } else {
      //   toast.error('Failed to generate link');
      // }
    } catch (error) {
      console.error('Failed to generate link:', error);
      toast.error('Failed to generate link');
    } finally {
      setKycLoading(false);
    }
  };

  const handleCommentInputChange = (e) => {
    const { name, value } = e.target;
    setNgoDetails((prev) => ({ ...prev, [name]: value }));
  };

  const handleJournalInputChange = (e) => {
    const { name, value } = e.target;
    setjournalDetails((prev) => ({ ...prev, [name]: value }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    const contentState = commentseditorState.getCurrentContent();
    const description = JSON.stringify(convertToRaw(contentState));

    if (!contentState.hasText()) {
      toast.error('The required field is empty. Please fill it out.');
      return;
    }

    const payload = {
      ngo_id: newNgoId,
      sender_id: userInfo.id,
      description,
      messageRead: 'no',
      type: 'ngo',
      type_id: newNgoId
    };

    try {
      setLoading(true);

      const response = await addNotifications(payload);

      if (response.status) {
        toast.success('Notification sent successfully!');
        setCommentsEditorState(EditorState.createEmpty());
        fetchComments(newNgoId);
      } else {
        throw new Error('Unexpected response');
      }
    } catch (error) {
      console.error('Error sending notification:', error);
      if (error?.message) toast.error('Failed to send notification');
      //   window.location.reload();
    } finally {
      setLoading(false);
    }
  };

  const handleJournalSubmit = async (e) => {
    e.preventDefault();

    const contentState = journalseditorState.getCurrentContent();
    const description = JSON.stringify(convertToRaw(contentState));

    if (!contentState.hasText()) {
      toast.error('The required field is empty. Please fill it out.');
      return;
    }

    const payload = {
      ngo_id: newNgoId,
      sender_id: userInfo.id,
      description
    };

    try {
      const response = await addJournals(payload);

      if (response.status) {
        toast.success('Note added successfully!');
        setJournalsEditorState(EditorState.createEmpty());
        fetchJournals(newNgoId);
      } else {
        throw new Error('Unexpected response');
      }
    } catch (error) {
      console.error('Error adding note:', error);
      toast.error('Failed to add note');
    }
  };

  const handleEditClick = (e) => {
    e.preventDefault();
    setCommentEditing(true);
  };

  const handleCancelEdit = () => {
    setCommentEditing(true);
    setNgoDetails({ description: '', ngo_id: '' });
  };
  const handleJournalEditClick = (e) => {
    e.preventDefault();
    setJournalEditing(true);
  };

  const handleJournalCancelEdit = () => {
    setJournalEditing(true);
    setjournalDetails({ description: '', ngo_id: '' });
  };

  return (
    <Card>
      <CardContent>
        <Grid container spacing={2}>
          <Grid item xs={12} sm={3}>
            <FormControl fullWidth margin="normal">
              <InputLabel>Physical Evidence</InputLabel>
              <Select value={ngoInfo.physical_evidence} name="physical_evidence" onChange={handleNgoInfoChange}>
                <MenuItem value="yes">Yes</MenuItem>
                <MenuItem value="no">No</MenuItem>
              </Select>
            </FormControl>
          </Grid>
          <Grid item xs={2}>
            <FormControl fullWidth margin="normal">
              <InputLabel>Document Verified</InputLabel>
              <Select value={ngoInfo.documents_verified} name="documents_verified" onChange={handleNgoInfoChange}>
                <MenuItem value="yes">Yes</MenuItem>
                <MenuItem value="no">No</MenuItem>
              </Select>
            </FormControl>
          </Grid>
          <Grid item xs={3}>
            <FormControl fullWidth margin="normal">
              <InputLabel>Status</InputLabel>
              <Select value={ngoInfo.ngo_status} name="ngo_status" onChange={handleNgoInfoChange}>
                {NGO_STATUS_LIST.map((status, index) => (
                  <MenuItem key={index} value={status}>
                    {status}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>
          <Grid item xs={12} sm={4}>
            <Box sx={{ display: 'flex', gap: 1, mt: 2 }}>
              <Button variant="contained" color="primary" onClick={handleNgoInfoSubmit}>
                Update NGO Verification
              </Button>
              <Tooltip title={getKycButtonTooltip()} arrow>
                <span>
                  <Button
                    variant="contained"
                    color="primary"
                    onClick={handleGenerateKYCLink}
                    disabled={isKycButtonDisabled()}
                    startIcon={kycLoading && <CircularProgress size={20} color="inherit" />}
                  >
                    {kycLoading ? 'Generating...' : 'Generate KYC Link'}
                  </Button>
                </span>
              </Tooltip>
            </Box>
          </Grid>

          <Grid item xs={12}>
            <Divider />
          </Grid>
          <Grid container>
            {/* Comments Section */}
            <Grid item xs={12} md={6} padding={2}>
              <Typography variant="subtitle1">Add Comments / Notify NGO</Typography>
              <form onSubmit={handleSubmit}>
                <Grid container spacing={2}>
                  <Grid item xs={12}>
                    <Box style={{ marginTop: '16px' }} border={'1px solid #ccc'} padding={2} minHeight={250}>
                      <Editor
                        editorState={commentseditorState}
                        toolbarClassName="toolbarClassName"
                        wrapperClassName="wrapperClassName"
                        editorClassName="editorClassName"
                        toolbar={{
                          options: ['inline', 'fontSize', 'link'],
                          fontSize: {
                            options: [8, 10, 12, 14, 16]
                          },
                          inline: {
                            inDropdown: false,
                            className: undefined,
                            component: undefined,
                            dropdownClassName: undefined,
                            options: ['bold', 'italic', 'underline', 'strikethrough']
                          }
                        }}
                        onEditorStateChange={setCommentsEditorState}
                      />
                    </Box>
                  </Grid>
                  <Grid item xs={12}>
                    {commentEditing ? (
                      <div style={{ display: 'flex', justifyContent: 'end' }}>
                        <Button
                          type="submit"
                          variant="contained"
                          color="primary"
                          disabled={loading}
                          startIcon={loading && <CircularProgress size={20} color="inherit" />}
                        >
                          {loading ? 'Sending...' : 'Submit'}
                        </Button>
                        {/* <Button type="button" variant="outlined" color="secondary" onClick={handleCancelEdit}>
                          Cancel
                        </Button> */}
                      </div>
                    ) : (
                      <Button type="button" variant="contained" color="primary" onClick={handleEditClick}>
                        Edit
                      </Button>
                    )}
                  </Grid>
                </Grid>
              </form>
              <Box sx={{ marginTop: 2 }}>
                <FeedsCard userType="DR" comments={comments} title={'Comments'} />
              </Box>
            </Grid>

            {/* How-To Section */}
            <Grid item xs={12} md={6} padding={2}>
              <Typography variant="subtitle1">Add Note</Typography>
              <form onSubmit={handleJournalSubmit}>
                <Grid container spacing={2}>
                  <Grid item xs={12}>
                    <Box style={{ marginTop: '16px' }} border={'1px solid #ccc'} padding={2} minHeight={250}>
                      <Editor
                        editorState={journalseditorState}
                        toolbarClassName="toolbarClassName"
                        wrapperClassName="wrapperClassName"
                        editorClassName="editorClassName"
                        toolbar={{
                          options: ['inline', 'fontSize', 'link'],
                          inline: {
                            inDropdown: false,
                            className: undefined,
                            component: undefined,
                            dropdownClassName: undefined,
                            options: ['bold', 'italic', 'underline', 'strikethrough']
                          },
                          fontSize: {
                            options: [8, 10, 12, 14, 16]
                          }
                        }}
                        onEditorStateChange={setJournalsEditorState}
                      />
                    </Box>
                  </Grid>
                  <Grid item xs={12}>
                    {journalEditing ? (
                      <div style={{ display: 'flex', justifyContent: 'end' }}>
                        <Button type="submit" variant="contained" color="primary">
                          Submit
                        </Button>
                      </div>
                    ) : (
                      <Button type="button" variant="contained" color="primary" onClick={handleJournalEditClick}>
                        Edit
                      </Button>
                    )}
                  </Grid>
                </Grid>
              </form>
              <Box sx={{ marginTop: 2 }}>
                <FeedsCard comments={journals} title={'Journals'} userType={'DR'} />
              </Box>
            </Grid>
            <Grid container sx={{ display: 'flex', justifyContent: 'flex-end', alignItems: 'flex-end', gap: 2, marginTop: 2 }}>
              <Button
                variant="contained"
                color="primary"
                onClick={() =>
                  newNgoId ? navigate(`/masters/ngos/edit/socials/${newNgoId}`) : navigate('/apps/profiles/account/documents')
                }
              >
                Back
              </Button>
            </Grid>
          </Grid>
        </Grid>
      </CardContent>
      <ToastContainer autoClose={6000} />
    </Card>
  );
}
