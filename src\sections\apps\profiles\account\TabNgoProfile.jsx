// material-ui
import {
  Box,
  Button,
  CardMedia,
  Chip,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  Divider,
  Grid,
  Link,
  List,
  ListItem,
  ListItemIcon,
  ListItemSecondaryAction,
  Rating,
  Stack,
  Tooltip,
  Typography,
  useMediaQuery
} from '@mui/material';
import { ToastContainer, toast } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';

// third-party
import { PatternFormat } from 'react-number-format';

// project import
import MainCard from 'components/MainCard';
import Avatar from 'components/@extended/Avatar';
import LinearWithLabel from 'components/@extended/progress/LinearWithLabel';

// assets
import {
  AimOutlined,
  CheckCircleFilled,
  CheckCircleTwoTone,
  CloudUploadOutlined,
  CreditCardOutlined,
  EditOutlined,
  EnvironmentOutlined,
  ExclamationCircleTwoTone,
  EyeOutlined,
  FacebookOutlined,
  InstagramOutlined,
  MailOutlined,
  PhoneOutlined,
  TwitterOutlined,
  YoutubeOutlined
} from '@ant-design/icons';
import { useEffect, useState } from 'react';
import { useNavigate, useParams } from 'react-router';
import { BASE_URL, getLoggedInNGOInfo, IMAGE_BASE_URL, updateUsingPatchNGO } from '../profile.service';
import dayjs from 'dayjs';
import moment from 'moment';
import { Link as RouterLink } from 'react-router-dom';
import { getAllCategories, getNGOBasedCategories, getSubcategoriesByCategoryId } from 'api/categories.service';
import {
  calculateProfileCompletion,
  checkRequiredFieldsCompletion,
  getConvertedFileName,
  getSessionStorageItem
} from 'utils/permissionUtils';
import UploadSingleFile from 'components/third-party/dropzone/SingleFile';

import useAuth from 'hooks/useAuth';
import { API_BASE_URL, getAllCampaigns } from 'api/campaigns.service';
import { useFormik } from 'formik';
import FeedsCard from 'sections/widget/data/FeedsCard';
import { getNotifications } from 'pages/masters/apis/notification.service';
import { getQuestionsList } from 'api/questions.service';
import { getAllBankDetails } from './tabs.service';

const avatarImage = () => {};

// ==============================|| ACCOUNT PROFILE - BASIC ||============================== //

const TabNgoProfile = () => {
  const matchDownMD = useMediaQuery((theme) => theme.breakpoints.down('md'));
  const userInfo = getSessionStorageItem('user');
  const { user } = useAuth();
  const { newNgoId } = useParams();
  const navigate = useNavigate();
  const [ngoDetails, setNgoDetails] = useState(null);
  const [categories, setCategories] = useState([]);
  const [campaigns, setCampaigns] = useState(0);
  const [profileCompletePercentage, setProfileCompletePercentage] = useState(0);
  const [fieldsCompleted, setFieldCompleted] = useState({
    basinInfoComplted: false,
    aboutInfoCompleted: false,
    allMandatoryDocsUploaded: false,
    isbankDetailsAdded: false
  });
  const [openUploadDialog, setOpenUploadDialog] = useState(false);
  const [isHovered, setIsHovered] = useState(false);
  const [comments, setComments] = useState([]);
  const [filteredFields, setFilteredFields] = useState([]);
  const [questions, setQuestions] = useState([]);
  const [registrationDetails, setRegistrationDetails] = useState(null);
  const [bankDetails, setBankDetails] = useState(null);
  const [selectedCategory, setSelectedCategory] = useState([]);
  const formik = useFormik({
    initialValues: {
      files: []
    },
    onSubmit: async (values) => {
      let formData = new FormData();
      if (values?.files && values?.files?.length > 0) {
        const convertedFileName = getConvertedFileName(values?.files[0]?.name);
        formData.append('fileName', `${convertedFileName}`);
        formData.append('file', values?.files[0], `${convertedFileName}`);
      }
      await updateUsingPatchNGO(ngoDetails?.id, formData, 'NgoProfile');
      toast.success('File uploaded Successfully');
      fetchngoDetails();
    }
  });

  useEffect(() => {
    fetchngoDetails();
    fetchCategories();
    fetchQuestions();
    fetchBankDetails();

    if (user?.ngo_id) {
      fetchCampaigns(user?.ngo_id);
      fetchComments(user?.ngo_id);
    }

    if (newNgoId) {
      fetchComments(newNgoId);
    }
  }, []);

  useEffect(() => {
    {
      ngoDetails && questions && getQuestion();
    }
  }, [ngoDetails, questions]);

  const getQuestion = () => {
    if (ngoDetails.ngo_type && questions.length > 0) {
      const matchedFields = questions.filter((question) =>
        question.ngo_type
          .split(',')
          .map((type) => type.trim())
          .includes(ngoDetails.ngo_type)
      );
      setFilteredFields(matchedFields);
    }
  };
  const fetchNgoDataAndCalculate = async (ngoDetails) => {
    try {
      const profilepercentage = await calculateProfileCompletion(ngoDetails);
      const { basinInfoComplted, aboutInfoCompleted, allMandatoryDocsUploaded, isbankDetailsAdded } =
        await checkRequiredFieldsCompletion(ngoDetails);
      setFieldCompleted({
        basinInfoComplted: basinInfoComplted,
        aboutInfoCompleted: aboutInfoCompleted,
        allMandatoryDocsUploaded: allMandatoryDocsUploaded,
        isbankDetailsAdded: isbankDetailsAdded
      });
      setProfileCompletePercentage(profilepercentage);
    } catch (error) {
      console.error('Error fetching NGO data:', error);
    }
  };
  useEffect(() => {
    if (categories.length > 0) {
      fetchSavedData();
    }
  }, [categories]);

  const handleOpenDialog = () => {
    setOpenUploadDialog(true);
  };

  const handleCloseDialog = () => {
    setOpenUploadDialog(false);
  };
  const fetchCategories = async () => {
    try {
      const response = await getAllCategories('NgoProfile');
      setCategories(response);
    } catch (error) {
      console.error('Failed to fetch categories:', error);
    }
  };
  const fetchCampaigns = async (ngo_id) => {
    try {
      const response = await getAllCampaigns('All', ngo_id, 'NgoProfile');
      setCampaigns(response.length);
    } catch (error) {
      console.error('Failed to fetch campaigns:', error);
    }
  };

  const fetchQuestions = async () => {
    try {
      const response = await getQuestionsList();
      setQuestions(response);
    } catch (error) {
      console.error('Error fetching questions:', error);
      toast.error('Failed to fetch questions');
    }
  };
  const fetchBankDetails = async () => {
    try {
      const response = await getAllBankDetails(user?.ngo_id ? user?.ngo_id : newNgoId);
      if (response && response.length > 0) {
        setBankDetails(response[0]);
      }
    } catch (error) {
      console.error('Error fetching bank details:', error);
      toast.error('Failed to fetch bank details');
    }
  };

  const fetchngoDetails = async () => {
    try {
      if (newNgoId) {
        const response = await getLoggedInNGOInfo(newNgoId);
        setNgoDetails({
          ...response,
          date_of_establishment: response?.date_of_establishment ? dayjs(response?.date_of_establishment).format("DD-MM-YYYY") : null
        });
        const regDetails = response?.registration_details ? JSON.parse(response.registration_details) : {};
        const parsedDetails = typeof regDetails == 'string' ? JSON.parse(regDetails) : regDetails;
        setRegistrationDetails(parsedDetails);
        fetchNgoDataAndCalculate(response);

        return;
      }
      const response = await getLoggedInNGOInfo(userInfo?.ngo_id);
      
      setNgoDetails({
        ...response,
        date_of_establishment: response?.date_of_establishment ? dayjs(response?.date_of_establishment).format("DD-MM-YYYY") : null
      });
      const regDetails = response?.registration_details ? JSON.parse(response.registration_details) : {};
      const parsedDetails = typeof regDetails == 'string' ? JSON.parse(regDetails) : regDetails;
      setRegistrationDetails(parsedDetails);

      fetchNgoDataAndCalculate(response);
    } catch (error) {
      console.error('Error fetching ngo details:', error);
      toast.error('Failed to fetch ngo details');
    }
  };

  const fetchSavedData = async () => {
    try {
      const ngoId = newNgoId || userInfo?.ngo_id;

      const categoryResponse = await getNGOBasedCategories(ngoId, 'NgoProfile');
      if (!categoryResponse || categoryResponse.length === 0) {
        return;
      }

      const savedCategories = categoryResponse.map((saved) => categories.find((cat) => cat.id === saved.category_id));

      const selectedCategory = savedCategories[0];
      setSelectedCategory({ ...selectedCategory, subCategories: categoryResponse[0]?.subCategories });
    } catch (error) {
      console.error('Failed to fetch saved data:', error);
      toast.error('Failed to fetch saved categories');
    }
  };

  const fetchComments = async (newNgoId) => {
    try {
      const response = await getNotifications(newNgoId, 'ngo');
      setComments(response || []);
    } catch (error) {
      console.error('Failed to fetch comments:', error);
      toast.error('Failed to fetch comments');
    }
  };

  const handleSubmitReview = async (e) => {
    if (profileCompletePercentage < 100) {
      toast.error('Please check youre profile its not atleast 100% complete');
      return;
    }
    e.preventDefault();
    if (window.confirm('Are you sure you want to submit profile for review')) {
      try {
        if (!newNgoId) {
          await updateUsingPatchNGO(userInfo?.ngo_id, { ngo_status: 'In Review', last_status: 'In Review' }, 'NGOProfile');
          toast.success('Your profile has been submitted for review.');
          fetchngoDetails();
          return;
        }
        // navigate(`/masters/ngos/edit/my-account/${newNgoId}`);
      } catch (error) {
        console.error('Error updating ngo details:', error);
        toast.error('Failed to update NGO details');
      }
    }
  };

  const mediaSX = {
    position: 'relative',
    width: 150,
    height: 140,
    borderRadius: 1,
    overflow: 'hidden',
    border: '4px solid orange',
    // borderRadius:'50%',
    cursor: 'pointer'
  };

  const iconOverlaySX = {
    position: 'absolute',
    top: '50%',
    left: '50%',
    transform: 'translate(-50%, -50%)',
    display: isHovered ? 'flex' : 'none',
    gap: '10px',
    color: 'rgba(0, 0, 0, 0.7)',
    fontSize: '24px'
  };
  return (
    <Grid container spacing={3}>
      <Grid item xs={12} sm={5} md={4} xl={3}>
        <Grid container spacing={3}>
          <Grid item xs={12}>
            <MainCard style={{ border: 'none' }}>
              <Grid container spacing={3}>
                <Grid item xs={12}>
                  <Stack direction="row" justifyContent="flex-end">
                    <Chip
                      color={
                        ngoDetails?.ngo_status === 'Verified'
                          ? 'success'
                          : ngoDetails?.ngo_status === 'In Review'
                            ? 'info'
                            : ngoDetails?.ngo_status === 'Pending'
                              ? 'warning'
                              : 'error'
                      }
                      label={ngoDetails?.ngo_status}
                      size="small"
                    />

                    {/* <Chip label="Pro" size="small" color={ngoDetails?.ngo_status === "Pending" ? : } /> */}
                  </Stack>
                  <Stack spacing={2.5} alignItems="center" marginTop={1}>
                    {/* <Avatar alt="Avatar 1" size="xl" src={avatarImage(`./default.png`)} /> */}
                    <Stack spacing={2.5} alignItems="center">
                      {ngoDetails?.fileName ? (
                        // <Avatar alt="NGO Avatar" size="xl" src={`${BASE_URL}/ngoProfileImages/${ngoDetails?.fileName}`} />
                        <Box sx={mediaSX} onMouseEnter={() => setIsHovered(true)} onMouseLeave={() => setIsHovered(false)}>
                          <CardMedia
                            component="img"
                            image={`${API_BASE_URL}/fetchNgoImages/${ngoDetails?.fileName}`}
                            title={ngoDetails?.fileName}
                            sx={{ width: '100%', height: '100%' }}
                          />
                          {/* Overlay Icons */}
                          <Box sx={iconOverlaySX}>
                            <Tooltip title="View Image">
                              <EyeOutlined
                                style={{
                                  color: '#0288d1',
                                  fontSize: '24px',
                                  cursor: 'pointer'
                                }}
                                onClick={() => window.open(`${API_BASE_URL}/fetchNgoImages/${ngoDetails?.fileName}`, '_blank')}
                              />
                            </Tooltip>
                            <Tooltip title="Edit Image">
                              <EditOutlined
                                style={{
                                  color: '#f57c00', // Orange color for the edit icon
                                  fontSize: '24px',
                                  cursor: 'pointer'
                                }}
                                onClick={handleOpenDialog}
                              />
                            </Tooltip>
                          </Box>
                        </Box>
                      ) : (
                        <Avatar alt="Upload Avatar" size="xl" onClick={handleOpenDialog} style={{ cursor: 'pointer' }}>
                          <CloudUploadOutlined style={{ fontSize: '3rem' }} />
                        </Avatar>
                      )}

                      {/* Upload Dialog */}
                      <Dialog open={openUploadDialog} onClose={handleCloseDialog} maxWidth="sm" fullWidth>
                        <DialogTitle>Upload File</DialogTitle>
                        <DialogContent>
                          <Stack spacing={1.5} alignItems="center">
                            <UploadSingleFile
                              setFieldValue={formik.setFieldValue}
                              file={formik.values.files}
                              error={formik.touched.files && !!formik.errors.files}
                            />
                            <Stack spacing={0}>
                              <Typography align="center" variant="caption" color="secondary">
                                Allowed *.jpeg, *.jpg, *.png, *.gif
                              </Typography>
                              <Typography align="center" variant="caption" color="secondary">
                                Max size of 2 MB
                              </Typography>
                            </Stack>
                          </Stack>

                          {/* Show errors if any */}
                          {formik.touched.files && formik.errors.files && (
                            <Typography color="error" variant="caption">
                              {formik.errors.files}
                            </Typography>
                          )}
                        </DialogContent>
                        <DialogActions>
                          <Button onClick={handleCloseDialog} color="secondary">
                            Cancel
                          </Button>
                          <Button
                            onClick={() => {
                              handleCloseDialog();
                              formik.handleSubmit();
                            }}
                            variant="contained"
                            color="primary"
                          >
                            Upload
                          </Button>
                        </DialogActions>
                      </Dialog>
                    </Stack>

                    <Stack spacing={0.5} alignItems="center">
                      <Typography color="secondary">{ngoDetails?.name}</Typography>
                      <Typography color="secondary">{ngoDetails?.ngo_type}</Typography>
                      <Stack direction="row" alignItems="center" spacing={2} sx={{ mt: 2, marginLeft: 2, fontSize: 20 }}>
                        <Rating name="grade-rating" value={parseFloat(ngoDetails?.rating)} precision={0.5} disabled />
                      </Stack>
                    </Stack>
                  </Stack>
                </Grid>
                <Grid item xs={12}>
                  <Divider />
                </Grid>
                <Grid item xs={12}>
                  <Stack direction="row" justifyContent="space-around" alignItems="center">
                    <Stack spacing={0.5} alignItems="center">
                      <Typography variant="h5">{campaigns || 0}</Typography>
                      <Typography color="secondary">Campaigns</Typography>
                    </Stack>
                    <Divider orientation="vertical" flexItem />
                    <Stack spacing={0.5} alignItems="center">
                      <Typography variant="h5">0</Typography>
                      <Typography color="secondary">Contributors</Typography>
                    </Stack>
                  </Stack>
                </Grid>
                <Grid item xs={12}>
                  <Divider />
                </Grid>
                <Grid item xs={12}>
                  <LinearWithLabel
                    value={parseInt(profileCompletePercentage)}
                    color={
                      parseInt(profileCompletePercentage) < 50
                        ? 'error'
                        : parseInt(profileCompletePercentage) >= 50 && parseInt(profileCompletePercentage) < 100
                          ? 'primary'
                          : 'success'
                    }
                  />

                  <Typography color="secondary"> Profile completed </Typography>
                </Grid>
                {/* <Grid item xs={12}>
                  <Divider />
                </Grid>
                <Grid item xs={12}>
                  <List component="nav" aria-label="main mailbox folders" sx={{ py: 0, '& .MuiListItem-root': { p: 0, py: 1 } }}>
                    <ListItem>
                      <ListItemIcon>
                        <MailOutlined />
                      </ListItemIcon>
                      <ListItemSecondaryAction>
                        <Typography align="right">{ngoDetails?.email ? ngoDetails?.email : '-'}</Typography>
                      </ListItemSecondaryAction>
                    </ListItem>
                    <ListItem>
                      <ListItemIcon>
                        <PhoneOutlined />
                      </ListItemIcon>
                      <ListItemSecondaryAction>
                        <Typography align="right">
                          {ngoDetails?.point_of_contact_mobile_number ? ngoDetails?.point_of_contact_mobile_number : '-'}
                        </Typography>
                      </ListItemSecondaryAction>
                    </ListItem>
                    <ListItem>
                      <ListItemIcon>
                        <CreditCardOutlined />
                      </ListItemIcon>
                      <ListItemSecondaryAction>
                        <Typography align="right">{ngoDetails?.pan ? ngoDetails?.pan : '-'}</Typography>
                      </ListItemSecondaryAction>
                    </ListItem>
                    <ListItem>
                      <ListItemIcon>
                        <AimOutlined />
                      </ListItemIcon>
                      <ListItemSecondaryAction>
                        <Typography align="right">{ngoDetails?.state ? ngoDetails?.state : '-'}</Typography>
                      </ListItemSecondaryAction>
                    </ListItem>
                  </List>
                </Grid> */}
                {comments && comments?.length > 0 && (
                  <Grid item xs={12}>
                    <Divider />
                    <FeedsCard comments={comments} title={'Comments'} />
                  </Grid>
                )}

                <Divider />
                {/* {!newNgoId && ngoDetails && (ngoDetails.ngo_status === 'Pending' || ngoDetails.ngo_status === 'New' || ngoDetails.ngo_status === 'OS Requirements') && (
                  <Grid item xs={12}>
                    <Button onClick={handleSubmitReview} variant="contained" color="primary" sx={{ mr: 2 }}>
                      Submit for Review
                    </Button>
                  </Grid>
                )} */}
              </Grid>
            </MainCard>
          </Grid>
        </Grid>
      </Grid>

      <Grid item xs={12} sm={7} md={8} xl={9}>
        <Grid container spacing={3}>
          <Grid item xs={12}>
            <MainCard
              title="Basic Information"
              secondary={
                <RouterLink to={newNgoId ? `/masters/ngos/edit/basic/${newNgoId}` : `/apps/profiles/account/basic`} color="primary">
                  {fieldsCompleted.basinInfoComplted ? (
                    <Tooltip title="Completed" style={{ marginRight: '1rem' }}>
                      <CheckCircleTwoTone twoToneColor="#00A854" />
                    </Tooltip>
                  ) : (
                    <Tooltip title="Incomplete" style={{ marginRight: '1rem' }}>
                      <ExclamationCircleTwoTone twoToneColor="red" />
                    </Tooltip>
                  )}
                  {user?.roleInfo?.name == 'DR_Management' ||
                  user?.roleInfo?.name === 'DR_Staff' ||
                  (ngoDetails?.ngo_status !== 'In Review' && ngoDetails?.ngo_status !== 'Verified') ? (
                    <Tooltip title="Edit">
                      <EditOutlined />
                    </Tooltip>
                  ) : null}
                </RouterLink>
              }
            >
              <List sx={{ py: 0 }}>
                <ListItem divider>
                  <Grid container spacing={matchDownMD ? 0.5 : 3}>
                    <Grid item xs={12} md={6}>
                      <Stack spacing={0.5}>
                        <Typography color="secondary">Contact person name</Typography>
                        <Typography>{ngoDetails?.point_of_contact_name ? ngoDetails?.point_of_contact_name : '-'}</Typography>
                      </Stack>
                    </Grid>
                    <Grid item xs={12} md={6}>
                      <Stack spacing={0.5}>
                        <Typography color="secondary">Email</Typography>
                        <Typography>{ngoDetails?.email ? ngoDetails?.email : '-'}</Typography>
                      </Stack>
                    </Grid>
                    {/* <Grid item xs={12} md={6}>
                      <Stack spacing={0.5}>
                        <Typography color="secondary">Date of Establishment</Typography>
                        <Typography>
                          {ngoDetails?.date_of_establishment ? moment(ngoDetails?.date_of_establishment).format('DD-MM-YYYY') : '-'}
                        </Typography>
                      </Stack>
                    </Grid> */}
                  </Grid>
                </ListItem>
                <ListItem divider>
                  <Grid container spacing={matchDownMD ? 0.5 : 3}>
                    <Grid item xs={12} md={6}>
                      <Stack spacing={0.5}>
                        <Typography color="secondary">Mobile number</Typography>
                        <Typography>
                          {ngoDetails?.point_of_contact_mobile_number ? ngoDetails?.point_of_contact_mobile_number : '-'}
                        </Typography>
                      </Stack>
                    </Grid>
                    <Grid item xs={12} md={6}>
                      <Stack spacing={0.5}>
                        <Typography color="secondary">PAN number</Typography>
                        <Typography>{ngoDetails?.pan ? ngoDetails?.pan : '-'}</Typography>
                      </Stack>
                    </Grid>
                  </Grid>
                </ListItem>
                {/* <ListItem divider>
                  <Grid container spacing={matchDownMD ? 0.5 : 3}>
                    <Grid item xs={12} md={6}>
                      <Stack spacing={0.5}>
                        <Typography color="secondary">Website Url</Typography>
                        <Typography>
                          {ngoDetails?.website_url ? (
                            <Link align="right" href={ngoDetails?.website_url} target="_blank">
                              {ngoDetails?.website_url}
                            </Link>
                          ) : (
                            '-'
                          )}
                        </Typography>
                      </Stack>
                    </Grid>
                    <Grid item xs={12} md={6}>
                      <Stack spacing={0.5}>
                        <Typography color="secondary">Documents</Typography>
                        <Typography>
                          {' '}
                          <Chip
                            onClick={() =>
                              navigate(newNgoId ? `/masters/ngos/edit/documents/${newNgoId}` : '/apps/profiles/account/documents')
                            }
                            label="View"
                            size="small"
                            color="primary"
                          />
                        </Typography>
                      </Stack>
                    </Grid>
                  </Grid>
                </ListItem> */}
                <ListItem>
                  <Stack spacing={0.5}>
                    <Typography color="secondary">Address</Typography>
                    <Typography>{ngoDetails?.current_address ? ngoDetails?.current_address : '-'}</Typography>
                  </Stack>
                </ListItem>
              </List>
            </MainCard>
          </Grid>
          {/* <Grid item xs={12}>
            <MainCard title="About">
              <Typography color="secondary">{ngoDetails?.about_us ? ngoDetails?.about_us : '-'}</Typography>
            </MainCard>
          </Grid> */}

          <Grid item xs={12}>
            <MainCard
              title="About NGO"
              secondary={
                <RouterLink to={newNgoId ? `/masters/ngos/edit/personal/${newNgoId}` : '/apps/profiles/account/personal'} color="primary">
                  {fieldsCompleted.aboutInfoCompleted ? (
                    <Tooltip title="Completed" style={{ marginRight: '1rem' }}>
                      <CheckCircleTwoTone twoToneColor="#00A854" />
                    </Tooltip>
                  ) : (
                    <Tooltip title="Incomplete" style={{ marginRight: '1rem' }}>
                      <ExclamationCircleTwoTone twoToneColor="red" />
                    </Tooltip>
                  )}
                  {user?.roleInfo?.name == 'DR_Management' ||
                  user?.roleInfo?.name === 'DR_Staff' ||
                  (ngoDetails?.ngo_status !== 'In Review' && ngoDetails?.ngo_status !== 'Verified') ? (
                    <Tooltip title="Edit">
                      <EditOutlined />
                    </Tooltip>
                  ) : null}
                </RouterLink>
              }
            >
              <List sx={{ py: 0 }}>
                <ListItem divider={!matchDownMD}>
                  <Grid container spacing={3}>
                    <Grid item xs={12} md={6}>
                      <Stack spacing={0.5}>
                        <Typography color="secondary">Darpan ID</Typography>
                        <Typography>{ngoDetails?.darpan_id ? ngoDetails?.darpan_id : '-'}</Typography>
                      </Stack>
                    </Grid>
                    <Grid item xs={12} md={6}>
                      <Stack spacing={0.5}>
                        <Typography color="secondary">Date of Establishment</Typography>
                        <Typography>
                          {ngoDetails?.date_of_establishment ? ngoDetails?.date_of_establishment : '-'}
                        </Typography>
                      </Stack>
                    </Grid>
                  </Grid>
                </ListItem>

                <ListItem divider>
                  <Grid container spacing={3}>
                    <Grid item xs={12} md={6}>
                      <Stack spacing={0.5}>
                        <Typography color="secondary">Category</Typography>
                        <Typography>{selectedCategory?.name ? selectedCategory?.name : '-'}</Typography>
                      </Stack>
                    </Grid>
                    <Grid item xs={12} md={6}>
                      <Stack spacing={0.5}>
                        <Typography color="secondary">Sub categories</Typography>
                        <Typography>{selectedCategory?.subCategories ? selectedCategory?.subCategories : '-'}</Typography>
                      </Stack>
                    </Grid>
                  </Grid>
                </ListItem>

                {/* <ListItem>
                  <Grid container spacing={3}>
                    <Grid item xs={12} md={6}>
                      <Stack spacing={0.5}>
                        <Typography color="secondary">Documents</Typography>
                        <Chip
                          style={{ width: '20%' }}
                          onClick={() =>
                            newNgoId ? navigate(`/masters/ngos/edit/documents/${newNgoId}`) : navigate('/apps/profiles/account/documents')
                          }
                          label="View"
                          size="small"
                          color="primary"
                        />
                      </Stack>
                    </Grid>
                     <Grid item xs={12} md={6}>
                      <Stack spacing={0.5}>
                        <Typography color="secondary">Images</Typography>
                        <Chip
                          style={{ width: '20%' }}
                          onClick={() =>
                            newNgoId ? navigate(`/masters/ngos/edit/images/${newNgoId}`) : navigate('/apps/profiles/account/images')
                          }
                          label="View"
                          size="small"
                          color="primary"
                        />
                      </Stack>
                    </Grid> 
                  </Grid>
                </ListItem> */}
              </List>
            </MainCard>
          </Grid>
          <Grid item xs={12}>
            <MainCard
              title="Documents"
              secondary={
                <RouterLink to={newNgoId ? `/masters/ngos/edit/documents/${newNgoId}` : '/apps/profiles/account/documents'} color="primary">
                  {fieldsCompleted.allMandatoryDocsUploaded ? (
                    <Tooltip title="Completed" style={{ marginRight: '1rem' }}>
                      <CheckCircleTwoTone twoToneColor="#00A854" />
                    </Tooltip>
                  ) : (
                    <Tooltip title="Incomplete" style={{ marginRight: '1rem' }}>
                      <ExclamationCircleTwoTone twoToneColor="red" />
                    </Tooltip>
                  )}
                  {user?.roleInfo?.name == 'DR_Management' ||
                  user?.roleInfo?.name === 'DR_Staff' ||
                  (ngoDetails?.ngo_status !== 'In Review' && ngoDetails?.ngo_status !== 'Verified') ? (
                    <Tooltip title="Edit">
                      <EditOutlined />
                    </Tooltip>
                  ) : null}
                </RouterLink>
              }
            ></MainCard>
          </Grid>
          <Grid item xs={12}>
            <MainCard
              title="Registration Details"
              secondary={
                <RouterLink to={newNgoId ? `/masters/ngos/edit/personal/${newNgoId}` : '/apps/profiles/account/personal'} color="primary">
                  {user?.roleInfo?.name == 'DR_Management' ||
                  user?.roleInfo?.name === 'DR_Staff' ||
                  (ngoDetails?.ngo_status !== 'In Review' && ngoDetails?.ngo_status !== 'Verified') ? (
                    <Tooltip title="Edit">
                      <EditOutlined />
                    </Tooltip>
                  ) : null}
                </RouterLink>
              }
            >
              <List sx={{ py: 0 }}>
                {/* {Object.entries(registrationDetails).map((field, index) => {
                    console  . log("field",field);
                })
              } */}
                <ListItem>
                  {registrationDetails &&
                    Object.entries(registrationDetails).map(([key, value]) => {
                      return (
                        <Grid key={key} container spacing={3}>
                          <Grid item xs={6} md={6}>
                            <Stack spacing={0.5}>
                              <Typography color="secondary">{key}</Typography>
                              <Typography>{value || '-'}</Typography>
                            </Stack>
                          </Grid>
                        </Grid>
                      );
                    })}
                </ListItem>

                {/* ))} */}
              </List>
            </MainCard>
          </Grid>
          <Grid item xs={12}>
            <MainCard
              title="Bank Details"
              secondary={
                <RouterLink
                  to={newNgoId ? `/masters/ngos/edit/bank-accounts/${newNgoId}` : '/apps/profiles/account/bank-accounts'}
                  color="primary"
                >
                  {fieldsCompleted.isbankDetailsAdded ? (
                    <Tooltip title="Completed" style={{ marginRight: '1rem' }}>
                      <CheckCircleTwoTone twoToneColor="#00A854" />
                    </Tooltip>
                  ) : (
                    <Tooltip title="Incomplete" style={{ marginRight: '1rem' }}>
                      <ExclamationCircleTwoTone twoToneColor="red" />
                    </Tooltip>
                  )}

                  {user?.roleInfo?.name == 'DR_Management' ||
                  user?.roleInfo?.name === 'DR_Staff' ||
                  (ngoDetails?.ngo_status !== 'In Review' && ngoDetails?.ngo_status !== 'Verified') ? (
                    <Tooltip title="Edit">
                      <EditOutlined />
                    </Tooltip>
                  ) : null}
                </RouterLink>
              }
            >
              <List sx={{ py: 0 }}>
                <Grid container spacing={2}>
                  {bankDetails &&
                    Object.entries(bankDetails)
                      .filter(([key]) => !['id', 'createdAt', 'updatedAt', 'ngo_id', 'ngoInfo'].includes(key)) // Filtering unwanted fields
                      .map(([key, value]) => (
                        <Grid item xs={12} sm={3} key={key}>
                          <Stack spacing={0.5}>
                            <Typography color="secondary" variant="subtitle2">
                              {key.replace(/_/g, ' ').toUpperCase()} {/* Formatting key names */}
                            </Typography>
                            <Typography variant="body1">{value || '-'}</Typography>
                          </Stack>
                        </Grid>
                      ))}
                </Grid>
              </List>
            </MainCard>
          </Grid>

          <Grid item xs={12}>
            <MainCard
              title="Socials"
              secondary={
                <RouterLink to={newNgoId ? `/masters/ngos/edit/socials/${newNgoId}` : '/apps/profiles/account/socials'} color="primary">
                  {user?.roleInfo?.name == 'DR_Management' ||
                  user?.roleInfo?.name === 'DR_Staff' ||
                  (ngoDetails?.ngo_status !== 'In Review' && ngoDetails?.ngo_status !== 'Verified') ? (
                    <Tooltip title="Edit">
                      <EditOutlined />
                    </Tooltip>
                  ) : null}
                </RouterLink>
              }
            >
              <List sx={{ py: 0 }}>
                <ListItem>
                  <Grid container spacing={3}>
                    {ngoDetails?.facebook_url && (
                      <Grid item xs={12} sm={6} md={3}>
                        <Link href={ngoDetails.facebook_url} target="_blank" rel="noopener noreferrer">
                          <Stack direction="row" spacing={1} alignItems="center">
                            <FacebookOutlined style={{ fontSize: '24px', color: '#3b5998' }} />
                            <Typography>Facebook</Typography>
                          </Stack>
                        </Link>
                      </Grid>
                    )}
                    {ngoDetails?.twitter_url && (
                      <Grid item xs={12} sm={6} md={3}>
                        <Link href={ngoDetails.twitter_url} target="_blank" rel="noopener noreferrer">
                          <Stack direction="row" spacing={1} alignItems="center">
                            <TwitterOutlined style={{ fontSize: '24px', color: '#1da1f2' }} />
                            <Typography>Twitter</Typography>
                          </Stack>
                        </Link>
                      </Grid>
                    )}
                    {ngoDetails?.youtube_url && (
                      <Grid item xs={12} sm={6} md={3}>
                        <Link href={ngoDetails.youtube_url} target="_blank" rel="noopener noreferrer">
                          <Stack direction="row" spacing={1} alignItems="center">
                            <YoutubeOutlined style={{ fontSize: '24px', color: '#ff0000' }} />
                            <Typography>YouTube</Typography>
                          </Stack>
                        </Link>
                      </Grid>
                    )}
                    {ngoDetails?.instagram_url && (
                      <Grid item xs={12} sm={6} md={3}>
                        <Link href={ngoDetails.instagram_url} target="_blank" rel="noopener noreferrer">
                          <Stack direction="row" spacing={1} alignItems="center">
                            <InstagramOutlined style={{ fontSize: '24px', color: '#c13584' }} />
                            <Typography>Instagram</Typography>
                          </Stack>
                        </Link>
                      </Grid>
                    )}
                  </Grid>
                </ListItem>
              </List>
            </MainCard>
          </Grid>
          <Grid container sx={{ display: 'flex', justifyContent: 'flex-end', alignItems: 'flex-end', gap: 2, marginTop: 2 }}>
            <Button
              variant="contained"
              color="primary"
              //   onClick={() => {
              //     if (!newNgoId && ngoDetails?.ngo_status === 'Verified') {
              //       toast.warn("Your profile is locked since it's verified. To make changes, contact the administrator.");
              //     } else {
              //       navigate(newNgoId ? `/masters/ngos/edit/basic/${newNgoId}` : '/apps/profiles/account/basic');
              //     }
              //   }}
            >
              Next
            </Button>
          </Grid>

          {/* <Grid item xs={12}>
            <MainCard title="Education">
              <List sx={{ py: 0 }}>
                <ListItem divider>
                  <Grid container spacing={matchDownMD ? 0.5 : 3}>
                    <Grid item xs={12} md={6}>
                      <Stack spacing={0.5}>
                        <Typography color="secondary">Master Degree (Year)</Typography>
                        <Typography>2014-2017</Typography>
                      </Stack>
                    </Grid>
                    <Grid item xs={12} md={6}>
                      <Stack spacing={0.5}>
                        <Typography color="secondary">Institute</Typography>
                        <Typography>-</Typography>
                      </Stack>
                    </Grid>
                  </Grid>
                </ListItem>
                <ListItem divider>
                  <Grid container spacing={matchDownMD ? 0.5 : 3}>
                    <Grid item xs={12} md={6}>
                      <Stack spacing={0.5}>
                        <Typography color="secondary">Bachelor (Year)</Typography>
                        <Typography>2011-2013</Typography>
                      </Stack>
                    </Grid>
                    <Grid item xs={12} md={6}>
                      <Stack spacing={0.5}>
                        <Typography color="secondary">Institute</Typography>
                        <Typography>Imperial College London</Typography>
                      </Stack>
                    </Grid>
                  </Grid>
                </ListItem>
                <ListItem>
                  <Grid container spacing={matchDownMD ? 0.5 : 3}>
                    <Grid item xs={12} md={6}>
                      <Stack spacing={0.5}>
                        <Typography color="secondary">School (Year)</Typography>
                        <Typography>2009-2011</Typography>
                      </Stack>
                    </Grid>
                    <Grid item xs={12} md={6}>
                      <Stack spacing={0.5}>
                        <Typography color="secondary">Institute</Typography>
                        <Typography>School of London, England</Typography>
                      </Stack>
                    </Grid>
                  </Grid>
                </ListItem>
              </List>
            </MainCard>
          </Grid>
          <Grid item xs={12}>
            <MainCard title="Emplyment">
              <List sx={{ py: 0 }}>
                <ListItem divider>
                  <Grid container spacing={matchDownMD ? 0.5 : 3}>
                    <Grid item xs={12} md={6}>
                      <Stack spacing={0.5}>
                        <Typography color="secondary">Senior</Typography>
                        <Typography color="secondary">Senior UI/UX designer (Year)</Typography>
                      </Stack>
                    </Grid>
                    <Grid item xs={12} md={6}>
                      <Stack spacing={0.5}>
                        <Typography color="secondary">Job Responsibility</Typography>
                        <Typography>
                          Perform task related to project manager with the 100+ team under my observation. Team management is key role in
                          this company.
                        </Typography>
                      </Stack>
                    </Grid>
                  </Grid>
                </ListItem>
                <ListItem>
                  <Grid container spacing={matchDownMD ? 0.5 : 3}>
                    <Grid item xs={12} md={6}>
                      <Stack spacing={0.5}>
                        <Typography color="secondary">Trainee cum Project Manager (Year)</Typography>
                        <Typography>2017-2019</Typography>
                      </Stack>
                    </Grid>
                    <Grid item xs={12} md={6}>
                      <Stack spacing={0.5}>
                        <Typography color="secondary">Job Responsibility</Typography>
                        <Typography>Team management is key role in this company.</Typography>
                      </Stack>
                    </Grid>
                  </Grid>
                </ListItem>
              </List>
            </MainCard>
          </Grid> */}
        </Grid>
      </Grid>
      {/* {!newNgoId && ngoDetails && (ngoDetails.ngo_status === 'Pending' || ngoDetails.ngo_status === 'New') && (
        <Grid item xs={12}>
          <Button onClick={handleSubmitReview} variant="contained" color="primary" sx={{ mr: 2 }}>
            Submit for Review
          </Button>
        </Grid>
      )} */}
      <ToastContainer autoClose={6000} />
    </Grid>
  );
};

export default TabNgoProfile;
