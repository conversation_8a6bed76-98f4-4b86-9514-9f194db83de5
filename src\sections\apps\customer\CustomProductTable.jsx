import PropTypes from 'prop-types';
import { Fragment, useEffect, useMemo, useState } from 'react';

// material-ui
import { alpha, useTheme } from '@mui/material/styles';
import useMediaQuery from '@mui/material/useMediaQuery';
import Button from '@mui/material/Button';
import Divider from '@mui/material/Divider';
import MenuItem from '@mui/material/MenuItem';
import Select from '@mui/material/Select';
import Stack from '@mui/material/Stack';
import Table from '@mui/material/Table';
import TableBody from '@mui/material/TableBody';
import TableCell from '@mui/material/TableCell';
import TableContainer from '@mui/material/TableContainer';
import TableHead from '@mui/material/TableHead';
import TableRow from '@mui/material/TableRow';
import Box from '@mui/material/Box';

// third-party
import {
  flexRender,
  getCoreRowModel,
  getSortedRowModel,
  getPaginationRowModel,
  getFilteredRowModel,
  getExpandedRowModel,
  useReactTable
} from '@tanstack/react-table';
import { rankItem } from '@tanstack/match-sorter-utils';

// project-import
import ScrollX from 'components/ScrollX';
import MainCard from 'components/MainCard';

import {
  CSVExport,
  DebouncedInput,
  HeaderSort,
  RowSelection,
  SelectColumnSorting,
  SelectColumnVisibility,
  TablePagination
} from 'components/third-party/react-table';


// assets
import PlusOutlined from '@ant-design/icons/PlusOutlined';

import { CardMedia, CircularProgress, Typography } from '@mui/material';
import { BASE_URL } from '../profiles/profile.service';
import useAuth from 'hooks/useAuth';
import SearchWithButton from 'components/third-party/react-table/SearchWithButton';

export const fuzzyFilter = (row, columnId, value, addMeta) => {
  // rank the item
  const itemRank = rankItem(row.getValue(columnId), value);

  // store the ranking info
  addMeta(itemRank);

  // return if the item should be filtered in/out
  return itemRank.passed;
};

// ==============================|| REACT TABLE - LIST ||============================== //

export default function CustomProductTable({
  data,
  columns,
  modalToggler,
  setRowSelection,
  rowSelection,
  category,
  totalCount,
  currentPage,
  pageSize,
  onPageChange,
  onPageSizeChange,
  globalFilter,
  setGlobalFilter
}) {
  const theme = useTheme();
  const downSM = useMediaQuery(theme.breakpoints.down('sm'));
  const { user } = useAuth();
  const initialColumnVisibility = useMemo(() => {
    const visibility = {};
    columns.forEach((column) => {
      visibility[column.accessorKey] = column.showByDefault !== false;
    });
    return visibility;
  }, [columns]);
  const [sorting, setSorting] = useState([
    {
      id: 'id',
      desc: true
    }
  ]);
  const [columnVisibility, setColumnVisibility] = useState(initialColumnVisibility);
  const [searchFilteredData, setFilteredData] = useState(data);
  const [isLoading, setIsLoading] = useState(false);

  const table = useReactTable({
    data: searchFilteredData,
    columns,
    state: {
      sorting,
      rowSelection,
      globalFilter,
      columnVisibility
    },
    initialState: {
      columnVisibility: initialColumnVisibility
    },
    enableRowSelection: true,
    getRowId: (row) => row.id,
    onSortingChange: setSorting,
    onRowSelectionChange: setRowSelection,
    onGlobalFilterChange: setGlobalFilter,
    getRowCanExpand: () => true,
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    getCoreRowModel: getCoreRowModel(),
    // getPaginationRowModel: getPaginationRowModel(),
    ...(category !== 'Product' && { getPaginationRowModel: getPaginationRowModel() }),
    getExpandedRowModel: getExpandedRowModel(),
    globalFilterFn: fuzzyFilter,
    debugTable: true,
    onColumnVisibilityChange: setColumnVisibility
  });

  const backColor = alpha(theme.palette.primary.lighter, 0.1);
  let headers = [];
  columns.map(
    (columns) =>
      // @ts-ignore
      columns.accessorKey &&
      headers.push({
        label: typeof columns.header === 'string' ? columns.header : '#',
        // @ts-ignore
        key: columns.accessorKey
      })
  );

  const handlePageChange = (newPage) => {
    onPageChange(newPage + 1);
  };

  const handlePageSizeChange = (newPageSize) => {
    onPageSizeChange(newPageSize);
  };
  return (
    <MainCard content={false}>
      <Stack
        direction={{ xs: 'column', sm: 'row' }}
        spacing={2}
        alignItems="center"
        justifyContent="space-between"
        sx={{ padding: 2, ...(downSM && { '& .MuiOutlinedInput-root, & .MuiFormControl-root': { width: '100%' } }) }}
      >
        <Stack direction={{ xs: 'column', sm: 'row' }} spacing={2} alignItems="center" sx={{ width: { xs: '100%', sm: 'auto' } }}>
          <SearchWithButton value={globalFilter ?? ''} onFilterChange={(value) => setGlobalFilter(String(value))} placeholder="Search" />

          <SelectColumnVisibility
            {...{
              getVisibleLeafColumns: table.getVisibleLeafColumns,
              getIsAllColumnsVisible: table.getIsAllColumnsVisible,
              getToggleAllColumnsVisibilityHandler: table.getToggleAllColumnsVisibilityHandler,
              getAllColumns: table.getAllColumns
            }}
          />
        </Stack>
        {/* {user?.ngo_id && (
          <Stack direction="row" spacing={2} alignItems="center">
            <Button variant="contained" color="primary" size="small" startIcon={<PlusOutlined />} onClick={modalToggler}>
              Add Product
            </Button>
          </Stack>
        )} */}
        {(category == 'Collection' || category == 'Slide') && (
          <Stack direction="row" spacing={2} alignItems="center">
            <Button variant="contained" color="primary" size="small" startIcon={<PlusOutlined />} onClick={modalToggler}>
              {`Add ${category}`}
            </Button>
          </Stack>
        )}
      </Stack>
      <ScrollX>
        <Stack>
          <TableContainer>
            {/* <Table size="small" {...{ style: { width: table.getCenterTotalSize() } }}> */}
            <Table size="small" style={category != 'Product' ? { width: '100%' } : { width: table.getCenterTotalSize() }}>
              <TableHead>
                {table.getHeaderGroups().map((headerGroup) => (
                  <TableRow key={headerGroup.id}>
                    {headerGroup.headers.map((header) => {
                      if (header.column.columnDef.meta !== undefined && header.column.getCanSort()) {
                        Object.assign(header.column.columnDef.meta, {
                          className: header.column.columnDef.meta.className + ' cursor-pointer prevent-select'
                        });
                      }

                      return (
                        <TableCell
                          key={header.id}
                          {...header.column.columnDef.meta}
                          onClick={header.column.getToggleSortingHandler()}
                          {...(header.column.getCanSort() &&
                            header.column.columnDef.meta === undefined && {
                              className: 'cursor-pointer prevent-select'
                            })}
                          style={{ textTransform: 'none' }}
                        >
                          {header.isPlaceholder ? null : (
                            <Stack direction="row" spacing={1} alignItems="center">
                              <Box>{flexRender(header.column.columnDef.header, header.getContext())}</Box>
                              {header.column.getCanSort() && <HeaderSort column={header.column} />}
                            </Stack>
                          )}
                        </TableCell>
                      );
                    })}
                  </TableRow>
                ))}
              </TableHead>
              <TableBody>
                {isLoading ? (
                  <TableRow>
                    <TableCell colSpan={columns.length + 1}>
                      <Stack direction="row" spacing={2} alignItems="center">
                        <CircularProgress size={20} />
                        <Typography>Loading your data...</Typography>
                      </Stack>
                    </TableCell>
                  </TableRow>
                ) : table.getRowModel().rows.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={columns.length + 1} align="center">
                      <Typography variant="body2" color="text.secondary">
                        No data found
                      </Typography>
                    </TableCell>
                  </TableRow>
                ) : (
                  table.getRowModel().rows.map((row) => (
                    <TableRow key={row.id}>
                      <TableCell
                        sx={{
                          width: '150px',
                          height: '150px',
                          padding: 2
                        }}
                      >
                        <CardMedia
                          component="img"
                          alt={row.original.imageName}
                          image={`${BASE_URL}/${
                            category === 'Product'
                              ? 'fetchProductImages'
                              : category === 'Slide'
                                ? 'fetchSlideImages'
                                : 'fetchCollectionImages'
                          }/${row.original.imageName}`}
                          style={{ objectFit: 'fill' }}
                        />
                      </TableCell>
                      {row.getVisibleCells().map((cell, index) => {
                        if (index === 0) return null;
                        return (
                          <TableCell key={cell.id} {...(cell.column.columnDef.meta ?? {})}>
                            {flexRender(cell.column.columnDef.cell, cell.getContext())}
                          </TableCell>
                        );
                      })}
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </TableContainer>
          <>
            <Divider />
            <Box sx={{ p: 2 }}>
              {category == 'Product' ? (
                <TablePagination
                  getPageCount={() => Math.ceil(totalCount / pageSize)}
                  setPageIndex={handlePageChange}
                  setPageSize={handlePageSizeChange}
                  getState={() => ({ pagination: { pageIndex: currentPage - 1, pageSize } })}
                  initialPageSize={pageSize}
                />
              ) : (
                <TablePagination
                  {...{
                    setPageSize: table.setPageSize,
                    setPageIndex: table.setPageIndex,
                    getState: table.getState,
                    getPageCount: table.getPageCount
                  }}
                />
              )}
            </Box>
          </>
        </Stack>
      </ScrollX>
    </MainCard>
  );
}

CustomProductTable.propTypes = {
  data: PropTypes.array,
  columns: PropTypes.array,
  modalToggler: PropTypes.func,
  category: PropTypes.string,
  setRowSelection: PropTypes.func,
  rowSelection: PropTypes.object,
  openAssignDialog: PropTypes.func,
  openFeaturedDialog: PropTypes.func,
  openAssignGradeDialog: PropTypes.func,
  tabValue: PropTypes.number,
  roleName: PropTypes.string,
  totalCount: PropTypes.number,
  currentPage: PropTypes.number,
  pageSize: PropTypes.number,
  onPageChange: PropTypes.func,
  onPageSizeChange: PropTypes.func
};
