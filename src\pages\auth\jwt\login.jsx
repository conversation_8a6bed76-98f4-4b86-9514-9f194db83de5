import { Link, useSearchParams } from 'react-router-dom';
import { useState } from 'react';

// material-ui
import Grid from '@mui/material/Grid';
import Stack from '@mui/material/Stack';
import Typography from '@mui/material/Typography';
import Box from '@mui/material/Box';

// project import
import useAuth from 'hooks/useAuth';
import AuthWrapper from 'sections/auth/AuthWrapper';
import AuthLogin from 'sections/auth/jwt/AuthLogin';
import { useTheme } from '@mui/material';
import ImageSlider from 'components/ImageSlider';

export default function Login() {
  const { isLoggedIn } = useAuth();
  const [searchParams] = useSearchParams();
  const auth = searchParams.get('auth');
  const theme = useTheme();

  return (
    <Box sx={{ display: 'flex', minHeight: '100vh' }}>
      {/* Left side - Image Slider */}
      <ImageSlider />

      {/* Right side - Login Form */}
      <Box sx={{ flex: 1, display: 'flex', flexDirection: 'column', bgcolor: theme.palette.primary.main }}>
        <AuthWrapper>
          <Grid container spacing={3}>
            <Grid item xs={12}>
              <Stack direction="row" justifyContent="space-between" alignItems="baseline" sx={{ mb: { xs: -0.5, sm: 0.5 } }}>
                <Typography variant="h3" color="#fff">
                  Sign in
                </Typography>
                <Typography
                  component={Link}
                  to={isLoggedIn ? '/auth/register' : auth ? `/${auth}/register?auth=jwt` : '/register'}
                  variant="body1"
                  sx={{ textDecoration: 'none' }}
                  color="#fff"
                >
                  Not Registered? Create an Account
                </Typography>
              </Stack>
            </Grid>
            <Grid item xs={12}>
              <AuthLogin isDemo={isLoggedIn} />
            </Grid>
          </Grid>
        </AuthWrapper>
      </Box>
    </Box>
  );
}
