// third-party
import { FormattedMessage } from 'react-intl';

// project-imports
import { handlerCustomerDialog } from 'api/customer';
import { NavActionType } from 'config';

// assets
import BuildOutlined from '@ant-design/icons/BuildOutlined';
import CalendarOutlined from '@ant-design/icons/CalendarOutlined';
import CustomerServiceOutlined from '@ant-design/icons/CustomerServiceOutlined';
import FileTextOutlined from '@ant-design/icons/FileTextOutlined';
import MessageOutlined from '@ant-design/icons/MessageOutlined';
import ShoppingCartOutlined from '@ant-design/icons/ShoppingCartOutlined';
import UserOutlined from '@ant-design/icons/UserOutlined';
import AppstoreAddOutlined from '@ant-design/icons/AppstoreAddOutlined';
import PlusOutlined from '@ant-design/icons/PlusOutlined';
import LinkOutlined from '@ant-design/icons/LinkOutlined';
import { useEffect } from 'react';
import { AuditOutlined, ImportOutlined, PictureOutlined, UserAddOutlined } from '@ant-design/icons';
import { getSessionStorageItem } from 'utils/permissionUtils';

// type

// icons
const icons = {
  BuildOutlined,
  CalendarOutlined,
  CustomerServiceOutlined,
  MessageOutlined,
  ShoppingCartOutlined,
  UserOutlined,
  AppstoreAddOutlined,
  FileTextOutlined,
  PlusOutlined,
  PictureOutlined,
  LinkOutlined,
  ImportOutlined
};

//retrieve from local storage
const user = getSessionStorageItem('user');

// ==============================|| MENU ITEMS - MASTERS ||============================== //

const allChannelItems = [
  {
    id: 'themes',
    title: <FormattedMessage id="themes" />,

    type: 'collapse',
    url: '/channels/themes',
    icon: AppstoreAddOutlined,
    breadcrumbs: true,
    children: [
      {
        id: 'cover-images',
        title: (
          <>
            <FormattedMessage id="cover-images" />
          </>
        ),
        type: 'item',
        url: '/channels/cover-images',
        caption: <FormattedMessage id="cover-images" />,
        icon: PictureOutlined,
        breadcrumbs: true
      },
      {
        id: 'impact-areas',
        title: (
          <>
            <FormattedMessage id="impact-areas" />
          </>
        ),
        type: 'item',
        url: '/channels/impact-areas',
        caption: <FormattedMessage id="impact-areas" />,
        icon: ImportOutlined,
        breadcrumbs: true
      },
      {
        id: 'gallery-images',
        title: <FormattedMessage id="gallery-images" />,
        type: 'item',
        caption: <FormattedMessage id="gallery-images" />,
        icon: PictureOutlined,
        url: '/channels/gallery-images',
        breadcrumbs: true
      }
    ]
  }
];

const channel = {
  id: 'channel',
  title: <FormattedMessage id="channel" />,
  icon: AppstoreAddOutlined,
  type: 'group',
  children: allChannelItems // Use filtered items here
};

export default channel;
