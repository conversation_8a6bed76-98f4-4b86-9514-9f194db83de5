// third-party
import { FormattedMessage } from 'react-intl';

// project-imports
import { handlerCustomerDialog } from 'api/customer';
import { NavActionType } from 'config';

// assets
import BuildOutlined from '@ant-design/icons/BuildOutlined';
import CalendarOutlined from '@ant-design/icons/CalendarOutlined';
import CustomerServiceOutlined from '@ant-design/icons/CustomerServiceOutlined';
import FileTextOutlined from '@ant-design/icons/FileTextOutlined';
import MessageOutlined from '@ant-design/icons/MessageOutlined';
import ShoppingCartOutlined from '@ant-design/icons/ShoppingCartOutlined';
import UserOutlined from '@ant-design/icons/UserOutlined';
import AppstoreAddOutlined from '@ant-design/icons/AppstoreAddOutlined';
import PlusOutlined from '@ant-design/icons/PlusOutlined';
import LinkOutlined from '@ant-design/icons/LinkOutlined';
import { useEffect } from 'react';
import { getSessionStorageItem } from 'utils/permissionUtils';
import {
  AuditOutlined,
  ContainerOutlined,
  FundProjectionScreenOutlined,
  MailOutlined,
  QuestionCircleOutlined,
  SecurityScanOutlined,
  SettingOutlined,
  TransactionOutlined,
  TruckOutlined
} from '@ant-design/icons';

// type

// icons
const icons = {
  BuildOutlined,
  CalendarOutlined,
  CustomerServiceOutlined,
  MessageOutlined,
  ShoppingCartOutlined,
  UserOutlined,
  AppstoreAddOutlined,
  FileTextOutlined,
  PlusOutlined,
  LinkOutlined,
  SecurityScanOutlined,
  MailOutlined
};

//retrieve from local storage
const user = getSessionStorageItem('user');
const sidebarPermissions = user?.roleInfo && user?.roleInfo?.permissions ? user?.roleInfo?.permissions?.ShowSidebar : [];

// ==============================|| MENU ITEMS - MASTERS ||============================== //

const allMasterItems = [
  {
    id: 'Categories',
    title: <FormattedMessage id="Categories" />,
    type: 'collapse',
    url: '/masters/categories',
    icon: MessageOutlined,
    breadcrumbs: true,
    children: [
      {
        id: 'SubCategories', // Nested under Categories
        title: (
          <>
            <FormattedMessage id="SubCategories" />
          </>
        ),
        type: 'item',
        url: '/masters/sub-categories',
        caption: <FormattedMessage id="SubCategories" />,
        icon: MessageOutlined,
        breadcrumbs: true
      }
    ]
  },
  {
    id: 'Skills',
    title: <FormattedMessage id="Skills" />,
    type: 'item',
    icon: SettingOutlined,
    url: '/masters/skills',
    breadcrumbs: true
  },
  {
    id: 'Slides',
    title: <FormattedMessage id="Slides" />,
    type: 'item',
    icon: FundProjectionScreenOutlined,
    url: '/masters/slides',
    breadcrumbs: true
  },
  {
    id: 'Transactions',
    title: <FormattedMessage id="Transactions" />,
    type: 'item',
    icon: TransactionOutlined,
    url: '/masters/transactions',
    breadcrumbs: true
  },
  {
    id: 'NewsLetters',
    title: <FormattedMessage id="NewsLetters" />,
    type: 'item',
    icon: AuditOutlined,
    url: '/masters/newsletters',
    breadcrumbs: true
  },
  {
    id: 'Collections',
    title: <FormattedMessage id="Collections" />,
    type: 'item',
    icon: ContainerOutlined,
    url: '/masters/collections',
    breadcrumbs: true
  },
  {
    id: 'Faqs',
    title: <FormattedMessage id="Faqs" />,
    type: 'item',
    icon: QuestionCircleOutlined,
    url: '/masters/faqs',
    breadcrumbs: true
  },
  {
    id: 'NgoTypes',
    title: <FormattedMessage id="NgoTypes" />,
    type: 'item',
    icon: BuildOutlined,
    url: '/masters/ngo-types',
    breadcrumbs: true
  },
  {
    id: 'Roles',
    title: <FormattedMessage id="Roles" />,
    type: 'item',
    url: '/masters/roles',
    icon: SecurityScanOutlined
  },
  {
    id: 'Items',
    title: <FormattedMessage id="Items" />,
    type: 'item',
    url: '/masters/items',
    icon: CalendarOutlined
  },
  {
    id: 'Document',
    title: <FormattedMessage id="Document" />,
    type: 'item',
    icon: FileTextOutlined,
    url: '/masters/documents',
    breadcrumbs: true
  },
  {
    id: 'Questions',
    title: <FormattedMessage id="Questions" />,
    type: 'item',
    icon: FileTextOutlined,
    url: '/masters/questions',
    breadcrumbs: true
  },
  {
    id: 'EmailTemplates',
    title: <FormattedMessage id="EmailTemplates" />,
    type: 'item',
    icon: MailOutlined,
    url: '/masters/emails',
    breadcrumbs: true
  },
  {
    id: 'themes',
    title: <FormattedMessage id="themes" />,
    type: 'item',
    url: '/channels/themes',
    icon: AppstoreAddOutlined,
    breadcrumbs: true
  },
  {
    id: 'Orders',
    title: <FormattedMessage id="Orders" />,
    type: 'item',
    icon: AuditOutlined,
    url: '/channels/orders',
    breadcrumbs: true
  },

];

// Filter it
const filteredMasterItems = allMasterItems.filter((item) => {
  if (sidebarPermissions[item.id]) {
    if (item.children) {
      item.children = item.children.filter((child) => sidebarPermissions[child.id]);
    }
    return true;
  }
  return false;
});
// Master menu configuration with filtered items
const masters = {
  id: 'masters',
  title: <FormattedMessage id="masters" />,
  icon: AppstoreAddOutlined,
  type: 'group',
  children: filteredMasterItems // Use filtered items here
};

export default masters;
