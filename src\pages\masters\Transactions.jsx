import React, { useEffect, useState } from 'react';
import { Button } from '@mui/material';
import { toast, ToastContainer } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import CustomerTable from 'sections/apps/customer/CustomerTable';
import { getUserPermissions } from 'utils/permissionUtils';
import useAuth from 'hooks/useAuth';
import { fetchTransactionsService, deleteTransactionService } from './apis/transaction.service';
import dayjs from 'dayjs';
import TableActions from 'components/TableActions';
import CustomerTableWithPagination from 'sections/apps/customer/CustomerTableWithPagination';

export default function TransactionsTable() {
  const { user } = useAuth();
  const [transactions, setTransactions] = useState([]);

  // Role-based access
  const permissions = getUserPermissions(user);
  const canDelete = permissions?.Delete?.Transactions || false;
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [totalCount, setTotalCount] = useState(0);

  useEffect(() => {
    fetchTransactions();
  }, [currentPage, pageSize]);

  const fetchTransactions = async () => {
    try {
      const { transactions, totalCount } = await fetchTransactionsService(user?.ngo_id, currentPage, pageSize);
      setTransactions(transactions);
      setTotalCount(totalCount);
    } catch (error) {
      console.error('Failed to fetch Transactions:', error);
      toast.error('Failed to fetch Transactions');
    }
  };

  const handleDelete = async (id) => {
    if (!canDelete) {
      toast.error('You do not have permission to delete Transactions.');
      return;
    }
    const confirmDelete = window.confirm('Are you sure you want to delete this Transaction?');
    if (confirmDelete) {
      try {
        await deleteTransactionService(id);
        toast.success('Transaction deleted successfully!');
        fetchTransactions();
      } catch (error) {
        console.error('Failed to delete Transaction:', error);
        toast.error('Failed to delete Transaction');
      }
    }
  };

  const columns = [
    { accessorKey: 'userInfo.fullname', header: 'User Name', showByDefault: true },
    { accessorKey: 'amount', header: 'Amount', showByDefault: true },
    { accessorKey: 'orderInfo.order_id', header: 'Order ID', showByDefault: true },
    { accessorKey: 'razorpay_payment_id', header: 'Razorpay Payment ID', showByDefault: true },
    { accessorKey: 'razorpay_signature', header: 'Razorpay Signature', showByDefault: true },
    { accessorKey: 'status', header: 'Status', showByDefault: true },
    { accessorKey: 'tracking_id', header: 'Tracking ID', showByDefault: true },
    { accessorKey: 'payment_mode', header: 'Payment Mode', showByDefault: true },
    { accessorKey: 'bank_ref_no', header: 'Bank Ref No.', showByDefault: true },
    { accessorKey: 'all_values', header: 'All Values', showByDefault: false },
    { accessorKey: 'transferResponse', header: 'Transfer Response', showByDefault: false },
    { accessorKey: 'payment_type', header: 'Payment Type', showByDefault: true },
    { accessorKey: 'donation_type', header: 'Donation Type', showByDefault: true },
    { accessorKey: 'ngoInfo.name', header: 'NGO Name', showByDefault: true },
    {
      accessorKey: 'campaignInfo.name',
      header: 'Campaign ID',
      showByDefault: true,
      cell: (cell) => <span>{cell.getValue() || 'N/A'}</span>
    },
    { accessorKey: 'description', header: 'Description', showByDefault: false },
    {
      accessorKey: 'createdAt',
      header: 'Created At',
      showByDefault: true,
      cell: (cell) => {
        return <span>{dayjs(cell.row.original.createdAt).format('DD-MM-YYYY hh:mm A')}</span>;
      }
    },
    {
      accessorKey: 'updatedAt',
      header: 'Updated At',
      showByDefault: true,
      cell: (cell) => {
        return <span>{dayjs(cell.row.original.updatedAt).format('DD-MM-YYYY hh:mm A')}</span>;
      }
    },
    {
      id: 'actions',
      header: 'Action',
      cell: (cell) => {
        return <TableActions showEdit={false} handleDeleteClick={handleDelete} cell={cell} />;
      }
    }
  ];
  const handlePageChange = (newPage) => {
    setCurrentPage(newPage);
  };

  const handlePageSizeChange = (newPageSize) => {
    setPageSize(newPageSize);
  };
  return (
    <div>
      <CustomerTableWithPagination
        data={transactions}
        columns={columns}
        category={'Transaction'}
        currentPage={currentPage}
        totalCount={totalCount}
        pageSize={pageSize}
        onPageChange={handlePageChange}
        onPageSizeChange={handlePageSizeChange}
      />
      <ToastContainer />
    </div>
  );
}
