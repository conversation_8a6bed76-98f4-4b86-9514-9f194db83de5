import axiosServices from 'utils/axios_node';

export const API_BASE_URL = `${import.meta.env.VITE_APP_APPLICATION_API_URL}/api`;
const sessionToken = '0af7b610-9cae-42a1-892e-c6a1df102ec5';
const accessToken = 'pk.eyJ1IjoiYmh1c2hhbjY1NiIsImEiOiJjbTJ6cXA4OGgwZHIxMm1xMm81aDNyazE1In0.Zm3xPsoxU9RNU9t468gX9Q';

export const fetchSuggestions = async (query) => {
  const response = await axiosServices.get(`https://api.mapbox.com/search/searchbox/v1/suggest`, {
    params: {
      q: query,
      language: 'en',
      country: 'in',
      proximity: '-73.990593,40.740121',
      session_token: sessionToken,
      access_token: accessToken
    }
  });
  return response.data.suggestions;
};

export const fetchLocationDetails = async (mapboxId) => {
  const response = await axiosServices.get(`https://api.mapbox.com/search/searchbox/v1/retrieve/${mapboxId}`, {
    params: {
      session_token: sessionToken,
      access_token: accessToken
    }
  });
  return response.data.features[0];
};

export const getDocuments = async () => {
  const response = await axiosServices.get(`${API_BASE_URL}/documents`);
  return response.data;
};

export const uploadDocuments = async (data, headers) => {
  const response = await axiosServices.post(`${API_BASE_URL}documents/upload`, data, headers);
  return response.data;
};

export const deleteDocument = async (filename) => {
  const response = await axiosServices.delete(`${API_BASE_URL}documents/${filename}`);
  return response.data;
};

export const getAllBankDetails = async (ngo_id) => {
  const url = ngo_id ? `${API_BASE_URL}/bank-details?ngoId=${ngo_id}` : `${API_BASE_URL}/bank-details`;

  const response = await axiosServices.get(url);
  return response.data;
};
export const addBankDetails = async (payload) => {
  const response = await axiosServices.post(`${API_BASE_URL}/bank-details`, payload);
  return response.data;
};
export const updateBankDetails = async (id, payload) => {
  const response = await axiosServices.put(`${API_BASE_URL}/bank-details/${id}`, payload);
  return response.data;
};
export const deleteBankDetails = async (id) => {
  const response = await axiosServices.delete(`${API_BASE_URL}/bank-details/${id}`);
  return response.data;
};

export const addProfileQueries = async (payload) => {
  const response = await axiosServices.post(`${API_BASE_URL}/profile-queries`, payload);
  return response.data;
};

export const getProfileQueries = async (ngo_id, userId, type) => {
  const params = new URLSearchParams();

  if (ngo_id) params.append('ngoId', ngo_id);
  if (userId) params.append('userId', userId);
  if (type) params.append('type', type);

  const url = `${API_BASE_URL}/profile-queries${params.toString() ? `?${params.toString()}` : ''}`;

  const response = await axiosServices.get(url);
  return response.data;
};

export const getAssineeBasedProfileQueries = async (userId) => {
  const url = userId ? `${API_BASE_URL}/profile-queries?userId=${userId}` : `${API_BASE_URL}/profile-queries`;

  const response = await axiosServices.get(url);
  return response.data;
};

export const updateProfileQueries = async (id, payload) => {
  const response = await axiosServices.put(`${API_BASE_URL}/profile-queries/${id}`, payload);
  return response.data;
};
export const sendVerificationEmail = async (email) => {
  const response = await axiosServices.get(`${API_BASE_URL}/portal-users/sendVerificationEmail?email=${email}`);
  return response.data;
};
export const sendverificationNumber = async (contact_number) => {
  const response = await axiosServices.post(`${API_BASE_URL}/users/sendVerificationOTP?mobile_number=${contact_number}`);
  return response.data;
};
export const sendverificationNumberForReg = async (contact_number) => {
  const response = await axiosServices.post(`${API_BASE_URL}/temp-users/sendVerificationOTP?mobile_number=${contact_number}`);
  return response.data;
};
export const verifyOtpReg = async (mobile_number, otp) => {
  const response = await axiosServices.post(`${API_BASE_URL}/temp-users/verifyOtp`, {
    mobile_number: mobile_number,
    otp: otp
  });
  return response.data;
};
export const verifyPan = async (pan, mobile_number) => {
  const response = await axiosServices.post(`${API_BASE_URL}/temp-users/verifyPan`, {
    pan: pan,
    mobile_number: mobile_number
  });
  return response.data;
};

export const verifyEmail = async (token, email) => {
  const response = await axiosServices.get(`${API_BASE_URL}/portal-users/verifyEmail?token=${token}&email=${email}`);
  return response.data;
};
export const verifyOtp = async (mobile_number, otp) => {
  const response = await axiosServices.post(`${API_BASE_URL}/users/verifyOtpForNgo`, {
    mobile_number: mobile_number,
    otp: otp
  });
  return response.data;
};
