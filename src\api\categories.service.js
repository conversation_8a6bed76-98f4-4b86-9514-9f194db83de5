import axiosServices from 'utils/axios_node';

export const API_BASE_URL = `${import.meta.env.VITE_APP_APPLICATION_API_URL}/api`;

export const getAllCategories = async (pageName = '') => {
  const response = await axiosServices.get(`${API_BASE_URL}/categories?pageName=${pageName}`);
  return response?.data;
};
export const getSubcategoriesByCategoryId = async (categoryId) => {
  const response = await axiosServices.get(`${API_BASE_URL}/sub-categories?category_id=${categoryId}`);
  return response?.data;
};

export const getNGOBasedCategories = async (ngoId, pageName = '') => {
  const response = await axiosServices.get(`${API_BASE_URL}/ngo-category/ngos/${ngoId}?pageName=${pageName}`);
  return response?.data;
};

export const addCategory = async (category) => {
  const response = await axiosServices.post(`${API_BASE_URL}/categories`, category);
  return response?.data;
};

export const updateCategory = async (id, category) => {
  const response = await axiosServices.put(`${API_BASE_URL}/categories/${id}`, category);
  return response?.data;
};

export const deleteCategory = async (id) => {
  const response = await axiosServices.delete(`${API_BASE_URL}/categories/${id}`);
  return response?.data;
};
