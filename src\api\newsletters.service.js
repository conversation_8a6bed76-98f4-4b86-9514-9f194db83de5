// fetchNewslettersService, addNewsletterService, updateNewsletterService, deleteNewsletterService
import axiosServices from 'utils/axios_node';

const API_BASE_URL = `${import.meta.env.VITE_APP_APPLICATION_API_URL}/api`;

export const fetchNewslettersService = async () => {
  const response = await axiosServices.get(`${API_BASE_URL}/newsletters`);
  return response.data;
};

export const addNewsletterService = async (newsletter) => {
  const response = await axiosServices.post(`${API_BASE_URL}/newsletters`, newsletter);
  return response.data;
};

export const updateNewsletterService = async (id, newsletter) => {
  const response = await axiosServices.put(`${API_BASE_URL}/newsletters/${id}`, newsletter);
  return response.data;
};

export const deleteNewsletterService = async (id) => {
  const response = await axiosServices.delete(`${API_BASE_URL}/newsletters/${id}`);
  return response.data;
};
