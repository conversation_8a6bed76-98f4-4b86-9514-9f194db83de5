import { lazy } from 'react';

// project import
import ErrorBoundary from './ErrorBoundary';
import Loadable from 'components/Loadable';
import DashboardLayout from 'layout/Dashboard';
import PagesLayout from 'layout/Pages';
import SimpleLayout from 'layout/Simple';

import { SimpleLayoutType } from 'config';
import { loader as productsLoader, productLoader } from 'api/products';
// import CampaignForm from 'pages/masters/campaigns/CampaignForm';
import TabProfile from 'sections/apps/profiles/account/TabProfile';
import TabOrganizationInfo from 'sections/apps/profiles/account/TabPersonal';
import NGOAddEditForm from 'pages/masters/ngos/NgoAddEditForm';

// render - dashboard
const DashboardDefault = Loadable(lazy(() => import('pages/dashboard/default')));
const DashboardAnalytics = Loadable(lazy(() => import('pages/dashboard/analytics')));

//masters
const CategoriesTable = Loadable(lazy(() => import('pages/masters/Categories')));
const SubCategoriesTable = Loadable(lazy(() => import('pages/masters/SubCategories')));
const Roles = Loadable(lazy(() => import('pages/masters/Roles')));
const Items = Loadable(lazy(() => import('pages/masters/Items')));
const Documents = Loadable(lazy(() => import('pages/masters/Documents')));
const Questions = Loadable(lazy(() => import('pages/masters/Questions')));
const AuditLogs = Loadable(lazy(() => import('pages/masters/AuditLogs')));
const ImageComponent = Loadable(lazy(() => import('pages/masters/components/ImageComponent')));
const Skills = Loadable(lazy(() => import('pages/masters/Skills')));
const Transactions = Loadable(lazy(() => import('pages/masters/Transactions')));
const Slides = Loadable(lazy(() => import('pages/masters/Slides')));
const Newsletters = Loadable(lazy(() => import('pages/masters/NewLetters')));
const Collections = Loadable(lazy(() => import('pages/masters/Collections')));
const Faqs = Loadable(lazy(() => import('pages/masters/Faqs')));
const Emails = Loadable(lazy(() => import('pages/masters/email-template/EmailTemplate')));
const EmailsForm = Loadable(lazy(() => import('pages/masters/email-template/EmailTemplateForm')));
const ProfileQueries = Loadable(lazy(() => import('sections/apps/profiles/account/TabProfileQueries')));
const FeedbackandBugs = Loadable(lazy(() => import('pages/masters/FeedBackBugs')));
const Campaigns = Loadable(lazy(() => import('pages/masters/campaigns/components/NewCampaign')));
const StaffMembers = Loadable(lazy(() => import('pages/masters/StaffMemebrs')));
const ViewJournals = Loadable(lazy(() => import('pages/masters/campaigns/components/ViewJournals')));
const NGOS = Loadable(lazy(() => import('pages/masters/ngos/Ngos')));
const Users = Loadable(lazy(() => import('pages/masters/Users')));
const Products = Loadable(lazy(() => import('pages/masters/products/NewProducts')));
const ProductsForm = Loadable(lazy(() => import('pages/masters/products/ProductForm')));
const ProductPreview = Loadable(lazy(() => import('pages/masters/campaigns/components/ProductPreview')));
const ProductPreviewAddEdit = Loadable(lazy(() => import('pages/masters/products/ProductAddEditPreviewForm')));
const VerifyEmail = Loadable(lazy(() => import('pages/masters/components/VerifyEmail')));

//campaignd
const InitiativeSelector = Loadable(lazy(() => import('pages/masters/campaigns/components/InitiativeSelector')));
const CampaignForm = Loadable(lazy(() => import('pages/masters/campaigns/components/NewCampaignForm')));
const EventForm = Loadable(lazy(() => import('pages/masters/campaigns/components/EventForm')));

//channels
const ThemesCard = Loadable(lazy(() => import('pages/channels/ThemesCard')));
const Buckets = Loadable(lazy(() => import('pages/channels/Buckets')));
const BucketForm = Loadable(lazy(() => import('pages/channels/BucketForm')));
const CoverImages = Loadable(lazy(() => import('pages/channels/CoverImages')));
const ImpactAreas = Loadable(lazy(() => import('pages/channels/ImpactAreas')));
const Orders = Loadable(lazy(() => import('pages/channels/Orders')));
const SendNotification = Loadable(lazy(() => import('pages/channels/SendNotification')));
const NgoTypes = Loadable(lazy(() => import('pages/masters/NgoTypes')));

const CampaignDashboard = Loadable(lazy(() => import('pages/masters/campaigns/components/CampaignDashboard/CampaignDashboard')));

// render - widget
const WidgetStatistics = Loadable(lazy(() => import('pages/widget/statistics')));
const NgoSearchPage = Loadable(lazy(() => import('pages/masters/components/NgoSearchPage')));
const WidgetData = Loadable(lazy(() => import('pages/widget/data')));
const WidgetChart = Loadable(lazy(() => import('pages/widget/chart')));

// render - applications
const AppChat = Loadable(lazy(() => import('pages/apps/chat')));
const AppCalendar = Loadable(lazy(() => import('pages/apps/calendar')));

const AppKanban = Loadable(lazy(() => import('pages/apps/kanban')));
const AppKanbanBacklogs = Loadable(lazy(() => import('sections/apps/kanban/Backlogs')));
const AppKanbanBoard = Loadable(lazy(() => import('sections/apps/kanban/Board')));

const AppCustomerList = Loadable(lazy(() => import('pages/apps/customer/list')));
const AppCustomerCard = Loadable(lazy(() => import('pages/apps/customer/card')));

const AppInvoiceCreate = Loadable(lazy(() => import('pages/apps/invoice/create')));
const AppInvoiceDashboard = Loadable(lazy(() => import('pages/apps/invoice/dashboard')));
const AppInvoiceList = Loadable(lazy(() => import('pages/apps/invoice/list')));
const AppInvoiceDetails = Loadable(lazy(() => import('pages/apps/invoice/details')));
const AppInvoiceEdit = Loadable(lazy(() => import('pages/apps/invoice/edit')));

const UserProfile = Loadable(lazy(() => import('pages/apps/profiles/user')));
const UserTabPersonal = Loadable(lazy(() => import('sections/apps/profiles/user/TabPersonal')));
const UserTabPayment = Loadable(lazy(() => import('sections/apps/profiles/user/TabPayment')));
const UserTabPassword = Loadable(lazy(() => import('sections/apps/profiles/user/TabPassword')));
const UserTabSettings = Loadable(lazy(() => import('sections/apps/profiles/user/TabSettings')));

const AccountProfile = Loadable(lazy(() => import('pages/apps/profiles/account')));
const AccountNgos = Loadable(lazy(() => import('pages/apps/profiles/ngoTabs')));
const AccountTabProfile = Loadable(lazy(() => import('sections/apps/profiles/account/TabProfile')));
const AccountTabPersonal = Loadable(lazy(() => import('sections/apps/profiles/account/TabPersonal')));
const AccountTabAccount = Loadable(lazy(() => import('sections/apps/profiles/account/TabAccount')));
const AccountTabPassword = Loadable(lazy(() => import('sections/apps/profiles/account/TabPassword')));
const AccountTabRole = Loadable(lazy(() => import('sections/apps/profiles/account/TabRole')));
const AccountTabPreview = Loadable(lazy(() => import('sections/apps/profiles/account/TabPreview')));
const AccountAdditionalInfo = Loadable(lazy(() => import('sections/apps/profiles/account/TabAdditionalInfo')));
const AccountBankDetails = Loadable(lazy(() => import('sections/apps/profiles/account/TabBankDetails')));
const AccountProfileQueries = Loadable(lazy(() => import('sections/apps/profiles/account/TabProfileQueries')));
const AccountTabSettings = Loadable(lazy(() => import('sections/apps/profiles/account/TabSettings')));
const AccountTabDocuments = Loadable(lazy(() => import('sections/apps/profiles/account/TabDocuments')));
const AccountTabAddCommnets = Loadable(lazy(() => import('sections/apps/profiles/account/TabAddCommnets')));

const AccountTabNGOProfile = Loadable(lazy(() => import('sections/apps/profiles/account/TabNgoProfile')));

const AppECommProducts = Loadable(lazy(() => import('pages/apps/e-commerce/products')));
const AppECommProductDetails = Loadable(lazy(() => import('pages/apps/e-commerce/product-details')));
const AppECommProductList = Loadable(lazy(() => import('pages/apps/e-commerce/products-list')));
const AppECommCheckout = Loadable(lazy(() => import('pages/apps/e-commerce/checkout')));
const AppECommAddProduct = Loadable(lazy(() => import('pages/apps/e-commerce/add-product')));

// render - forms & tables
const FormsValidation = Loadable(lazy(() => import('pages/forms/validation')));
const FormsWizard = Loadable(lazy(() => import('pages/forms/wizard')));

const FormsLayoutBasic = Loadable(lazy(() => import('pages/forms/layouts/basic')));
const FormsLayoutMultiColumn = Loadable(lazy(() => import('pages/forms/layouts/multi-column')));
const FormsLayoutActionBar = Loadable(lazy(() => import('pages/forms/layouts/action-bar')));
const FormsLayoutStickyBar = Loadable(lazy(() => import('pages/forms/layouts/sticky-bar')));

const FormsPluginsMask = Loadable(lazy(() => import('pages/forms/plugins/mask')));
const FormsPluginsClipboard = Loadable(lazy(() => import('pages/forms/plugins/clipboard')));
const FormsPluginsRecaptcha = Loadable(lazy(() => import('pages/forms/plugins/re-captcha')));
const FormsPluginsEditor = Loadable(lazy(() => import('pages/forms/plugins/editor')));
const FormsPluginsDropzone = Loadable(lazy(() => import('pages/forms/plugins/dropzone')));

const ReactTableBasic = Loadable(lazy(() => import('pages/tables/react-table/basic')));
const ReactDenseTable = Loadable(lazy(() => import('pages/tables/react-table/dense')));
const ReactTableSorting = Loadable(lazy(() => import('pages/tables/react-table/sorting')));
const ReactTableFiltering = Loadable(lazy(() => import('pages/tables/react-table/filtering')));
const ReactTableGrouping = Loadable(lazy(() => import('pages/tables/react-table/grouping')));
const ReactTablePagination = Loadable(lazy(() => import('pages/tables/react-table/pagination')));
const ReactTableRowSelection = Loadable(lazy(() => import('pages/tables/react-table/row-selection')));
const ReactTableExpanding = Loadable(lazy(() => import('pages/tables/react-table/expanding')));
const ReactTableEditable = Loadable(lazy(() => import('pages/tables/react-table/editable')));
const ReactTableDragDrop = Loadable(lazy(() => import('pages/tables/react-table/drag-drop')));
const ReactTableColumnVisibility = Loadable(lazy(() => import('pages/tables/react-table/column-visibility')));
const ReactTableColumnResizing = Loadable(lazy(() => import('pages/tables/react-table/column-resizing')));
const ReactTableStickyTable = Loadable(lazy(() => import('pages/tables/react-table/sticky')));
const ReactTableUmbrella = Loadable(lazy(() => import('pages/tables/react-table/umbrella')));
const ReactTableEmpty = Loadable(lazy(() => import('pages/tables/react-table/empty')));
const ReactTableVirtualized = Loadable(lazy(() => import('pages/tables/react-table/virtualized')));

// render - charts & map
const ChartApexchart = Loadable(lazy(() => import('pages/charts/apexchart')));
const ChartOrganization = Loadable(lazy(() => import('pages/charts/org-chart')));
const Map = Loadable(lazy(() => import('pages/map')));

// table routing
const MuiTableBasic = Loadable(lazy(() => import('pages/tables/mui-table/basic')));
const MuiTableDense = Loadable(lazy(() => import('pages/tables/mui-table/dense')));
const MuiTableEnhanced = Loadable(lazy(() => import('pages/tables/mui-table/enhanced')));
const MuiTableDatatable = Loadable(lazy(() => import('pages/tables/mui-table/datatable')));
const MuiTableCustom = Loadable(lazy(() => import('pages/tables/mui-table/custom')));
const MuiTableFixedHeader = Loadable(lazy(() => import('pages/tables/mui-table/fixed-header')));
const MuiTableCollapse = Loadable(lazy(() => import('pages/tables/mui-table/collapse')));

// pages routing
const AuthLogin = Loadable(lazy(() => import('pages/auth/jwt/login')));
const AuthRegister = Loadable(lazy(() => import('pages/auth/jwt/register')));
const AuthClaimNgo = Loadable(lazy(() => import('pages/auth/jwt/claim-ngo')));
const AuthForgotPassword = Loadable(lazy(() => import('pages/auth/jwt/forgot-password')));
const AuthResetPassword = Loadable(lazy(() => import('pages/auth/jwt/reset-password')));
const AuthCheckMail = Loadable(lazy(() => import('pages/auth/jwt/check-mail')));
const AuthCodeVerification = Loadable(lazy(() => import('pages/auth/jwt/code-verification')));

const MaintenanceError = Loadable(lazy(() => import('pages/maintenance/404')));
const MaintenanceError500 = Loadable(lazy(() => import('pages/maintenance/500')));
const MaintenanceUnderConstruction = Loadable(lazy(() => import('pages/maintenance/under-construction')));
const MaintenanceComingSoon = Loadable(lazy(() => import('pages/maintenance/coming-soon')));

const AppContactUS = Loadable(lazy(() => import('pages/contact-us')));

// render - sample page
const SamplePage = Loadable(lazy(() => import('pages/extra-pages/sample-page')));
const PricingPage = Loadable(lazy(() => import('pages/extra-pages/pricing')));

// ==============================|| MAIN ROUTING ||============================== //

const MainRoutes = {
  path: '/',
  children: [
    {
      path: '/',
      element: <DashboardLayout />,
      children: [
        {
          path: 'dashboard',
          children: [
            {
              path: 'default',
              element: <DashboardDefault />
            },
            {
              path: 'analytics',
              element: <DashboardAnalytics />
            },
            {
              path: 'invoice',
              element: <AppInvoiceDashboard />
            }
          ]
        },

        {
          path: 'ngoSearchPage',
          children: [
            {
              path: 'search',
              element: <NgoSearchPage />
            }
          ]
        },

        {
          path: 'masters',
          children: [
            {
              path: 'categories',
              element: <CategoriesTable />
            },
            {
              path: 'categories/category-images/:categoryId',
              element: <ImageComponent type={'category'} />
            },
            {
              path: 'ngo-types',
              element: <NgoTypes />
            },
            {
              path: 'sub-categories',
              element: <SubCategoriesTable />
            },
            {
              path: 'roles',
              element: <Roles />
            },
            {
              path: 'items',
              element: <Items />
            },
            {
              path: 'skills',
              element: <Skills />
            },
            {
              path: 'transactions',
              element: <Transactions />
            },
            {
              path: 'slides',
              element: <Slides />
            },
            {
              path: 'newsletters',
              element: <Newsletters />
            },
            {
              path: 'collections',
              element: <Collections />
            },
            {
              path: 'faqs',
              element: <Faqs />
            },
            {
              path: 'emails',
              element: <Emails />
            },
            {
              path: 'emails/add',
              element: <EmailsForm />
            },
            {
              path: 'emails/edit/:templateId',
              element: <EmailsForm />
            },
            {
              path: 'documents',
              element: <Documents />
            },
            {
              path: 'questions',
              element: <Questions />
            },
            {
              path: 'logs',
              element: <AuditLogs />
            },
            {
              path: 'profile-queries',
              element: <ProfileQueries />
            },
            {
              path: 'feedback-bugs',
              element: <FeedbackandBugs />
            },
            {
              path: 'campaigns',
              element: <Campaigns campaignType="campaigns" />,
              children: []
            },
            {
              path: 'events',
              element: <Campaigns campaignType="events" />,
              children: []
            },
            {
              path: 'campaigns/campaign-images/:campaignId',
              element: <ImageComponent type={'campaign'} />
            },
            {
              path: 'campaigns/add',
              // element: <CampaignForm isEdit={false} />
              element: <InitiativeSelector />
            },
            {
              path: 'campaigns/add/campaign',
              // element: <CampaignForm isEdit={false} />
              element: <CampaignForm />
            },
            {
              path: 'events/add/event',
              // element: <CampaignForm isEdit={false} />
              element: <EventForm />
            },
            {
              path: 'campaigns/edit/campaign/:campaignId',
              // element: <CampaignForm isEdit={false} />
              element: <CampaignForm />
            },
            {
              path: 'events/edit/event/:eventId',
              // element: <CampaignForm isEdit={false} />
              element: <EventForm />
            },
            {
              path: 'campaigns/edit/:campaignId',
              element: <CampaignForm isEdit={true} />
            },
            {
              path: 'staff-members',
              element: <StaffMembers />
            },
            {
              path: 'staff-members/view-journals/:senderId',
              element: <ViewJournals />
            },
            {
              path: 'ngos',
              element: <NGOS />
            },
            {
              path: 'ngos/ngo-images/:ngoId',
              element: <ImageComponent type={'ngo'} />
            },
            {
              path: 'ngos',
              children: [
                {
                  path: 'add',
                  element: <AccountNgos />,
                  children: [
                    {
                      path: 'basic/:newNgoId',
                      element: <AccountTabProfile />
                    },
                    {
                      path: 'personal/:newNgoId',
                      element: <AccountTabPersonal />
                    },
                    {
                      path: 'my-account/:newNgoId',
                      element: <AccountTabAccount />
                    },
                    {
                      path: 'password/:newNgoId',
                      element: <AccountTabPassword />
                    },
                    {
                      path: 'role/:newNgoId',
                      element: <AccountTabRole />
                    },
                    {
                      path: 'preview/:newNgoId',
                      element: <AccountTabPreview />
                    },
                    {
                      path: 'socials/:newNgoId',
                      element: <AccountAdditionalInfo />
                    },
                    {
                      path: 'bank-accounts/:newNgoId',
                      element: <AccountBankDetails />
                    },
                    {
                      path: 'settings/:newNgoId',
                      element: <AccountTabSettings />
                    },
                    {
                      path: 'documents/:newNgoId',
                      element: <AccountTabDocuments />
                    },
                    {
                      path: 'ngoprofile/:newNgoId',
                      element: <AccountTabNGOProfile />
                    }
                  ]
                },
                {
                  path: 'edit',
                  element: <AccountNgos />,
                  children: [
                    {
                      path: 'basic/:newNgoId',
                      element: <AccountTabProfile />
                    },
                    {
                      path: 'personal/:newNgoId',
                      element: <AccountTabPersonal />
                    },
                    {
                      path: 'my-account/:newNgoId',
                      element: <AccountTabAccount />
                    },
                    {
                      path: 'password/:newNgoId',
                      element: <AccountTabPassword />
                    },
                    {
                      path: 'role/:newNgoId',
                      element: <AccountTabRole />
                    },
                    {
                      path: 'preview/:newNgoId',
                      element: <AccountTabPreview />
                    },
                    {
                      path: 'socials/:newNgoId',
                      element: <AccountAdditionalInfo />
                    },
                    {
                      path: 'bank-accounts/:newNgoId',
                      element: <AccountBankDetails />
                    },
                    {
                      path: 'images/:newNgoId',
                      element: <ImageComponent type={'ngo'} />
                    },
                    {
                      path: 'settings/:newNgoId',
                      element: <AccountTabSettings />
                    },
                    {
                      path: 'documents/:newNgoId',
                      element: <AccountTabDocuments />
                    },
                    {
                      path: 'add-comments/:newNgoId',
                      element: <AccountTabAddCommnets />
                    },
                    {
                      path: 'ngoprofile/:newNgoId',
                      element: <AccountTabNGOProfile />
                    }
                  ]
                }
              ]
            },
            {
              path: 'ngos/add/:ngoId',
              children: [
                {
                  path: 'basic',
                  element: <AccountTabProfile />
                },
                {
                  path: 'personal',
                  element: <AccountTabPersonal />
                },
                {
                  path: 'my-account',
                  element: <AccountTabAccount />
                },
                {
                  path: 'password',
                  element: <AccountTabPassword />
                },
                {
                  path: 'role',
                  element: <AccountTabRole />
                },
                {
                  path: 'add-comments',
                  element: <AccountTabPreview />
                },
                {
                  path: 'settings',
                  element: <AccountTabSettings />
                },
                {
                  path: 'documents',
                  element: <AccountTabDocuments />
                },
                {
                  path: 'ngoprofile',
                  element: <AccountTabNGOProfile />
                }
              ]
            },
            {
              path: 'ngos/edit/:ngoId',
              element: <NGOAddEditForm />
            },
            {
              path: 'users',
              element: <Users />
            },
            {
              path: 'products',
              element: <Products />
            },
            {
              path: 'products/add',
              element: <ProductPreviewAddEdit isEditing={false} />
            },
            {
              path: 'products/edit/:productId',
              element: <ProductPreviewAddEdit isEditing={true} />
            }
          ]
          // children: filteredMastersRoutes
        },
        {
          path: 'auth',
          children: [{ path: 'verify-email', element: <VerifyEmail /> }]
        },
        {
          path: 'channels',
          children: [
            {
              path: 'buckets',
              element: <Buckets />
            },
            {
              path: 'buckets/add',
              element: <BucketForm />
            },
            {
              path: 'buckets/edit/:bucketId',
              element: <BucketForm />
            },
            {
              path: 'themes',
              element: <ThemesCard />
            },
            {
              path: 'cover-images',
              element: <CoverImages />
            },
            {
              path: 'gallery-images',
              element: <ImageComponent type={'ngo'} />
            },
            {
              path: 'impact-areas',
              element: <ImpactAreas />
            },
            {
              path: 'orders',
              element: <Orders />
            },
            {
              path: 'user-notifications',
              element: <SendNotification />
            }
          ]
          // children: filteredMastersRoutes
        },
        {
          path: 'campaigns',
          children: [
            {
              path: 'dashboard/:campaignId',
              element: <CampaignDashboard isEdit={true} />
            }
          ]
        },
        {
          path: 'widget',
          children: [
            {
              path: 'statistics',
              element: <WidgetStatistics />
            },
            {
              path: 'data',
              element: <WidgetData />
            },
            {
              path: 'chart',
              element: <WidgetChart />
            }
          ]
        },
        {
          path: 'apps',
          children: [
            {
              path: 'chat',
              element: <AppChat />
            },
            {
              path: 'calendar',
              element: <AppCalendar />
            },
            {
              path: 'kanban',
              element: <AppKanban />,
              children: [
                {
                  path: 'backlogs',
                  element: <AppKanbanBacklogs />
                },
                {
                  path: 'board',
                  element: <AppKanbanBoard />
                }
              ]
            },
            {
              path: 'customer',
              children: [
                {
                  path: 'customer-list',
                  element: <AppCustomerList />
                },
                {
                  path: 'customer-card',
                  element: <AppCustomerCard />
                }
              ]
            },
            {
              path: 'invoice',
              children: [
                {
                  path: 'dashboard',
                  element: <AppInvoiceDashboard />
                },
                {
                  path: 'create',
                  element: <AppInvoiceCreate />
                },
                {
                  path: 'details/:id',
                  element: <AppInvoiceDetails />
                },
                {
                  path: 'edit/:id',
                  element: <AppInvoiceEdit />
                },
                {
                  path: 'list',
                  element: <AppInvoiceList />
                }
              ]
            },
            {
              path: 'profiles',
              children: [
                {
                  path: 'account',
                  element: <AccountProfile />,
                  children: [
                    {
                      path: 'basic',
                      element: <AccountTabProfile />
                    },
                    {
                      path: 'personal',
                      element: <AccountTabPersonal />
                    },
                    {
                      path: 'my-account',
                      element: <AccountTabAccount />
                    },
                    {
                      path: 'password',
                      element: <AccountTabPassword />
                    },
                    {
                      path: 'role',
                      element: <AccountTabRole />
                    },
                    {
                      path: 'add-comments',
                      element: <AccountTabPreview />
                    },
                    {
                      path: 'socials',
                      element: <AccountAdditionalInfo />
                    },
                    {
                      path: 'bank-accounts',
                      element: <AccountBankDetails />
                    },
                    {
                      path: 'profile-queries',
                      element: <AccountProfileQueries />
                    },
                    {
                      path: 'images',
                      element: <ImageComponent type={'ngo'} />
                    },
                    {
                      path: 'settings',
                      element: <AccountTabSettings />
                    },
                    {
                      path: 'documents',
                      element: <AccountTabDocuments />
                    },
                    {
                      path: 'add-comments',
                      element: <AccountTabAddCommnets />
                    },
                    {
                      path: 'ngoprofile',
                      element: <AccountTabNGOProfile />
                    }
                  ]
                },
                {
                  path: 'user',
                  element: <UserProfile />,
                  children: [
                    {
                      path: 'personal',
                      element: <UserTabPersonal />
                    },
                    {
                      path: 'payment',
                      element: <UserTabPayment />
                    },
                    {
                      path: 'password',
                      element: <UserTabPassword />
                    },
                    {
                      path: 'settings',
                      element: <UserTabSettings />
                    }
                  ]
                }
              ]
            },
            {
              path: ' ',
              children: [
                {
                  path: 'products',
                  element: <AppECommProducts />,
                  loader: productsLoader,
                  errorElement: <ErrorBoundary />
                },
                {
                  path: 'product-details/:id',
                  element: <AppECommProductDetails />,
                  loader: productLoader,
                  errorElement: <ErrorBoundary />
                },
                {
                  path: 'product-list',
                  element: <AppECommProductList />,
                  loader: productsLoader,
                  errorElement: <ErrorBoundary />
                },
                {
                  path: 'add-new-product',
                  element: <AppECommAddProduct />
                },
                {
                  path: 'checkout',
                  element: <AppECommCheckout />
                }
              ]
            }
          ]
        },
        {
          path: 'forms',
          children: [
            {
              path: 'validation',
              element: <FormsValidation />
            },
            {
              path: 'wizard',
              element: <FormsWizard />
            },
            {
              path: 'layout',
              children: [
                {
                  path: 'basic',
                  element: <FormsLayoutBasic />
                },
                {
                  path: 'multi-column',
                  element: <FormsLayoutMultiColumn />
                },
                {
                  path: 'action-bar',
                  element: <FormsLayoutActionBar />
                },
                {
                  path: 'sticky-bar',
                  element: <FormsLayoutStickyBar />
                }
              ]
            },
            {
              path: 'plugins',
              children: [
                {
                  path: 'mask',
                  element: <FormsPluginsMask />
                },
                {
                  path: 'clipboard',
                  element: <FormsPluginsClipboard />
                },
                {
                  path: 're-captcha',
                  element: <FormsPluginsRecaptcha />
                },
                {
                  path: 'editor',
                  element: <FormsPluginsEditor />
                },
                {
                  path: 'dropzone',
                  element: <FormsPluginsDropzone />
                }
              ]
            }
          ]
        },
        {
          path: 'tables',
          children: [
            {
              path: 'react-table',
              children: [
                {
                  path: 'basic',
                  element: <ReactTableBasic />
                },
                {
                  path: 'dense',
                  element: <ReactDenseTable />
                },
                {
                  path: 'sorting',
                  element: <ReactTableSorting />
                },
                {
                  path: 'filtering',
                  element: <ReactTableFiltering />
                },
                {
                  path: 'grouping',
                  element: <ReactTableGrouping />
                },
                {
                  path: 'pagination',
                  element: <ReactTablePagination />
                },
                {
                  path: 'row-selection',
                  element: <ReactTableRowSelection />
                },
                {
                  path: 'expanding',
                  element: <ReactTableExpanding />
                },
                {
                  path: 'editable',
                  element: <ReactTableEditable />
                },
                {
                  path: 'drag-drop',
                  element: <ReactTableDragDrop />
                },
                {
                  path: 'column-visibility',
                  element: <ReactTableColumnVisibility />
                },
                {
                  path: 'column-resizing',
                  element: <ReactTableColumnResizing />
                },
                {
                  path: 'sticky-table',
                  element: <ReactTableStickyTable />
                },
                {
                  path: 'umbrella',
                  element: <ReactTableUmbrella />
                },
                {
                  path: 'empty',
                  element: <ReactTableEmpty />
                },
                {
                  path: 'virtualized',
                  element: <ReactTableVirtualized />
                }
              ]
            },
            {
              path: 'mui-table',
              children: [
                {
                  path: 'basic',
                  element: <MuiTableBasic />
                },
                {
                  path: 'dense',
                  element: <MuiTableDense />
                },
                {
                  path: 'enhanced',
                  element: <MuiTableEnhanced />
                },
                {
                  path: 'datatable',
                  element: <MuiTableDatatable />
                },
                {
                  path: 'custom',
                  element: <MuiTableCustom />
                },
                {
                  path: 'fixed-header',
                  element: <MuiTableFixedHeader />
                },
                {
                  path: 'collapse',
                  element: <MuiTableCollapse />
                }
              ]
            }
          ]
        },
        {
          path: 'charts',
          children: [
            {
              path: 'apexchart',
              element: <ChartApexchart />
            },
            {
              path: 'org-chart',
              element: <ChartOrganization />
            }
          ]
        },
        {
          path: 'map/ngos',
          element: <Map />
        },
        {
          path: 'map/campaigns',
          element: <Map />
        },
        {
          path: 'sample-page',
          element: <SamplePage />
        },
        {
          path: 'pricing',
          element: <PricingPage />
        }
      ]
    },
    {
      path: '/maintenance',
      element: <PagesLayout />,
      children: [
        {
          path: '404',
          element: <MaintenanceError />
        },
        {
          path: '500',
          element: <MaintenanceError500 />
        },
        {
          path: 'under-construction',
          element: <MaintenanceUnderConstruction />
        },
        {
          path: 'coming-soon',
          element: <MaintenanceComingSoon />
        }
      ]
    },
    {
      path: '/auth',
      element: <PagesLayout />,
      children: [
        {
          path: 'login',
          element: <AuthLogin />
        },
        {
          path: 'register',
          element: <AuthRegister />
        },
        {
          path: 'forgot-password',
          element: <AuthForgotPassword />
        },
        {
          path: 'reset-password',
          element: <AuthResetPassword />
        },
        {
          path: 'check-mail',
          element: <AuthCheckMail />
        },
        {
          path: 'code-verification',
          element: <AuthCodeVerification />
        },
        {
          path: 'claim-ngo',
          element: <AuthCodeVerification />
        }
      ]
    },
    {
      path: '/',
      element: <SimpleLayout layout={SimpleLayoutType.SIMPLE} />,
      children: [
        {
          path: 'contact-us',
          element: <AppContactUS />
        }
      ]
    }
  ]
};

export default MainRoutes;
