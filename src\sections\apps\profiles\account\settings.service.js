import axiosServices from 'utils/axios_node';

const API_BASE_URL = `${import.meta.env.VITE_APP_APPLICATION_API_URL}/api`;

export const getSettingsMasterInfo = async (pageName) => {
  const response = await axiosServices.get(`${API_BASE_URL}/settings-master?pageName=${pageName}`);
  return response?.data;
};
export const getNgoSettings = async (ngo_id, pageName) => {
  const response = await axiosServices.get(`${API_BASE_URL}/ngo-settings/${ngo_id}?pageName=${pageName}`);
  return response?.data;
};
export const updateNgoSettings = async (payload, ngo_id, pageName = '') => {
  const response = await axiosServices.post(`${API_BASE_URL}/ngo-settings/${ngo_id}?pageName=${pageName}`, payload);
  return response?.data;
};
export const deleteAllByNgoId = async (ngo_id) => {
  const response = await axiosServices.delete(`${API_BASE_URL}/ngo-settings/${ngo_id}`);
  return response?.data;
};
