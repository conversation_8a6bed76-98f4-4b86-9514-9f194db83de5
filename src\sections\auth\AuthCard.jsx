import PropTypes from 'prop-types';
import Box from '@mui/material/Box';

// project import
import MainCard from 'components/MainCard';
import { bgcolor } from '@mui/system';
import { useTheme } from '@mui/material';

// ==============================|| AUTHENTICATION - CARD WRAPPER ||============================== //

export default function AuthCard({ children, ...other }) {
  const theme = useTheme();

  return (
    <MainCard
      sx={{
        bgcolor: theme.palette.primary.main,
        maxWidth: { xs: 400, lg: 475 },
        margin: { xs: 2.5, md: 3 },
        '& > *': { flexGrow: 1, flexBasis: '50%' },
      }}
      content={false}
      {...other}
      border={false}
      boxShadow={false}
      shadow={(theme) => theme.customShadows.z1}
    >
      <Box sx={{ p: { xs: 2, sm: 3, md: 4, xl: 5 } }}>{children}</Box>
    </MainCard>
  );
}

AuthCard.propTypes = { children: PropTypes.any, other: PropTypes.any };
