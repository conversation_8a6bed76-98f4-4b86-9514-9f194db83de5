import React, { useEffect, useState } from 'react';
import CustomerTable from 'sections/apps/customer/CustomerTable';
import {
  Button,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  TextField,
  MenuItem,
  Select,
  FormControl,
  InputLabel
} from '@mui/material';
import { toast, ToastContainer } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import { getUserPermissions } from 'utils/permissionUtils';
import TableActions from 'components/TableActions';
import useAuth from 'hooks/useAuth';
import { addFaqService, deleteFaqService, fetchFaqService, updateFaqService } from './apis/faq.service';
import dayjs from 'dayjs';

export default function FaqTable() {
  const { user } = useAuth();
  const [faqs, setFaqs] = useState([]);
  const [openDialog, setOpenDialog] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [currentFaq, setCurrentFaq] = useState(null);

  // Role-based access
  const permissions = getUserPermissions(user);
  const canAdd = permissions?.Add?.Faqs || false;
  const canEdit = permissions?.Edit?.Faqs || false;
  const canDelete = permissions?.Delete?.Faqs || false;

  useEffect(() => {
    fetchFaqs();
  }, []);

  const fetchFaqs = async () => {
    try {
      const data = await fetchFaqService();
      setFaqs(data);
    } catch (error) {
      console.error('Failed to fetch FAQs:', error);
      toast.error('Failed to fetch FAQs');
    }
  };

  const handleAddOrEdit = async (e) => {
    e.preventDefault();
    try {
      if (isEditing) {
        await updateFaqService(currentFaq.id, currentFaq);
        toast.success('FAQ updated successfully!');
      } else {
        await addFaqService(currentFaq);
        toast.success('FAQ added successfully!');
      }
      setOpenDialog(false);
      fetchFaqs();
    } catch (error) {
      console.error('Failed to save FAQ:', error);
      toast.error('Failed to save FAQ');
    }
  };

  const handleDelete = async (id) => {
    if (!canDelete) {
      toast.error('You do not have permission to delete FAQs.');
      return;
    }
    const confirmDelete = window.confirm('Are you sure you want to delete this FAQ?');
    if (confirmDelete) {
      try {
        await deleteFaqService(id);
        toast.success('FAQ deleted successfully!');
        fetchFaqs();
      } catch (error) {
        console.error('Failed to delete FAQ:', error);
        toast.error('Failed to delete FAQ');
      }
    }
  };

  const openAddDialog = () => {
    if (!canAdd) {
      toast.error('You do not have permission to add FAQs.');
      return;
    }
    setCurrentFaq({ id: 0, question: '', answer: '', status: 'Active' });
    setOpenDialog(true);
    setIsEditing(false);
  };

  const openEditDialog = (faq) => {
    if (!canEdit) {
      toast.error('You do not have permission to edit FAQs.');
      return;
    }
    setCurrentFaq(faq);
    setIsEditing(true);
    setOpenDialog(true);
  };

  const columns = [
    {
      accessorKey: 'question',
      header: 'Question',
      showByDefault: true,
      cell: ({ cell }) => (
        <Button variant="text" onClick={() => openEditDialog(cell.row.original)}>
          {cell.row.original.question}
        </Button>
      )
    },
    { accessorKey: 'answer', header: 'Answer', showByDefault: true },
    { accessorKey: 'status', header: 'Status', showByDefault: true },
    {
      accessorKey: 'createdAt',
      header: 'Created At',
      showByDefault: true,
      cell: (cell) => <span>{dayjs(cell.row.original.createdAt).format('DD-MM-YYYY hh:mm A')}</span>
    },
    {
      accessorKey: 'updatedAt',
      header: 'Updated At',
      showByDefault: true,
      cell: (cell) => <span>{dayjs(cell.row.original.updatedAt).format('DD-MM-YYYY hh:mm A')}</span>
    },
    {
      id: 'actions',
      header: 'Actions',
      cell: (cell) => <TableActions handleEditClick={openEditDialog} cell={cell} handleDeleteClick={handleDelete} />
    }
  ];

  return (
    <div>
      <CustomerTable data={faqs} columns={columns} modalToggler={openAddDialog} category={'FAQ'} />

      <Dialog
        open={openDialog}
        onClose={(event, reason) => {
          if (reason !== 'backdropClick' && reason !== 'escapeKeyDown') {
            setOpenDialog(false);
          }
        }}
        maxWidth="50%"
        PaperProps={{
          sx: {
            top: 0,
            position: 'absolute',
            borderRadius: '16px',
            width: '100%',
            maxWidth: '600px'
          }
        }}
      >
        <form autoComplete="off" onSubmit={handleAddOrEdit}>
          <DialogTitle>{isEditing ? 'Edit FAQ' : 'Add FAQ'}</DialogTitle>
          <DialogContent>
            <TextField
              autoFocus
              margin="dense"
              label="Question"
              type="text"
              fullWidth
              required
              variant="outlined"
              value={currentFaq?.question || ''}
              onChange={(e) => setCurrentFaq({ ...currentFaq, question: e.target.value })}
            />
            <TextField
              margin="dense"
              label="Answer"
              type="text"
              fullWidth
              required
              variant="outlined"
              value={currentFaq?.answer || ''}
              onChange={(e) => setCurrentFaq({ ...currentFaq, answer: e.target.value })}
            />
            <FormControl fullWidth margin="dense">
              <InputLabel>Status</InputLabel>
              <Select
                value={currentFaq?.status || 'Active'}
                onChange={(e) => setCurrentFaq({ ...currentFaq, status: e.target.value })}
                variant="outlined"
              >
                <MenuItem value="Active">Active</MenuItem>
                <MenuItem value="Inactive">Inactive</MenuItem>
              </Select>
            </FormControl>
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setOpenDialog(false)} color="primary">
              Cancel
            </Button>
            <Button type="submit" color="primary">
              {isEditing ? 'Update' : 'Add'}
            </Button>
          </DialogActions>
        </form>
      </Dialog>

      <ToastContainer />
    </div>
  );
}
