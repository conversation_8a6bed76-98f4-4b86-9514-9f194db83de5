//React
import { useEffect } from 'react';
// material-ui
import Avatar from '@mui/material/Avatar';
import AvatarGroup from '@mui/material/AvatarGroup';
import Box from '@mui/material/Box';
import Button from '@mui/material/Button';
import Grid from '@mui/material/Grid';
import List from '@mui/material/List';
import ListItemAvatar from '@mui/material/ListItemAvatar';
import ListItemButton from '@mui/material/ListItemButton';
import ListItemSecondaryAction from '@mui/material/ListItemSecondaryAction';
import ListItemText from '@mui/material/ListItemText';
import Stack from '@mui/material/Stack';
import Typography from '@mui/material/Typography';

// project import
import MainCard from 'components/MainCard';
import AnalyticEcommerce from 'components/cards/statistics/AnalyticEcommerce';
import MonthlyBarChart from 'sections/dashboard/default/MonthlyBarChart';
import ReportAreaChart from 'sections/dashboard/default/ReportAreaChart';
import UniqueVisitorCard from 'sections/dashboard/default/UniqueVisitorCard';
import SaleReportCard from 'sections/dashboard/default/SaleReportCard';
import OrdersTable from 'sections/dashboard/default/OrdersTable';

// assets
import GiftOutlined from '@ant-design/icons/GiftOutlined';
import MessageOutlined from '@ant-design/icons/MessageOutlined';
import SettingOutlined from '@ant-design/icons/SettingOutlined';
import avatar1 from 'assets/images/users/avatar-1.png';
import avatar2 from 'assets/images/users/avatar-2.png';
import avatar3 from 'assets/images/users/avatar-3.png';
import avatar4 from 'assets/images/users/avatar-4.png';
import ProfileCard from 'sections/apps/profiles/user/ProfileCard';
import WelcomeBanner from 'sections/dashboard/analytics/WelcomeBanner';
import { Fragment, useState } from 'react';
import AlertMessage from './Alertmessage';
import { API_BASE_URL } from 'api/campaigns.service';
import useAuth from 'hooks/useAuth';
import { getNgoById } from 'api/ngos.service';
import { calculateProfileCompletion } from 'utils/permissionUtils';
import { CircularProgress, Dialog, DialogActions, DialogContent, DialogContentText, DialogTitle } from '@mui/material';
import { getCampaignStats, getFullCampaignStats, getFullNGOStats, getNGOStats } from 'pages/masters/apis/notification.service';
import { Link } from 'react-router-dom';
import { updateUsingPatchNGO } from 'sections/apps/profiles/profile.service';
import { ToastContainer, toast } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import { getProfileQueries } from 'sections/apps/profiles/account/tabs.service';

// avatar style
const avatarSX = {
  width: 36,
  height: 36,
  fontSize: '1rem'
};

// action style
const actionSX = {
  mt: 0.75,
  ml: 1,
  top: 'auto',
  right: 'auto',
  alignSelf: 'flex-start',
  transform: 'none'
};

// ==============================|| DASHBOARD - DEFAULT ||============================== //

export default function DashboardDefault() {
  const roleInfo = JSON.parse(localStorage.getItem('user'))?.roleInfo || {};
  const { user } = useAuth();
  const [profileCompletePercentage, setProfileCompletePercentage] = useState(null);
  const [loading, setLoading] = useState(false);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [ngoInfo, setNgoInfo] = useState(null);
  const [isLoadingCampaign, setIsLoadingCampaign] = useState(false);

  const [alertMessages, setAlertMessages] = useState({
    pendingCount: 0,
    inReviewCount: 0
  });
  const [ngoStats, setNgoStats] = useState({
    pendingCount: 0,
    inReviewNgos: 0,
    verifiedNgos: 0,
    totalAssignedNgos: 0
  });
  const [campaignStats, setCampaignStats] = useState({
    totalCampaigns: 0,
    completed: 0,
    live: 0,
    approved: 0,
    inReview: 0,
    draft: 0
  });
  const [alertMessagesAllNgo, setAlertMessagesAllNgo] = useState({
    inReviewNgoCount: 0
  });
  const [alertMessagesAllCam, setAlertMessagesAllCam] = useState({
    inReviewCampaginCount: 0
  });
  const [ngoStatsAll, setNgoStatsAll] = useState({
    totalNgos: 0,
    inReviewNgos: 0,
    pendingNgos: 0,
    verifiedNgos: 0
  });
  const [campaignStatsAll, setCampaignStatsAll] = useState({
    totalCampaigns: 0,
    live: 0,
    inReview: 0,
    approved: 0,
    completed: 0
  });

  const [profileQueries, setProfileQueries] = useState([]);

  useEffect(() => {
    const fetchNgoStats = async () => {
      try {
        const response = await getNGOStats(user?.id);
        setAlertMessages(response.alertMessages);
        setNgoStats(response.ngoStats);
      } catch (error) {
        console.error('Error fetching NGO stats:', error);
      }
    };

    const fetchCampaignStats = async () => {
      try {
        const response = await getCampaignStats(user?.ngo_id);

        setCampaignStats(response.campaignStats);
      } catch (error) {
        console.error('Error fetching NGO stats:', error);
      }
    };

    const fetchProfileQueries = async () => {
      try {
        const response = await getProfileQueries(null, user?.roleInfo?.name === 'DR_Management' ? null : user?.id, 'profile-queries');
        setProfileQueries(response);
      } catch (error) {
        console.error('Error fetching NGO stats:', error);
      }
    };
    fetchProfileQueries();
    fetchNgoStats();
    if (user?.ngo_id) {
      fetchCampaignStats();
    }
  }, []);
  useEffect(() => {
    const fetchFullNgoStats = async () => {
      try {
        const response = await getFullNGOStats();
        setNgoStatsAll(response.ngoStats);
      } catch (error) {
        console.error('Error fetching full NGO stats:', error);
      }
    };

    const fetchFullCampaignStats = async () => {
      setIsLoadingCampaign(true);
      try {
        const response = await getFullCampaignStats();

        setCampaignStatsAll(response.campaignStats);
      } catch (error) {
        console.error('Error fetching full campaign stats:', error);
      } finally {
        setIsLoadingCampaign(false);
      }
    };

    if (roleInfo.name === 'DR_Management') {
      setLoading(true);
      Promise.all([fetchFullNgoStats(), fetchFullCampaignStats()]).finally(() => {
        setLoading(false);
      });
    }
  }, [roleInfo.name]);
  useEffect(() => {
    const fetchNgoDataAndCalculate = async () => {
      if (user?.ngo_id) {
        try {
          const ngoRecords = await getNgoById(user?.ngo_id);
          const profilepercentage = await calculateProfileCompletion(ngoRecords);

          setNgoInfo(ngoRecords);
          if (profilepercentage == 100 && (ngoRecords?.ngo_status == 'New' || ngoRecords?.ngo_status == 'Pending')) {
            setIsDialogOpen(true);
          }
          setProfileCompletePercentage(profilepercentage);
        } catch (error) {
          console.error('Error fetching NGO data:', error);
        }
      } else {
        setProfileCompletePercentage(0);
      }
    };

    fetchNgoDataAndCalculate();
  }, [user?.ngo_id]);
  const handleDialogClose = () => {
    setIsDialogOpen(false);
  };
  const handleSubmitReview = async (e) => {
    e.preventDefault();
    if (window.confirm('Are you sure you want to submit profile for review')) {
      try {
        await updateUsingPatchNGO(user?.ngo_id, { ngo_status: 'In Review', last_status: 'In Review' }, 'NGOProfile');
        toast.success('Your profile has been submitted for review.');
        // fetchNGODetails(user?.ngo_id);
        setTimeout(() => {
          window.location.reload();
        }, 1000);
        return;
      } catch (error) {
        console.error('Error updating ngo details:', error);
        toast.error('Failed to update NGO details');
      }
    }
  };

  return (
    <Fragment>
      {roleInfo.name !== 'NGO_Management' && roleInfo.name !== 'NGO_Staff' && roleInfo.name !== 'DR_Management' && (
        <Grid container rowSpacing={4.5} columnSpacing={2.75}>
          {alertMessages.inReviewCount > 0 && (
            <Grid item xs={12}>
              <AlertMessage
                message={`${alertMessages.inReviewCount} NGO(s) are currently under review. Please take the appropriate action.`}
                status="In Review"
              />
            </Grid>
          )}
          {alertMessages.pendingCount > 0 && (
            <Grid item xs={6} style={{}}>
              <AlertMessage
                message={`${alertMessages.pendingCount} NGO(s) are currently assigned to you. Please review the NGO.`}
                status="Pending"
              />
            </Grid>
          )}
          {profileQueries?.length > 0 && (
            <Grid item xs={6} style={{}}>
              <AlertMessage
                message={`${profileQueries?.length} NGO(s) have requested profile edits. Please review their requests.`}
                status="Pending"
                type="profile-query"
              />
            </Grid>
          )}

          <Grid item xs={12} sx={{ mb: -2.25 }}>
            <Typography variant="h5">NGO Stats</Typography>
          </Grid>

          <Grid item xs={12} sm={6} md={4} lg={3}>
            <AnalyticEcommerce title="Assigned NGOS" count={ngoStats.totalAssignedNgos} url="/masters/ngos?alertStatus=All" />
          </Grid>
          <Grid item xs={12} sm={6} md={4} lg={3}>
            <AnalyticEcommerce title="Pending NGOS" count={ngoStats.pendingCount} url="/masters/ngos/?alertStatus=Pending" />
          </Grid>
          <Grid item xs={12} sm={6} md={4} lg={3}>
            <AnalyticEcommerce title="In-Review NGOS" count={ngoStats.inReviewNgos} url="/masters/ngos/?alertStatus=In Review" />
          </Grid>

          <Grid item xs={12} sm={6} md={4} lg={3}>
            <AnalyticEcommerce title="Verified NGOS" count={ngoStats.verifiedNgos} url="/masters/ngos/?alertStatus=Verified" />
          </Grid>
        </Grid>
      )}
      <Grid container rowSpacing={4.5} columnSpacing={2.75}>
        {(roleInfo.name === 'NGO_Management' || roleInfo.name === 'NGO_Staff') && (
          <Fragment>
            {/* <Grid item xs={12}>
              <WelcomeBanner />
            </Grid> */}

            {ngoInfo &&
              profileCompletePercentage &&
              (ngoInfo?.ngo_status == 'Pending' || ngoInfo?.ngo_status == 'New' || ngoInfo?.ngo_status == 'In Review') && (
                <Grid item xs={12}>
                  <ProfileCard ngoInfo={ngoInfo} profileCompletePercentage={profileCompletePercentage} />
                </Grid>
              )}
          </Fragment>
        )}
      </Grid>
      {/* row 1 */} {/** For ngo */}
      {(roleInfo.name === 'NGO_Management' || roleInfo.name === 'NGO_Staff') && (
        <Grid container rowSpacing={4.5} columnSpacing={2.75} sx={{ mt: 0.5, mb: 0.5 }}>
          <Grid item xs={12} sx={{ mb: -2.25 }}>
            <Typography variant="h5">Your Campaign Stats</Typography>
          </Grid>
          <Grid item xs={12} sm={6} md={4} lg={4}>
            {/* <Link to="/masters/campaigns" style={{ textDecoration: 'none' }}> */}
            <AnalyticEcommerce title="Total" count={campaignStats.totalCampaigns} url="/masters/campaigns" disableUrl={true} />
            {/* </Link> */}
          </Grid>
          <Grid item xs={12} sm={6} md={4} lg={4}>
            <AnalyticEcommerce
              title="Completed"
              count={campaignStats.completed}
              url="/masters/campaigns?alertStatus=Completed"
              disableUrl={true}
            />
            {/* </Link> */}
          </Grid>
          <Grid item xs={12} sm={6} md={4} lg={4}>
            <AnalyticEcommerce title="Live" count={campaignStats.live} url="/masters/campaigns?alertStatus=Live" disableUrl={true} />
          </Grid>
          <Grid item xs={12} sm={6} md={4} lg={4}>
            <AnalyticEcommerce
              title="Approved"
              count={campaignStats.approved}
              url="/masters/campaigns?alertStatus=Approved"
              disableUrl={true}
            />
          </Grid>
          <Grid item xs={12} sm={6} md={4} lg={4}>
            <AnalyticEcommerce
              title="In Review"
              count={campaignStats.inReview}
              url="/masters/campaigns?alertStatus=In Review"
              disableUrl={true}
            />
          </Grid>
          <Grid item xs={12} sm={6} md={4} lg={4}>
            <AnalyticEcommerce title="Draft" count={campaignStats.draft} url="/masters/campaigns?alertStatus=Draft" disableUrl={true} />
          </Grid>
        </Grid>
      )}
      {roleInfo.name === 'DR_Management' && (
        <Grid container rowSpacing={4.5} columnSpacing={2.75}>
          <Grid item xs={12} sx={{ mb: -2.25 }}>
            <Typography variant="h5">All Stats</Typography>
          </Grid>
          {loading ? (
            <Grid item xs={12} sx={{ display: 'flex', justifyContent: 'center', mt: 4 }}>
              <CircularProgress />
            </Grid>
          ) : (
            <>
              <Grid item xs={12} sm={6} md={4} lg={3}>
                <AnalyticEcommerce title="Total NGOs" count={ngoStatsAll?.totalNgos} />
              </Grid>
              <Grid item xs={12} sm={6} md={4} lg={3}>
                <AnalyticEcommerce title="Verified NGOs" count={ngoStatsAll?.verifiedNgos} />
              </Grid>
              <Grid item xs={12} sm={6} md={4} lg={3}>
                <AnalyticEcommerce
                  title="Total Campaigns"
                  count={campaignStatsAll?.totalCampaigns ? campaignStatsAll?.totalCampaigns : 0}
                />
              </Grid>
              <Grid item xs={12} sm={6} md={4} lg={3}>
                <AnalyticEcommerce title="Campaigns Completed" count={campaignStatsAll?.completed ? campaignStatsAll?.completed : 0} />
              </Grid>
            </>
          )}
        </Grid>
      )}
      {/** For Dr staffs */}
      {roleInfo.name === 'DR_Staff' && (
        <Grid container rowSpacing={4.5} columnSpacing={3}>
          <Grid item xs={12} sx={{ mb: -2.25 }}>
            <Typography variant="h5">Campaign Stats</Typography>
          </Grid>
          {isLoadingCampaign ? (
            <Grid item xs={12} display="flex" justifyContent="center" alignItems="center" sx={{ mt: 2 }}>
              <CircularProgress />
              <Typography sx={{ margin: 2 }}>Loading Campaigns Stats. Please wait....</Typography>
            </Grid>
          ) : (
            <>
              <Grid item xs={12} sm={6} md={4} lg={3}>
                <Link to="/masters/campaigns" style={{ textDecoration: 'none' }}>
                  <AnalyticEcommerce title="Total" count={campaignStatsAll.totalCampaigns} url="/masters/campaigns" />
                </Link>
              </Grid>
              <Grid item xs={12} sm={6} md={4} lg={3}>
                <Link to="/masters/campaigns" style={{ textDecoration: 'none' }}>
                  <AnalyticEcommerce title="In Review" count={campaignStatsAll.inReview} url="/masters/campaigns/?alertStatus=In Review" />
                </Link>
              </Grid>
              <Grid item xs={12} sm={6} md={4} lg={3}>
                <Link to="/masters/campaigns" style={{ textDecoration: 'none' }}>
                  <AnalyticEcommerce title="Approved" count={campaignStatsAll.approved} url="/masters/campaigns/?alertStatus=Approved" />
                </Link>
              </Grid>
              <Grid item xs={12} sm={6} md={4} lg={3}>
                <Link to="/masters/campaigns" style={{ textDecoration: 'none' }}>
                  <AnalyticEcommerce title="Live" count={campaignStatsAll.live} url="/masters/campaigns/?alertStatus=Live" />
                </Link>
              </Grid>
            </>
          )}
        </Grid>
      )}
      {/* <Grid item md={8} sx={{ display: { sm: 'none', md: 'block', lg: 'none' } }} /> */}
      {/* row 2 */}
      {/* <Grid item xs={12} md={7} lg={8}>
        <UniqueVisitorCard />
      </Grid>
      <Grid item xs={12} md={5} lg={4}>
        <Grid container alignItems="center" justifyContent="space-between">
          <Grid item>
            <Typography variant="h5">Income Overview</Typography>
          </Grid>
          <Grid item />
        </Grid>
        <MainCard sx={{ mt: 2 }} content={false}>
          <Box sx={{ p: 3, pb: 0 }}>
            <Stack spacing={2}>
              <Typography variant="h6" color="text.secondary">
                This Week Statistics
              </Typography>
              <Typography variant="h3">$7,650</Typography>
            </Stack>
          </Box>
          <MonthlyBarChart />
        </MainCard>
      </Grid> */}
      {/* row 3 */}
      {/* <Grid item xs={12} md={7} lg={8}>
        <Grid container alignItems="center" justifyContent="space-between">
          <Grid item>
            <Typography variant="h5">Recent Orders</Typography>
          </Grid>
          <Grid item />
        </Grid>
        <MainCard sx={{ mt: 2 }} content={false}>
          <OrdersTable />
        </MainCard>
      </Grid>
      <Grid item xs={12} md={5} lg={4}>
        <Grid container alignItems="center" justifyContent="space-between">
          <Grid item>
            <Typography variant="h5">Analytics Report</Typography>
          </Grid>
          <Grid item />
        </Grid>
        <MainCard sx={{ mt: 2 }} content={false}>
          <List sx={{ p: 0, '& .MuiListItemButton-root': { py: 2 } }}>
            <ListItemButton divider>
              <ListItemText primary="Company Finance Growth" />
              <Typography variant="h5">+45.14%</Typography>
            </ListItemButton>
            <ListItemButton divider>
              <ListItemText primary="Company Expenses Ratio" />
              <Typography variant="h5">0.58%</Typography>
            </ListItemButton>
            <ListItemButton>
              <ListItemText primary="Business Risk Cases" />
              <Typography variant="h5">Low</Typography>
            </ListItemButton>
          </List>
          <ReportAreaChart />
        </MainCard>
      </Grid> */}
      {/* row 4 */}
      {/* <Grid item xs={12} md={7} lg={8}>
        <SaleReportCard />
      </Grid>
      <Grid item xs={12} md={5} lg={4}>
        <Grid container alignItems="center" justifyContent="space-between">
          <Grid item>
            <Typography variant="h5">Transaction History</Typography>
          </Grid>
          <Grid item />
        </Grid>
        <MainCard sx={{ mt: 2 }} content={false}>
          <List
            component="nav"
            sx={{
              px: 0,
              py: 0,
              '& .MuiListItemButton-root': {
                py: 1.5,
                '& .MuiAvatar-root': avatarSX,
                '& .MuiListItemSecondaryAction-root': { ...actionSX, position: 'relative' }
              }
            }}
          >
            <ListItemButton divider>
              <ListItemAvatar>
                <Avatar sx={{ color: 'success.main', bgcolor: 'success.lighter' }}>
                  <GiftOutlined />
                </Avatar>
              </ListItemAvatar>
              <ListItemText primary={<Typography variant="subtitle1">Order #002434</Typography>} secondary="Today, 2:00 AM" />
              <ListItemSecondaryAction>
                <Stack alignItems="flex-end">
                  <Typography variant="subtitle1" noWrap>
                    + $1,430
                  </Typography>
                  <Typography variant="h6" color="secondary" noWrap>
                    78%
                  </Typography>
                </Stack>
              </ListItemSecondaryAction>
            </ListItemButton>
            <ListItemButton divider>
              <ListItemAvatar>
                <Avatar sx={{ color: 'primary.main', bgcolor: 'primary.lighter' }}>
                  <MessageOutlined />
                </Avatar>
              </ListItemAvatar>
              <ListItemText primary={<Typography variant="subtitle1">Order #984947</Typography>} secondary="5 August, 1:45 PM" />
              <ListItemSecondaryAction>
                <Stack alignItems="flex-end">
                  <Typography variant="subtitle1" noWrap>
                    + $302
                  </Typography>
                  <Typography variant="h6" color="secondary" noWrap>
                    8%
                  </Typography>
                </Stack>
              </ListItemSecondaryAction>
            </ListItemButton>
            <ListItemButton>
              <ListItemAvatar>
                <Avatar sx={{ color: 'error.main', bgcolor: 'error.lighter' }}>
                  <SettingOutlined />
                </Avatar>
              </ListItemAvatar>
              <ListItemText primary={<Typography variant="subtitle1">Order #988784</Typography>} secondary="7 hours ago" />
              <ListItemSecondaryAction>
                <Stack alignItems="flex-end">
                  <Typography variant="subtitle1" noWrap>
                    + $682
                  </Typography>
                  <Typography variant="h6" color="secondary" noWrap>
                    16%
                  </Typography>
                </Stack>
              </ListItemSecondaryAction>
            </ListItemButton>
          </List>
        </MainCard>
        <MainCard sx={{ mt: 2 }}>
          <Stack spacing={3}>
            <Grid container justifyContent="space-between" alignItems="center">
              <Grid item>
                <Stack>
                  <Typography variant="h5" noWrap>
                    Help & Support Chat
                  </Typography>
                  <Typography variant="caption" color="secondary" noWrap>
                    Typical replay within 5 min
                  </Typography>
                </Stack>
              </Grid>
              <Grid item>
                <AvatarGroup sx={{ '& .MuiAvatar-root': { width: 32, height: 32 } }}>
                  <Avatar alt="Remy Sharp" src={avatar1} />
                  <Avatar alt="Travis Howard" src={avatar2} />
                  <Avatar alt="Cindy Baker" src={avatar3} />
                  <Avatar alt="Agnes Walker" src={avatar4} />
                </AvatarGroup>
              </Grid>
            </Grid>
            <Button size="small" variant="contained" sx={{ textTransform: 'capitalize' }}>
              Need Help?
            </Button>
          </Stack>
        </MainCard>
      </Grid> */}
      <Dialog
        open={isDialogOpen}
        onClose={handleDialogClose}
        maxWidth="50%"
        PaperProps={{
          sx: {
            top: 0,
            position: 'absolute',
            borderRadius: '16px',
            width: '100%',
            maxWidth: '600px'
          }
        }}
      >
        <DialogTitle>Your Profile is Ready for Review</DialogTitle>
        <DialogContent>
          <DialogContentText>
            Your profile is 100% complete. You can submit it for review now if all the information is correct. If you want to recheck the
            details, click Cancel and manually submit later.
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleDialogClose} color="secondary">
            Cancel
          </Button>
          <Button onClick={handleSubmitReview} color="primary" variant="contained">
            Submit for Onboarding
          </Button>
        </DialogActions>
      </Dialog>
      <ToastContainer autoClose={4000} />
    </Fragment>
  );
}
