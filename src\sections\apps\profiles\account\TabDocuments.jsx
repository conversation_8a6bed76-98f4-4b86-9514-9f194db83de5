import React, { useState, useEffect, Fragment } from 'react';
import axios from 'axios';
import {
  <PERSON><PERSON>,
  Card,
  CardContent,
  DialogActions,
  DialogContent,
  Grid,
  TextField,
  Typography,
  Dialog,
  DialogTitle,
  Stack,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Tooltip,
  CircularProgress,
  Box
} from '@mui/material';
import { ToastContainer, toast } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import CustomReactTable from 'components/CustomerDataGrid';
import { getDocumentsMasterList, getNGODocumentsList, patchNgoDocumentsList } from 'pages/masters/apis/documents.service';
import { Formik } from 'formik';
import * as yup from 'yup';
import UploadSingleFile from 'components/third-party/dropzone/SingleFile';
import FormHelperText from '@mui/material/FormHelperText';
import { useNavigate, useParams } from 'react-router';
import { getConvertedFileName, getSessionStorageItem } from 'utils/permissionUtils';
import { addDocument, BASE_URL, getLoggedInNGOInfo, updateDocument } from '../profile.service';
import TableActions from 'components/TableActions';
import dayjs from 'dayjs';
import CustomerTableWithoutFilter from 'sections/apps/customer/CustomerTableWithoutFilter';
import IconButton from 'components/@extended/IconButton';
import { CheckCircleOutlined, CloseCircleOutlined, CloudUploadOutlined, EditOutlined, UndoOutlined } from '@ant-design/icons';
import { addNotifications } from 'pages/masters/apis/notification.service';

export default function TabDocuments() {
  const [isEditing, setIsEditing] = useState(false);
  const [currentDocument, setCurrentDocument] = useState(false);
  const { newNgoId } = useParams();
  const [loading, setLoading] = useState(true);

  const userInfo = getSessionStorageItem('user');
  const [documentMasterList, setDocumentMasterList] = useState([]);

  const [openDialog, setOpenDialog] = useState(false);
  const [documentType, setDocumentType] = useState(null);
  const [ngoDetails, setNgoDetails] = useState(null);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const [rejectionReason, setRejectionReason] = useState('');
  const [openRejectionDialog, setOpenRejectionDialog] = useState(false);
  const [selectedDocumentId, setSelectedDocumentId] = useState(null);

  const navigate = useNavigate();

  useEffect(() => {
    fetchngoDetails();
  }, []);

  const fetchDocuments = async (documentType) => {
    setLoading(true);
    try {
      const [documentList, ngoDocumentList] = await Promise.all([
        getDocumentsMasterList('NGO', documentType, 'DocumentsTab'),
        getNGODocumentsList(userInfo?.ngo_id ? userInfo?.ngo_id : newNgoId, 'DocumentsTab')
      ]);

      if (documentList.length > 0) {
        const mergedArray = documentList.map((item1) => {
          const match = ngoDocumentList.find((item2) => item2.documentId === item1.id);
          return { ...item1, ...match };
        });
        setDocumentMasterList(mergedArray);
        return;
      }
      setDocumentMasterList([]);
    } catch (error) {
      setDocumentMasterList([]);
      console.error('Failed to fetch documents:', error);
      toast.error('Failed to fetch documents');
    } finally {
      setLoading(false);
    }
  };

  const fetchngoDetails = async () => {
    setLoading(true);

    try {
      if (newNgoId) {
        const response = await getLoggedInNGOInfo(newNgoId);
        setNgoDetails({
          ...response,
          date_of_establishment: response?.date_of_establishment ? dayjs(response?.date_of_establishment) : null
        });
        fetchDocuments(response?.ngo_type);
        return;
      }
      const response = await getLoggedInNGOInfo(userInfo?.ngo_id);
      fetchDocuments(response?.ngo_type);
      setNgoDetails({
        ...response,
        date_of_establishment: response?.date_of_establishment ? dayjs(response?.date_of_establishment) : null
      });
    } catch (error) {
      console.error('Error fetching user details:', error);
      toast.error('Failed to fetch user details');
    } finally {
      setLoading(false);
    }
  };

  const openEditDialog = (document) => {
    setCurrentDocument(document);
    document?.fileName ? setIsEditing(true) : setIsEditing(false);
    setOpenDialog(true);
  };

  const handleCancelEdit = () => {
    setIsEditing(false);
    setCurrentDocument(null);
    setOpenDialog(false);
    setTimeout(() => {
      fetchDocuments(ngoDetails?.ngo_type);
    }, 2000);
  };

  const handleStatusUpdate = async (id, status) => {
    if (status === 'Rejected') {
      setSelectedDocumentId(id);
      setOpenRejectionDialog(true);
      return;
    }
    try {
      const payload = { approval_status: status };
      payload.ngo_id = userInfo?.ngo_id ? userInfo?.ngo_id : newNgoId;
      const response = await patchNgoDocumentsList(id, payload);

      if (response) {
        toast.success(`Document ${status === 'Pending' ? 'has been reset to pending' : status} successfully.`);
      } else {
        toast.error(`Failed to ${status.toLowerCase()} the document.`);
      }
      setTimeout(() => {
        fetchDocuments(ngoDetails?.ngo_type);
      }, 2000);
    } catch (error) {
      console.error(`Error updating document status:`, error);
      toast.error('An error occurred while updating the document status.');
    }
  };

  const handleReject = async () => {
    if (!rejectionReason.trim()) {
      toast.error('Please enter a reason for rejection.');
      return;
    }
    try {
      const payload = { approval_status: 'Rejected', rejection_reason: rejectionReason };
      payload.ngo_id = userInfo?.ngo_id ? userInfo?.ngo_id : newNgoId;

      const response = await patchNgoDocumentsList(selectedDocumentId, payload);

      const notificationPayload = {
        type_id: selectedDocumentId,
        sender_id: userInfo.id,
        description: rejectionReason,
        messageRead: 'no',
        type: 'documents',
        ngo_id: userInfo?.ngo_id ? userInfo?.ngo_id : newNgoId
      };
      const notificationResponse = await addNotifications(notificationPayload);

      if (response) {
        toast.success('Document Rejected successfully.');
      } else {
        toast.error('Failed to reject the document.');
      }

      if (notificationResponse.status) {
        toast.success('Notification sent successfully!');
        setRejectionReason('');
      } else {
        throw new Error('failed to to send notification');
      }

      setTimeout(() => {
        fetchDocuments(ngoDetails?.ngo_type);
      }, 1000);
    } catch (error) {
      console.error(`Error rejecting document:`, error);
      toast.error('An error occurred while rejecting the document.');
    } finally {
      setOpenRejectionDialog(false);
    }
  };

  if (loading) {
    return (
      <Box
        sx={{
          display: 'flex',
          flexDirection: 'column',
          justifyContent: 'center',
          alignItems: 'center'
        }}
      >
        <CircularProgress />
        <Typography sx={{ mt: 2 }}>Loading Your Documents....</Typography>
      </Box>
    );
  }

  return (
    <Card>
      <CardContent>
        <Grid container spacing={3}>
          {/* <Grid item xs={12}>
            <Typography variant="h5" gutterBottom>
              Mandatory Documents
            </Typography>
          </Grid> */}
          <Grid item xs={12}>
            <CustomerTableWithoutFilter
              data={documentMasterList}
              columns={[
                {
                  accessorKey: 'name',
                  header: 'Document Name',
                  cell: ({ cell }) => {
                    return cell.row.original.mandatory === 'yes' ? cell.row.original.name + ' *' : cell.row.original.name;
                  }
                },
                {
                  accessorKey: 'description',
                  header: 'Description'
                },
                {
                  accessorKey: 'fileName',
                  header: 'File',
                  cell: (cell) => {
                    return (
                      <Box>
                        <Stack direction="column" spacing={1}>
                          {/* Hide Upload button if approval_status is 'Approved' */}
                          {cell?.row?.original?.approval_status !== 'Approved' && (
                            <Tooltip title={cell?.row?.original?.fileName ? 'Edit' : 'Upload'}>
                              <IconButton color="primary" onClick={() => openEditDialog(cell.row.original)}>
                                <CloudUploadOutlined />
                              </IconButton>
                            </Tooltip>
                          )}

                          {cell?.row?.original?.fileName && (
                            <a target="_blank" href={`${BASE_URL}/fetchNGODocumentsPDF/${cell?.row?.original?.fileName}`}>
                              View
                            </a>
                          )}
                        </Stack>
                      </Box>
                    );
                  }
                },
                {
                  accessorKey: 'approval_status',
                  header: 'Approval Status',
                  cell: (cell) => {
                    const status = cell?.row?.original?.approval_status;
                    const id = cell?.row?.original?.documentId ? cell?.row?.original?.documentId : cell?.row?.original?.id;
                    const rejectionReason = cell?.row?.original?.rejection_reason; // Get rejection reason

                    let color = 'text.secondary';

                    if (status === 'Approved') {
                      color = 'success.main';
                    } else if (status === 'Rejected') {
                      color = 'error.main';
                    } else if (status === 'Pending') {
                      color = 'warning.main';
                    }

                    return (
                      <Box>
                        {newNgoId && status === 'Pending' ? (
                          <Fragment>
                            <Tooltip title="Approve">
                              <IconButton color="success" onClick={() => handleStatusUpdate(id, 'Approved')}>
                                <CheckCircleOutlined />
                              </IconButton>
                            </Tooltip>
                            <Tooltip title="Reject">
                              <IconButton color="error" onClick={() => handleStatusUpdate(id, 'Rejected')}>
                                <CloseCircleOutlined />
                              </IconButton>
                            </Tooltip>
                          </Fragment>
                        ) : (
                          <>
                            <Box display="flex" alignItems="center" gap={1}>
                              <Typography variant="body2" sx={{ color, fontWeight: 'bold' }}>
                                {status}
                              </Typography>
                              {newNgoId && (
                                <>
                                  {' '}
                                  {['Approved', 'Rejected'].includes(status) && (
                                    <Tooltip title="Reset Status">
                                      <IconButton size="large" color="info" onClick={() => handleStatusUpdate(id, 'Pending')}>
                                        <UndoOutlined />
                                      </IconButton>
                                    </Tooltip>
                                  )}
                                </>
                              )}
                            </Box>
                            {status === 'Rejected' && rejectionReason && (
                              <Typography
                                variant="body2"
                                sx={{
                                  color: 'error.dark',
                                  fontStyle: 'italic',
                                  mt: 0.5,
                                  whiteSpace: 'pre-line' // Ensures multi-line text displays correctly
                                }}
                              >
                                Reason : {rejectionReason}
                              </Typography>
                            )}
                          </>
                        )}
                      </Box>
                    );
                  }
                }
              ]}
              pagination={true}
              pageSize={5}
              filterable={true}
            />
          </Grid>
          <Grid container sx={{ display: 'flex', justifyContent: 'flex-end', alignItems: 'flex-end', gap: 2, marginTop: 2 }}>
            <Button
              variant="contained"
              color="primary"
              onClick={() => (newNgoId ? navigate(`/masters/ngos/edit/personal/${newNgoId}`) : navigate('/apps/profiles/account/personal'))}
            >
              Back
            </Button>

            <Button
              variant="contained"
              color="primary"
              onClick={() => (newNgoId ? navigate(`/masters/ngos/edit/socials/${newNgoId}`) : navigate('/apps/profiles/account/socials'))}
            >
              Next
            </Button>
          </Grid>
        </Grid>
      </CardContent>
      <ToastContainer autoClose={6000} />

      <Dialog open={openDialog} onClose={() => setOpenDialog(false)}>
        <Formik
          initialValues={{ files: null }}
          onSubmit={async (values) => {
            setIsSubmitting(true);

            // submit form
            let formData = new FormData();
            const convertedFileName = getConvertedFileName(values?.files[0]?.name);
            formData.append('fileName', `${convertedFileName}`);
            formData.append('file', values?.files[0], `${convertedFileName}`);
            formData.append('documentId', currentDocument?.documentId ? currentDocument?.documentId : currentDocument?.id);
            formData.append('ngo_id', userInfo?.ngo_id ? userInfo?.ngo_id : newNgoId);
            formData.append('approval_status', 'Pending');
            if (!currentDocument?.documentId) {
              const record = await addDocument(formData, 'DocumentsTab');
              if (record.status === false) {
                toast.error('File not uploaded');
              } else {
                toast.success('File uploaded successfully');
                // insert settings value

                setIsSubmitting(false);

                handleCancelEdit();
              }
            } else {
              const record = await updateDocument(currentDocument?.documentId, formData);
              if (record.status === false) {
                toast.error('File not uploaded');
              } else {
                setIsSubmitting(false);

                toast.success('File uploaded successfully');
                // insert settings value

                handleCancelEdit();
              }
            }
          }}
          validationSchema={yup.object().shape({
            files: yup.mixed().required('File is a required.')
          })}
        >
          {({ values, handleSubmit, setFieldValue, touched, errors }) => (
            <form onSubmit={handleSubmit}>
              <DialogTitle>{isEditing ? `Edit ${currentDocument?.name}` : `Add ${currentDocument?.name}`}</DialogTitle>
              <DialogContent>
                <Grid container spacing={3}>
                  <Grid item xs={12}>
                    <Stack spacing={1.5} alignItems="center">
                      <UploadSingleFile
                        setFieldValue={setFieldValue}
                        file={values.files}
                        error={touched.files && !!errors.files}
                        maxfileSize={parseInt(currentDocument?.size) * 1024 * 1024}
                      />
                      <Stack spacing={0}>
                        {/* <Typography align="center" variant="caption" color="secondary">
                          Allowed &#39;{currentDocument?.filetype}/*&#39;
                        </Typography> */}
                        {/* <Typography align="center" variant="caption" color="secondary">
                          *.png, *.jpeg, *.jpg, *.pdf
                        </Typography> */}
                      </Stack>
                    </Stack>
                    {touched.files && errors.files && (
                      <FormHelperText error id="standard-weight-helper-text-password-login">
                        {errors.files}
                      </FormHelperText>
                    )}
                  </Grid>
                </Grid>
              </DialogContent>
              <DialogActions>
                <Grid item xs={12}>
                  <Stack direction="row" justifyContent="flex-end" alignItems="center" spacing={2}>
                    <Button
                      color="error"
                      onClick={() => {
                        setFieldValue('files', null);
                        handleCancelEdit();
                      }}
                      disabled={isSubmitting}
                    >
                      Cancel
                    </Button>
                    <Button type="submit" variant="contained" disabled={isSubmitting}>
                      {isSubmitting ? <CircularProgress size={24} color="inherit" /> : 'Submit'}
                    </Button>
                  </Stack>
                </Grid>
                {/* <Button onClick={() => setOpenDialog(false)} color="primary">
                  Cancel
                </Button>
                <Button onClick={handleAddOrEdit} color="primary">
                  {isEditing ? 'Update' : 'Add'}
                </Button> */}
              </DialogActions>
            </form>
          )}
        </Formik>
      </Dialog>
      <Dialog
        open={openRejectionDialog}
        onClose={(event, reason) => {
          if (reason !== 'backdropClick' && reason !== 'escapeKeyDown') {
            setOpenRejectionDialog(false);
          }
        }}
        maxWidth="50%"
        PaperProps={{
          sx: {
            top: 0,
            position: 'absolute',
            borderRadius: '16px',
            width: '100%',
            maxWidth: '600px'
          }
        }}
      >
        <DialogTitle>Reject Document</DialogTitle>
        <DialogContent>
          <TextField
            label="Reason for Rejection"
            fullWidth
            multiline
            rows={3}
            value={rejectionReason}
            onChange={(e) => setRejectionReason(e.target.value)}
            sx={{ marginTop: 1 }}
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setOpenRejectionDialog(false)} color="error">
            Cancel
          </Button>
          <Button
            onClick={() => {
              handleReject();
            }}
            variant="contained"
            color="primary"
          >
            Reject
          </Button>
        </DialogActions>
      </Dialog>
    </Card>
  );
}
