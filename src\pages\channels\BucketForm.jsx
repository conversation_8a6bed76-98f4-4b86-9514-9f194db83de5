import React, { useEffect, useState } from 'react';
import { Grid, TextField, Button, Autocomplete, Typography, Box, CircularProgress, Stack, IconButton } from '@mui/material';
import { Formik, Form, FieldArray } from 'formik';
import * as Yup from 'yup';
import { toast } from 'react-toastify';

// Mocked API functions – Replace these with your actual services
import { CheckCircleOutlined, SearchOutlined } from '@ant-design/icons';
import { getAllCategories } from 'api/categories.service';
import MainCard from 'components/MainCard';
import { getCampaignsFromCategoryIds } from 'api/campaigns.service';
import { addBucketservice, getBucketById, getBucketItems, updateBucketservice } from 'pages/masters/apis/buckets.service';
import { useNavigate, useParams } from 'react-router';
import CustomCircularLoaded from 'components/CustomCircularLoaded';

const AllocationSchema = Yup.object().shape({
  name: Yup.string().required('Bucket name is required'),
  description: Yup.string().required('Bucket description is required'),
  categories: Yup.array().min(1, 'Select at least one category'),
  allocations: Yup.array()
    .of(
      Yup.object().shape({
        campaignId: Yup.number().required('Campaign ID is required'),
        percentage: Yup.number().min(0, 'Percentage must be at least 0').max(100, 'Percentage must be at most 100')
      })
    )
    .test('total-allocation', function (allocations) {
      const total = allocations.reduce((sum, curr) => sum + (curr.percentage || 0), 0);
      if (total < 100) return this.createError({ message: 'Total allocation is less than 100%' });
      if (total > 100) return this.createError({ message: 'Total allocation exceeds 100%' });
      return true;
    })
});

const CampaignAllocationForm = () => {
  const [categories, setCategories] = useState([]);
  const [campaignOptions, setCampaignOptions] = useState([]);
  const [loadingCampaigns, setLoadingCampaigns] = useState(false);
  const [loading, setLoading] = useState(false);
  const navigate = useNavigate();

  const { bucketId } = useParams();
  const isEditMode = Boolean(bucketId);

  useEffect(() => {
    const fetchCategories = async () => {
      const result = await getAllCategories();
      setCategories(result);
    };
    fetchCategories();
  }, []);

  return (
    <MainCard title={<Typography sx={{ fontSize: '0.9rem', fontWeight: 'bold' }}>{false ? 'Edit Bucket' : 'Add Bucket'}</Typography>}>
      <Box p={3}>
        <Formik
          initialValues={{
            name: '',
            description: '',
            categories: [],
            allocations: []
          }}
          validationSchema={AllocationSchema}
          onSubmit={async (values) => {
            try {
              setLoading(true);
              const filteredAllocations = values.allocations.filter((a) => a.percentage > 0);
              const submissionData = {
                name: values.name, // Include name in the submission
                description: values.description, // Include description in the submission
                categories: values.categories,
                allocations: filteredAllocations
              };
              if (bucketId) {
                const campaignAllocation = await updateBucketservice(bucketId, submissionData);
                if (campaignAllocation) {
                  toast.success(`Bucket ${bucketId ? 'updated' : 'submitted'} successfully!`);
                }
              } else {
                const campaignAllocation = await addBucketservice(submissionData);
                if (campaignAllocation) {
                  toast.success(`Bucket ${bucketId ? 'updated' : 'submitted'} successfully!`);
                }
              }
              setLoading(false);
              setTimeout(() => {
                navigate('/channels/buckets');
              }, 1000);
            } catch (err) {
              toast.error('Failed to submit allocations');
            }
          }}
        >
          {({ values, setFieldValue, errors, touched }) => {
            const handleFetchCampaigns = async () => {
              setLoadingCampaigns(true);
              try {
                const categoryIds = values.categories.map((cat) => cat.id);
                const campaigns = await getCampaignsFromCategoryIds(categoryIds); // Replace with API call if needed4
                setCampaignOptions(campaigns);
              } catch (err) {
                toast.error('Failed to fetch campaigns');
              } finally {
                setLoadingCampaigns(false);
              }
            };
            useEffect(() => {
              const initializeForm = async () => {
                if (!isEditMode) return;

                try {
                  const [bucket, items] = await Promise.all([getBucketById(bucketId), getBucketItems(bucketId)]);

                  // Set name and description
                  setFieldValue('name', bucket.name);
                  setFieldValue('description', bucket.description);

                  // Get unique category IDs
                  const uniqueCategories = [...new Map(items.map((item) => [item.category_id, item])).values()].map(
                    (entry) => entry.category_id
                  );

                  // Map to category objects using your `getAllCategories` result
                  const categoryObjects = categories.filter((c) => uniqueCategories.includes(c.id));
                  setFieldValue('categories', categoryObjects);

                  // Fetch campaigns by category
                  const campaigns = await getCampaignsFromCategoryIds(uniqueCategories);
                  setCampaignOptions(campaigns);

                  // Map campaigns and items to allocations
                  const allocations = items.map((item) => {
                    const campaign = campaigns.find((c) => c.id === item.campaign_id);
                    return {
                      campaignId: item.campaign_id,
                      ngoId: item.ngo_id,
                      categoryId: item.category_id,
                      percentage: parseFloat(item.percentage),
                      campaignName: campaign?.name || ''
                    };
                  });

                  setFieldValue('allocations', allocations);
                } catch (err) {
                  toast.error('Failed to load bucket data');
                }
              };

              initializeForm();
            }, [bucketId, categories]);
            return (
              <Form>
                <CustomCircularLoaded open={loading} />
                <Grid container spacing={3}>
                  <Grid item xs={12}>
                    <Typography fontWeight="bold" sx={{ marginBottom: 2 }}>
                      1. Enter Bucket Name
                    </Typography>
                    <TextField
                      fullWidth
                      label="Bucket Name"
                      required
                      value={values.name}
                      onChange={(e) => setFieldValue('name', e.target.value)}
                      error={touched.name && !!errors.name}
                      helperText={touched.name && errors.name}
                    />
                  </Grid>

                  {/* 2. Description Field */}
                  <Grid item xs={12}>
                    <Typography fontWeight="bold" sx={{ marginBottom: 2 }}>
                      2. Enter Bucket Description
                    </Typography>
                    <TextField
                      fullWidth
                      multiline
                      required
                      rows={4}
                      label="Bucket Description"
                      value={values.description}
                      onChange={(e) => setFieldValue('description', e.target.value)}
                      error={touched.description && !!errors.description}
                      helperText={touched.description && errors.description}
                    />
                  </Grid>
                  {/* 1. Category Selection */}
                  <Grid item xs={12}>
                    <Typography fontWeight="bold" sx={{ marginBottom: 2 }}>
                      3. Select Campaign Categories
                    </Typography>

                    <Autocomplete
                      multiple
                      options={categories}
                      getOptionLabel={(option) => option.name}
                      value={values.categories}
                      onChange={(_, value) => setFieldValue('categories', value)}
                      isOptionEqualToValue={(option, value) => option.id === value.id}
                      renderInput={(params) => (
                        <TextField
                          {...params}
                          label="Select Categories"
                          error={touched.categories && !!errors.categories}
                          helperText={touched.categories && errors.categories}
                        />
                      )}
                    />
                  </Grid>

                  {/* 2. Find Campaigns */}
                  <Grid item xs={12}>
                    <Button
                      variant="contained"
                      startIcon={<SearchOutlined />}
                      disabled={values.categories.length === 0 || loadingCampaigns}
                      onClick={handleFetchCampaigns}
                    >
                      {loadingCampaigns ? <CircularProgress size={20} color="secondary" /> : 'Find Campaigns'}
                    </Button>
                  </Grid>

                  {/* 3. Campaign Allocation */}
                  <Grid item xs={12}>
                    <Typography fontWeight="bold" mb={1}>
                      4. Select Campaigns & Allocate Budget
                    </Typography>

                    <Box
                      mt={1}
                      p={2}
                      border={2}
                      borderColor="primary.main"
                      borderRadius={2}
                      display="flex"
                      alignItems="center"
                      justifyContent="space-between"
                      bgcolor="#f5faff"
                    >
                      <Stack direction="row" spacing={1} alignItems="center">
                        <Typography variant="h6" fontWeight="bold">
                          Total Allocation:
                        </Typography>
                        <Typography variant="h6" color="primary" fontWeight="bold">
                          {values.allocations.reduce((sum, a) => sum + (a.percentage || 0), 0)}%
                        </Typography>
                      </Stack>

                      <Stack direction="row" spacing={1} alignItems="center" justifyContent="flex-end">
                        {(() => {
                          const total = values.allocations.reduce((sum, a) => sum + (a.percentage || 0), 0);
                          if (total === 100) {
                            return (
                              <Stack direction="row" spacing={1} alignItems="center">
                                <CheckCircleOutlined style={{ color: 'green' }} />
                                <Typography color="success.main" fontWeight="bold">
                                  Perfect
                                </Typography>
                              </Stack>
                            );
                          } else if (total < 100) {
                            return (
                              <Typography color="error" fontWeight="medium">
                                Total is less than 100%
                              </Typography>
                            );
                          } else {
                            return (
                              <Typography color="error" fontWeight="medium">
                                Total exceeds 100%
                              </Typography>
                            );
                          }
                        })()}
                      </Stack>
                    </Box>
                  </Grid>

                  <Grid item xs={12}>
                    <FieldArray name="allocations">
                      {({ push, remove, form }) => (
                        <Grid container spacing={3}>
                          {campaignOptions?.map((campaign) => {
                            const isSelected = values.allocations.some((a) => a.campaignId === campaign.id);
                            const selectedIndex = values.allocations.findIndex((a) => a.campaignId === campaign.id);
                            const allocation = values.allocations[selectedIndex];

                            return (
                              <Grid item xs={12} md={4} key={campaign.id}>
                                <Box
                                  sx={{
                                    border: 2,
                                    borderColor: isSelected ? 'success.main' : 'divider',
                                    borderRadius: 2,
                                    p: 2.5,
                                    // bgcolor: isSelected ? 'primary.lighter' : 'background.paper',
                                    boxShadow: isSelected ? '0 4px 8px rgba(0, 0, 0, 0.1)' : 'none',
                                    transition: 'all 0.3s ease',
                                    height: '100%',
                                    display: 'flex',
                                    flexDirection: 'column',
                                    '&:hover': {
                                      boxShadow: '0 6px 12px rgba(0, 0, 0, 0.08)',
                                      transform: 'translateY(-2px)'
                                    }
                                  }}
                                >
                                  <Stack spacing={2} sx={{ height: '100%' }}>
                                    <Box>
                                      <Typography variant="h5" fontWeight="600" color={isSelected ? 'primary.main' : 'text.primary'}>
                                        {campaign?.name}
                                      </Typography>
                                      <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
                                        {campaign?.description}
                                      </Typography>
                                    </Box>

                                    <Box sx={{ my: 1.5 }}>
                                      <TextField
                                        fullWidth
                                        label="Allocation Percentage"
                                        disabled={!isSelected}
                                        value={allocation?.percentage ?? ''}
                                        onChange={(e) => {
                                          const input = e.target.value;
                                          const numberPattern = /^\d{0,3}(\.\d{0,2})?$/; // up to 3 digits and optional 2 decimals
                                          if (input === '' || numberPattern.test(input)) {
                                            setFieldValue(
                                              `allocations[${selectedIndex}].percentage`,
                                              input === '' ? null : parseFloat(input)
                                            );
                                          }
                                        }}
                                        onKeyDown={(e) => {
                                          const allowedKeys = ['Backspace', 'Tab', 'ArrowLeft', 'ArrowRight', 'Delete'];
                                          if (!/[0-9.]/.test(e.key) && !allowedKeys.includes(e.key)) {
                                            e.preventDefault();
                                          }
                                        }}
                                        InputProps={{
                                          endAdornment: (
                                            <Typography variant="body2" color="text.secondary" sx={{ ml: 1 }}>
                                              %
                                            </Typography>
                                          ),
                                          sx: {
                                            borderRadius: 1.5,
                                            backgroundColor: !isSelected ? 'action.disabledBackground' : undefined,
                                            color: !isSelected ? 'text.disabled' : undefined
                                          }
                                        }}
                                        variant="outlined"
                                        size="small"
                                      />
                                    </Box>

                                    <Box sx={{ mt: 'auto', pt: 1 }}>
                                      <Button
                                        fullWidth
                                        size="medium"
                                        variant={isSelected ? 'outlined' : 'contained'}
                                        color={isSelected ? 'error' : 'primary'}
                                        startIcon={isSelected ? <span>-</span> : <span>+</span>}
                                        onClick={() => {
                                          if (isSelected) remove(selectedIndex);
                                          else
                                            push({
                                              campaignId: campaign.id,
                                              ngoId: campaign?.ngo_id,
                                              categoryId: campaign?.category_id,
                                              percentage: null,
                                              campaignName: campaign.name
                                            });
                                        }}
                                        sx={{
                                          borderRadius: 1.5,
                                          textTransform: 'none',
                                          fontWeight: 500,
                                          boxShadow: isSelected ? 'none' : 2
                                        }}
                                      >
                                        {isSelected ? 'Remove Campaign' : 'Select Campaign'}
                                      </Button>
                                    </Box>
                                  </Stack>
                                </Box>
                              </Grid>
                            );
                          })}
                        </Grid>
                      )}
                    </FieldArray>
                  </Grid>

                  {/* 4. Submit */}
                  <Grid item xs={12}>
                    <Button type="submit" variant="contained" color="primary">
                      {bucketId ? 'Update Bucket' : ' Submit Bucket'}
                    </Button>
                  </Grid>
                </Grid>
              </Form>
            );
          }}
        </Formik>
      </Box>
    </MainCard>
  );
};

export default CampaignAllocationForm;
