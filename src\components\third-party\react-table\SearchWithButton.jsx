import PropTypes from 'prop-types';
import { useEffect, useState } from 'react';

// material-ui
import OutlinedInput from '@mui/material/OutlinedInput';

// assets
import SearchOutlined from '@ant-design/icons/SearchOutlined';
import { Box, Button } from '@mui/material';

// ==============================|| FILTER - INPUT ||============================== //

export default function SearchWithButton({
  value: initialValue,
  onFilterChange,
  debounce = 1000,
  size,
  setGlobalFilter,
  startAdornment = <SearchOutlined />,
  ...props
}) {
  const [value, setValue] = useState(initialValue);
  const [searchTerm, setSearchTerm] = useState(initialValue);

  const handleInputChange = (event) => setSearchTerm(event.target.value);

  const handleKeyDown = (event) => {
    if (event.key === 'Enter') {
      onFilterChange(searchTerm);
    }
  };

//   useEffect(() => {
//     setValue(initialValue);
//   }, [initialValue]);

//   useEffect(() => {
//     const timeout = setTimeout(() => {
//       onFilterChange(value);
//     }, debounce);

//     return () => clearTimeout(timeout);
//     // eslint-disable-next-line
//   }, [value]);

  return (
    <Box
      sx={{
        display: 'flex',
        alignItems: 'center',
        gap: 1 // Adds spacing between input and button
      }}
    >
      <OutlinedInput
        {...props}
        value={searchTerm}
        onChange={handleInputChange}
        onKeyDown={handleKeyDown}
        size='small'
        sx={{
          minWidth: 200,
          '& .MuiOutlinedInput-notchedOutline': { borderColor: '#D9D9D9' } // Custom border color
        }}
        // {...(startAdornment && { startAdornment })}
        {...(size && { size })}
      />
      <Button
        variant="contained"
        color="primary"
        size='small'
        onClick={() => onFilterChange(searchTerm)}
      >
        Search
      </Button>
    </Box>
  );
}

SearchWithButton.propTypes = {
  value: PropTypes.oneOfType([PropTypes.number, PropTypes.string]),
  onFilterChange: PropTypes.func,
  debounce: PropTypes.number,
  size: PropTypes.any,
  startAdornment: PropTypes.any,
  SearchOutlined: PropTypes.any,
  props: PropTypes.any
};
