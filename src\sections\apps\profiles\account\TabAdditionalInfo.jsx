import React, { useState, useEffect } from 'react';
import { useParams } from 'react-router';
import { useNavigate } from 'react-router';
import { Alert, Box, Button, Card, CardContent, CircularProgress, Grid, TextField, Typography } from '@mui/material';
import { ToastContainer, toast } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import { getLoggedInNGOInfo, patchNGOInfo } from '../profile.service';
import { getSessionStorageItem } from 'utils/permissionUtils';
import UnsavedChangesDialog from './UnsavedChangesDialog';

export default function TabAdditionalInfo() {
  const [isEditing, setIsEditing] = useState(true);
  const { newNgoId } = useParams();
  const userInfo = getSessionStorageItem('user');
  const [isLoading, setIsLoading] = useState(true);

  //trace unsaved changes
  const [isValuesChanged, setValuesChanged] = useState(false);
  const [showUnsavedDialog, setShowUnsavedDialog] = useState(false);
  const [pendingNavigationPath, setPendingNavigationPath] = useState(null);

  const [additionalDetails, setAdditionalDetails] = useState({
    instagram_url: '',
    facebook_url: '',
    youtube_url: '',
    twitter_url: ''
  });

  const navigate = useNavigate();

  useEffect(() => {
    fetchNGODetails();
  }, []);

  const fetchNGODetails = async () => {
    setIsLoading(true);

    try {
      if (newNgoId) {
        const response = await getLoggedInNGOInfo(newNgoId);
        setAdditionalDetails({ ...response });
        return;
      }
      const response = await getLoggedInNGOInfo(userInfo?.ngo_id);
      setAdditionalDetails({
        ...response
      });
    } catch (error) {
      console.error('Error fetching ngo details:', error);
      toast.error('Failed to fetch ngo details');
    } finally {
      setIsLoading(false);
    }
  };

  const handleInputChange = (e) => {
    setValuesChanged(true);

    const { name, value } = e.target;
    setAdditionalDetails((prev) => ({ ...prev, [name]: value }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    try {
      const ngoId = newNgoId || userInfo?.ngo_id;
      await patchNGOInfo(ngoId, additionalDetails, 'Socials');
      toast.success('Socials details updated successfully');
      setValuesChanged(false);
    } catch (error) {
      console.error('Error updating social details:', error);
      toast.error('Failed to update social details');
    }
  };

  const handleEditClick = () => setIsEditing(true);
  const handleCancelEdit = () => {
    setIsEditing(false);
    fetchNGODetails();
  };

  if (isLoading) {
    return (
      <Box
        sx={{
          display: 'flex',
          flexDirection: 'column',
          justifyContent: 'center',
          alignItems: 'center'
        }}
      >
        <CircularProgress />
        <Typography sx={{ mt: 2 }}>Loading Social Information....</Typography>
      </Box>
    );
  }

  return (
    <Card>
      <CardContent>
        <Grid container spacing={3}>
          {/* <Grid item xs={12}>
            <Typography variant="h5" gutterBottom>
              Additional Information
            </Typography>
          </Grid> */}
          <Grid item xs={12}>
            <form onSubmit={handleSubmit}>
              <Grid container spacing={2}>
                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    label="Instagram URL"
                    name="instagram_url"
                    value={additionalDetails.instagram_url}
                    inputProps={{ maxLength: 180 }}
                    onChange={handleInputChange}
                    disabled={!isEditing}
                  />
                </Grid>
                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    label="Facebook URL"
                    name="facebook_url"
                    value={additionalDetails.facebook_url}
                    onChange={handleInputChange}
                    inputProps={{ maxLength: 180 }}
                    disabled={!isEditing}
                  />
                </Grid>
                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    label="YouTube URL"
                    name="youtube_url"
                    inputProps={{ maxLength: 180 }}
                    value={additionalDetails.youtube_url}
                    onChange={handleInputChange}
                    disabled={!isEditing}
                  />
                </Grid>
                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    label="Twitter URL"
                    name="twitter_url"
                    value={additionalDetails.twitter_url}
                    inputProps={{ maxLength: 180 }}
                    onChange={handleInputChange}
                    disabled={!isEditing}
                  />
                </Grid>
                <Grid item xs={12}>
                  <Grid
                    container
                    sx={{
                      display: 'flex',
                      justifyContent: 'space-between', // Distribute buttons with space between them
                      alignItems: 'center', // Vertically center the buttons
                      gap: 2,
                      marginTop: 2
                    }}
                  >
                    {/* Save Button aligned to the left */}
                    <Grid item>
                      <Button type="submit" variant="contained" color="primary" sx={{ mr: 2 }}>
                        Save
                      </Button>
                    </Grid>

                    {/* Back Button and Next Button aligned to the right */}
                    <Grid item>
                      <Button
                        variant="contained"
                        color="primary"
                        onClick={() => {
                          const path = newNgoId ? `/masters/ngos/edit/documents/${newNgoId}` : '/apps/profiles/account/documents';

                          if (isValuesChanged) {
                            setPendingNavigationPath(path);
                            setShowUnsavedDialog(true);
                            return;
                          }
                          navigate(path);
                        }}
                        sx={{ mr: 1 }} // Space between Back and Next buttons
                      >
                        Back
                      </Button>

                      <Button
                        variant="contained"
                        color="primary"
                        onClick={() => {
                          const path = newNgoId ? `/masters/ngos/edit/bank-accounts/${newNgoId}` : '/apps/profiles/account/bank-accounts';

                          if (isValuesChanged) {
                            setPendingNavigationPath(path);
                            setShowUnsavedDialog(true);
                            return;
                          }
                          navigate(path);
                        }}
                      >
                        Next
                      </Button>
                    </Grid>
                  </Grid>
                </Grid>
              </Grid>
            </form>

            <Grid container sx={{ display: 'flex', justifyContent: 'flex-end', alignItems: 'flex-end', gap: 2, marginTop: 2 }}>
              <Alert color="error">
                Note: If you click 'Next' or 'Back', any unsaved changes will be discarded. Be sure to click 'Save' first.
              </Alert>{' '}
            </Grid>
          </Grid>
        </Grid>
      </CardContent>

      <UnsavedChangesDialog
        showUnsavedDialog={showUnsavedDialog}
        setShowUnsavedDialog={setShowUnsavedDialog}
        pendingNavigationPath={pendingNavigationPath}
      />
      <ToastContainer autoClose={6000} />
    </Card>
  );
}
