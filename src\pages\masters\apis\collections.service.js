// addCollectionService,
// deleteCollectionService,
// fetchCollectionsService,
// updateCollectionService

import axiosServices from 'utils/axios_node';

const API_BASE_URL = `${import.meta.env.VITE_APP_APPLICATION_API_URL}/api`;

export const fetchCollectionsService = async () => {
  const response = await axiosServices.get(`${API_BASE_URL}/collections`);
  return response.data;
};

export const addCollectionService = async (collection) => {
  const response = await axiosServices.post(`${API_BASE_URL}/collections`, collection);
  return response.data;
};

export const updateCollectionService = async (id, collection) => {
  const response = await axiosServices.put(`${API_BASE_URL}/collections/${id}`, collection);
  return response.data;
};

export const deleteCollectionService = async (id) => {
  const response = await axiosServices.delete(`${API_BASE_URL}/collections/${id}`);
  return response.data;
};
