import React, { useState, useEffect } from 'react';
import { useParams } from 'react-router';
import { useNavigate } from 'react-router';
import axios from 'axios';
import {
  Autocomplete,
  Button,
  Card,
  CardContent,
  FormControl,
  Grid,
  TextField,
  Typography,
  Box,
  CircularProgress,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Tooltip,
  Alert,
  InputAdornment
} from '@mui/material';
import { ToastContainer, toast } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import {
  deleteNGOCategories,
  getLoggedInNGOInfo,
  insertCategories,
  patchNGOInfo,
  updateNGO,
  updateUsingPatchNGO
} from '../profile.service';
import { DatePicker, LocalizationProvider, MobileDatePicker } from '@mui/x-date-pickers';
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';
import dayjs from 'dayjs';
import { getAllCategories, getNGOBasedCategories, getSubcategoriesByCategoryId } from 'api/categories.service';
import { getSessionStorageItem } from 'utils/permissionUtils';
import { getQuestionsList } from 'api/questions.service';
import { InfoCircleOutlined } from '@ant-design/icons';
import UnsavedChangesDialog from './UnsavedChangesDialog';

export default function TabOrganizationInfo() {
  const [isEditing, setIsEditing] = useState(true);
  const { newNgoId } = useParams();
  const userInfo = getSessionStorageItem('user');
  const [organizationDetails, setOrganizationDetails] = useState({
    vision: '',
    mission: '',
    tagline: '',
    about_us: '',
    website_url: '',
    darpan_id: '',
    registration_details: '',
    date_of_establishment: ''
  });
  const MAX_LENGTH = 500;
  const [isDescriptionOpen, setDescriptionOpen] = useState(false);
  const [isCategoruDescription, setIsCategoryDescription] = useState(false);
  const [categories, setCategories] = useState([]);
  const [selectedCategory, setSelectedCategory] = useState(null);
  const [subcategories, setSubcategories] = useState([]);
  const [selectedSubcategories, setSelectedSubcategories] = useState([]);
  const [loadingSubcategories, setLoadingSubcategories] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [questions, setQuestions] = useState([]);
  const [filteredFields, setFilteredFields] = useState([]);
  const [registrationDetailsInitialized, setRegistrationDetailsInitialized] = useState(false);
  const navigate = useNavigate();

  //unsaved data change
  const [isValuesChanged, setValuesChanged] = useState(false);
  const [showUnsavedDialog, setShowUnsavedDialog] = useState(false);
  const [pendingNavigationPath, setPendingNavigationPath] = useState(null);

  useEffect(() => {
    const initializeData = async () => {
      setIsLoading(true);
      try {
        await Promise.all([fetchNGODetails(), fetchCategories(), fetchQuestions()]);
      } catch (error) {
        console.error('Error initializing data:', error);
      } finally {
        setIsLoading(false);
      }
    };
    initializeData();
  }, []);

  useEffect(() => {
    const getQuestion = () => {
      if (organizationDetails.ngo_type && questions.length > 0) {
        const matchedFields = questions.filter((question) =>
          question.ngo_type
            .split(',')
            .map((type) => type.trim())
            .includes(organizationDetails.ngo_type)
        );
        setFilteredFields(matchedFields);
        // registrationFieldsMerge(organizationDetails, matchedFields)
      }
    };
    getQuestion();
  }, [organizationDetails.ngo_type, questions]);

  useEffect(() => {
    if (categories.length > 0) fetchSavedData();
  }, [categories]);

  useEffect(() => {
    if (filteredFields.length > 0 && organizationDetails?.id) {
      registrationFieldsMerge(organizationDetails, filteredFields);
    }
  }, [filteredFields, organizationDetails]);

  const registrationFieldsMerge = (orgDetails, filteredMatchedFields) => {
    const allowedFieldNames = filteredMatchedFields.map((field) => field.name);
    const mergedRegistrationDetails = allowedFieldNames.reduce((acc, fieldName) => {
      acc[fieldName] = orgDetails.registration_details?.[fieldName] || '';
      return acc;
    }, {});

    if (JSON.stringify(organizationDetails.registration_details || {}) !== JSON.stringify(mergedRegistrationDetails)) {
      setOrganizationDetails((prev) => ({
        ...prev,
        registration_details: mergedRegistrationDetails
      }));
      setRegistrationDetailsInitialized(false); // Mark as uninitialized to trigger re-check
    } else {
      setRegistrationDetailsInitialized(true); // Mark as initialized
    }
  };

  const fetchNGODetails = async () => {
    try {
      const response = newNgoId ? await getLoggedInNGOInfo(newNgoId) : await getLoggedInNGOInfo(userInfo?.ngo_id);

      const regDetails = response?.registration_details ? JSON.parse(response.registration_details) : {};
      const parsedDetails = typeof regDetails == 'string' ? JSON.parse(regDetails) : regDetails;
      setOrganizationDetails({
        ...response,
        registration_details: parsedDetails,
        date_of_establishment: response?.date_of_establishment ? dayjs(response?.date_of_establishment) : null
      });
    } catch (error) {
      console.error('Error fetching ngo details:', error);
      toast.error('Failed to fetch ngo details');
    }
  };

  const renderRegistrationFields = () => {
    if (!registrationDetailsInitialized) {
      return;
    }
    return Object.keys(organizationDetails.registration_details).map((key) => (
      <Grid item xs={12} sm={6} key={key}>
        <TextField
          fullWidth
          label={key.replace(/_/g, ' ').toWellFormed()}
          name={key}
          value={organizationDetails.registration_details[key]}
          onChange={(e) => {
            setValuesChanged(true);

            const { name, value } = e.target;
            setOrganizationDetails((prev) => ({
              ...prev,
              registration_details: { ...prev.registration_details, [name]: value }
            }));
          }}
          required
          disabled={!isEditing}
        />
      </Grid>
    ));
  };

  const fetchCategories = async () => {
    try {
      const response = await getAllCategories('AboutNGOInfo');
      setCategories(response);
    } catch (error) {
      console.error('Failed to fetch categories:', error);
    }
  };
  const fetchSubcategories = async (categoryId) => {
    setLoadingSubcategories(true);
    try {
      const response = await getSubcategoriesByCategoryId(categoryId);
      setSubcategories(response);
    } catch (error) {
      console.error('Failed to fetch subcategories:', error);
      toast.error('Failed to fetch subcategories');
    } finally {
      setLoadingSubcategories(false);
    }
  };
  const fetchQuestions = async () => {
    try {
      const response = await getQuestionsList();
      setQuestions(response);
    } catch (error) {
      console.error('Error fetching questions:', error);
      toast.error('Failed to fetch questions');
    }
  };

  const fetchSavedData = async () => {
    try {
      const ngoId = newNgoId || userInfo?.ngo_id;
      const categoryResponse = await getNGOBasedCategories(ngoId, 'AboutNGOInfo');

      if (!categoryResponse || categoryResponse.length === 0) {
        return;
      }

      const savedCategories = categoryResponse.map((saved) => categories.find((cat) => cat.id === saved.category_id));
      const selectedCategory = savedCategories[0];
      setSelectedCategory(selectedCategory);

      if (selectedCategory) {
        const subcategoryResponse = await getSubcategoriesByCategoryId(selectedCategory.id);
        setSubcategories(subcategoryResponse);

        const savedSubcategories = categoryResponse
          .filter((saved) => saved.category_id === selectedCategory.id)
          .flatMap((saved) => saved.subCategories.split(',').map((sub) => subcategoryResponse.find((subcat) => subcat.name === sub.trim())))
          .filter(Boolean);
        setSelectedSubcategories(savedSubcategories);
      }
    } catch (error) {
      console.error('Failed to fetch saved data:', error);
      toast.error('Failed to fetch saved categories');
    }
  };
  const handleCategoryChange = (event, value) => {
    setValuesChanged(true);

    setSelectedCategory(value);
    setSelectedSubcategories([]);
    if (value) fetchSubcategories(value.id);
  };

  const handleInputChange = (e) => {
    setValuesChanged(true);

    const { name, value } = e.target;
    setOrganizationDetails((prev) => ({ ...prev, [name]: value }));
  };

  const handleChange = (e) => {
    setValuesChanged(true);

    let input = e.target.value.replace(/[^a-zA-Z0-9]/g, ''); // Allow only alphanumeric characters

    const name = e.target.name;
    let formatted = '';

    // Format the input based on length
    if (input.length <= 2) {
      formatted = input;
    } else if (input.length <= 6) {
      formatted = `${input.slice(0, 2)}/${input.slice(2)}`;
    } else {
      formatted = `${input.slice(0, 2)}/${input.slice(2, 6)}/${input.slice(6, 13)}`;
    }

    // Update the state with the formatted value
    setOrganizationDetails((prev) => ({ ...prev, [name]: formatted }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    const darpanIdRegex = /^[A-Za-z0-9]{2}\/[A-Za-z0-9]{4}\/\d{7}$/;
    if (!darpanIdRegex.test(organizationDetails?.darpan_id)) {
      toast.error('Invalid Darpan ID. Format should be XX/YYYY/NNNNNNN');
      return;
    }

    try {
      const ngoId = newNgoId || userInfo?.ngo_id;
      const updatedDetails = {
        ...organizationDetails,
        registration_details: JSON.stringify(organizationDetails.registration_details),
        date_of_establishment: organizationDetails?.date_of_establishment
          ? dayjs(organizationDetails?.date_of_establishment).format('YYYY-MM-DD')
          : null,
        pageName: 'AboutNGOInfo'
      };

      if (!newNgoId) {
        await patchNGOInfo(userInfo?.ngo_id, updatedDetails, 'AboutNGOInfo');
      } else {
        await updateUsingPatchNGO(newNgoId, updatedDetails, 'AboutNGOInfo');
      }

      // Save categories and subcategories
      await deleteNGOCategories(ngoId); // Ensure no duplicates
      await insertCategories(
        {
          ngo_id: ngoId,
          category_id: selectedCategory.id,
          subCategories: selectedSubcategories.map((sub) => sub.name).join(',')
        },
        'AboutNGOInfo'
      );

      toast.success('About NGO details updated successfully');
      setValuesChanged(false);

      // setIsEditing(false);
      setTimeout(() => {
        if (newNgoId) {
          navigate(`/masters/ngos/edit/documents/${newNgoId}`);
        }
      }, 1000);
    } catch (error) {
      console.error('Error updating NGO details or categories:', error);
      toast.error(error?.message);
    }
  };

  const handleEditClick = (e) => {
    e.preventDefault();
    setIsEditing(true);
  };

  const handleCancelEdit = () => {
    setIsEditing(false);
    fetchNGODetails();
  };

  if (isLoading) {
    return (
      <Box
        sx={{
          display: 'flex',
          flexDirection: 'column',
          justifyContent: 'center',
          alignItems: 'center'
        }}
      >
        <CircularProgress />
        <Typography sx={{ mt: 2 }}>Loading About Information....</Typography>
      </Box>
    );
  }

  return (
    <Card>
      <CardContent>
        <Grid container spacing={3}>
          <Grid item xs={12}>
            <form onSubmit={handleSubmit}>
              <Grid container spacing={2}>
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    multiline
                    rows={2}
                    label="Vision"
                    name="vision"
                    value={organizationDetails.vision}
                    onChange={handleInputChange}
                    disabled={!isEditing}
                    required
                    inputProps={{ maxLength: 500 }}
                    InputProps={{
                      sx: {
                        fontSize: '14px',
                        lineHeight: 1.5,
                        padding: '14px',
                        backgroundColor: !isEditing ? 'grey.100' : 'white'
                      }
                    }}
                    InputLabelProps={{
                      sx: { fontSize: '14px', color: 'text.secondary' }
                    }}
                    sx={{
                      borderRadius: '8px',
                      boxShadow: isEditing ? '0px 4px 10px rgba(0, 0, 0, 0.1)' : 'none',
                      '& .MuiOutlinedInput-root': {
                        '&:hover fieldset': {
                          borderColor: isEditing ? 'primary.main' : 'grey.400'
                        },
                        '&.Mui-focused fieldset': {
                          borderColor: 'primary.main'
                        }
                      }
                    }}
                  />
                  <Typography variant="caption" sx={{ display: 'block', textAlign: 'right', color: 'gray', mt: 0.5 }}>
                    {organizationDetails?.vision?.length ? MAX_LENGTH - organizationDetails?.vision?.length : MAX_LENGTH} characters left
                  </Typography>
                </Grid>
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    multiline
                    rows={2}
                    label="Mission"
                    name="mission"
                    value={organizationDetails.mission}
                    onChange={handleInputChange}
                    inputProps={{ maxLength: 500 }}
                    disabled={!isEditing}
                    required
                    InputProps={{
                      sx: {
                        fontSize: '14px',
                        lineHeight: 1.5,
                        padding: '14px',
                        backgroundColor: !isEditing ? 'grey.100' : 'white'
                      }
                    }}
                    InputLabelProps={{
                      sx: { fontSize: '14px', color: 'text.secondary' }
                    }}
                    sx={{
                      borderRadius: '8px',
                      boxShadow: isEditing ? '0px 4px 10px rgba(0, 0, 0, 0.1)' : 'none',
                      '& .MuiOutlinedInput-root': {
                        '&:hover fieldset': {
                          borderColor: isEditing ? 'primary.main' : 'grey.400'
                        },
                        '&.Mui-focused fieldset': {
                          borderColor: 'primary.main'
                        }
                      }
                    }}
                  />
                  <Typography variant="caption" sx={{ display: 'block', textAlign: 'right', color: 'gray', mt: 0.5 }}>
                    {organizationDetails?.mission?.length ? MAX_LENGTH - organizationDetails?.mission?.length : MAX_LENGTH} characters left
                  </Typography>
                </Grid>
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    multiline
                    rows={2}
                    label="Tag Line"
                    name="tagline"
                    value={organizationDetails.tagline}
                    onChange={handleInputChange}
                    inputProps={{ maxLength: 45 }}
                    disabled={!isEditing}
                    required
                    InputProps={{
                      sx: {
                        fontSize: '14px',
                        lineHeight: 1.5,
                        padding: '14px',
                        backgroundColor: !isEditing ? 'grey.100' : 'white'
                      }
                    }}
                    InputLabelProps={{
                      sx: { fontSize: '14px', color: 'text.secondary' }
                    }}
                    sx={{
                      borderRadius: '8px',
                      boxShadow: isEditing ? '0px 4px 10px rgba(0, 0, 0, 0.1)' : 'none',
                      '& .MuiOutlinedInput-root': {
                        '&:hover fieldset': {
                          borderColor: isEditing ? 'primary.main' : 'grey.400'
                        },
                        '&.Mui-focused fieldset': {
                          borderColor: 'primary.main'
                        }
                      }
                    }}
                  />
                  <Typography variant="caption" sx={{ display: 'block', textAlign: 'right', color: 'gray', mt: 0.5 }}>
                    {organizationDetails?.tagline?.length ? 45 - organizationDetails?.tagline?.length : 45} characters left
                  </Typography>
                </Grid>
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    multiline
                    rows={2}
                    label="About US"
                    name="about_us"
                    value={organizationDetails.about_us}
                    onChange={handleInputChange}
                    disabled={!isEditing}
                    required
                    inputProps={{ maxLength: 500 }}
                    InputProps={{
                      sx: {
                        fontSize: '14px',
                        lineHeight: 1.5,
                        padding: '14px',
                        backgroundColor: !isEditing ? 'grey.100' : 'white'
                      }
                    }}
                    InputLabelProps={{
                      sx: { fontSize: '14px', color: 'text.secondary' }
                    }}
                    sx={{
                      borderRadius: '8px',
                      boxShadow: isEditing ? '0px 4px 10px rgba(0, 0, 0, 0.1)' : 'none',
                      '& .MuiOutlinedInput-root': {
                        '&:hover fieldset': {
                          borderColor: isEditing ? 'primary.main' : 'grey.400'
                        },
                        '&.Mui-focused fieldset': {
                          borderColor: 'primary.main'
                        }
                      }
                    }}
                  />
                  <Typography variant="caption" sx={{ display: 'block', textAlign: 'right', color: 'gray', mt: 0.5 }}>
                    {organizationDetails?.about_us?.length ? MAX_LENGTH - organizationDetails?.about_us?.length : MAX_LENGTH} characters
                    left
                  </Typography>
                </Grid>
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    label="Website URL"
                    name="website_url"
                    value={organizationDetails.website_url}
                    onChange={handleInputChange}
                    disabled={!isEditing}
                    inputProps={{ maxLength: 180 }}
                  />
                </Grid>
                <Grid item xs={6}>
                  <TextField
                    fullWidth
                    label="Darpan ID"
                    name="darpan_id"
                    value={organizationDetails.darpan_id} // Ensure this is formatted correctly
                    onChange={handleChange}
                    placeholder="XX/YYYY/1111111"
                    inputProps={{ maxLength: 30 }} // Optional: Can limit length further
                  />
                  {/* <p>Current Value: {inputValue}</p> */}
                </Grid>
                <Grid item xs={12} sm={6}>
                  <FormControl fullWidth>
                    <LocalizationProvider dateAdapter={AdapterDayjs}>
                      {/* <DatePicker
                        format="DD/MM/YYYY"
                        value={organizationDetails?.date_of_establishment}
                        onChange={(newValue) => {
                          setOrganizationDetails((prev) => ({ ...prev, date_of_establishment: newValue }));
                        }}
                        label="Date of Establishment *"
                        disabled={!isEditing}
                        maxDate={dayjs()}
                        renderInput={(params) => <TextField {...params} fullWidth />}
                      /> */}
                      <MobileDatePicker
                        format="DD/MM/YYYY"
                        value={organizationDetails?.date_of_establishment}
                        onChange={(newValue) => {
                          setValuesChanged(true);

                          setOrganizationDetails((prev) => ({ ...prev, date_of_establishment: newValue }));
                        }}
                        label="Date of Establishment *"
                        required
                        disabled={!isEditing}
                        maxDate={dayjs()}
                        renderInput={(params) => <TextField {...params} fullWidth />}
                      />
                    </LocalizationProvider>
                  </FormControl>
                </Grid>

                {renderRegistrationFields()}

                <Grid item xs={12} container spacing={2} alignItems="center">
                  {/* Category Selection */}
                  <Grid item xs={12} md={6}>
                    <Autocomplete
                      options={categories}
                      getOptionLabel={(option) => option.name}
                      value={selectedCategory}
                      sx={{ paddingRight: 0 }}
                      onChange={handleCategoryChange}
                      renderInput={(params) => (
                        <TextField
                          required
                          sx={{ paddingRight: 0 }}
                          {...params}
                          label="Select category"
                          InputProps={{
                            ...params.InputProps,
                            endAdornment: selectedCategory?.description && (
                              <InputAdornment position="end">
                                <Tooltip title="View description">
                                  <IconButton onClick={() => setDescriptionOpen(true)} color="primary">
                                    <InfoCircleOutlined />
                                  </IconButton>
                                </Tooltip>
                              </InputAdornment>
                            )
                          }}
                        />
                      )}
                      disabled={!isEditing}
                    />
                  </Grid>

                  {/* Subcategory Selection */}
                  {selectedCategory && (
                    <Grid item xs={12} md={6}>
                      <Autocomplete
                        multiple
                        options={subcategories}
                        getOptionLabel={(option) => option.name}
                        value={selectedSubcategories}
                        onChange={(event, value) => {
                          setValuesChanged(true);

                          setSelectedSubcategories(value);
                        }}
                        isOptionEqualToValue={(option, value) => option.id === value.id}
                        sx={{ marginTop: 0 }}
                        renderInput={(params) => (
                          <TextField
                            {...params}
                            label={loadingSubcategories ? 'Loading Subcategories...' : 'Select subcategories'}
                            placeholder="Subcategories"
                          />
                        )}
                        disabled={!isEditing || !selectedCategory || loadingSubcategories}
                      />
                    </Grid>
                  )}
                </Grid>

                {/* <Grid item xs={12}>
                  {isEditing ? (
                    <>
                      <Button type="submit" variant="contained" color="primary" sx={{ mr: 2 }}>
                        Save
                      </Button>
                    </>
                  ) : (
                    <Button type="button" variant="contained" color="primary" onClick={handleEditClick}>
                      Edit Info
                    </Button>
                  )}
                </Grid> */}
                <Grid item xs={12}>
                  <Grid
                    container
                    sx={{
                      display: 'flex',
                      justifyContent: 'space-between', // Distribute buttons with space between them
                      alignItems: 'center', // Vertically center the buttons
                      gap: 2,
                      marginTop: 2
                    }}
                  >
                    {/* Save Button aligned to the left */}
                    <Grid item>
                      <Button type="submit" variant="contained" color="primary" sx={{ mr: 2 }}>
                        Save
                      </Button>
                    </Grid>

                    {/* Back and Next buttons aligned to the right */}
                    <Grid item>
                      <Button
                        variant="contained"
                        color="primary"
                        type="button"
                        onClick={() => {
                          const path = newNgoId ? `/masters/ngos/edit/basic/${newNgoId}` : '/apps/profiles/account/basic';

                          if (isValuesChanged) {
                            setPendingNavigationPath(path);
                            setShowUnsavedDialog(true);
                            return;
                          }
                          navigate(path);
                        }}
                        sx={{ mr: 1 }}
                      >
                        Back
                      </Button>

                      <Button
                        variant="contained"
                        color="primary"
                        type="button"
                        onClick={() => {
                          const path = newNgoId ? `/masters/ngos/edit/documents/${newNgoId}` : '/apps/profiles/account/documents';

                          if (isValuesChanged) {
                            setPendingNavigationPath(path); // Store path before showing dialog
                            setShowUnsavedDialog(true);
                            return;
                          }
                          navigate(path);
                        }}
                      >
                        Next
                      </Button>
                    </Grid>
                  </Grid>
                </Grid>
              </Grid>
            </form>

            <Grid container sx={{ display: 'flex', justifyContent: 'flex-end', alignItems: 'flex-end', gap: 2, marginTop: 2 }}>
              <Alert color="error">
                Note: If you click 'Next' or 'Back', any unsaved changes will be discarded. Be sure to click 'Save' first.
              </Alert>
            </Grid>
          </Grid>
        </Grid>
        <Dialog
          open={isDescriptionOpen}
          onClose={() => setDescriptionOpen(false)}
          maxWidth="sm"
          PaperProps={{
            sx: {
              top: 0,
              position: 'absolute',
              borderRadius: '16px',
              width: '100%',
              maxWidth: '600px',
              padding: '16px'
            }
          }}
        >
          <DialogTitle>{selectedCategory?.name} Description</DialogTitle>
          <DialogContent>
            <p style={{ margin: 0 }}>{selectedCategory?.description}</p>
          </DialogContent>

          <DialogActions>
            <Button
              onClick={() => setDescriptionOpen(false)}
              color="primary"
              variant="contained"
              sx={{ borderRadius: '8px', textTransform: 'none', padding: '6px 16px' }}
            >
              Close
            </Button>
          </DialogActions>
        </Dialog>
      </CardContent>

      <UnsavedChangesDialog
        showUnsavedDialog={showUnsavedDialog}
        setShowUnsavedDialog={setShowUnsavedDialog}
        pendingNavigationPath={pendingNavigationPath}
      />
      <ToastContainer autoClose={6000} />
    </Card>
  );
}
