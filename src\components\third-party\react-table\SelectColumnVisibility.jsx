import PropTypes from 'prop-types';

// material-ui
import Checkbox from '@mui/material/Checkbox';
import FormControl from '@mui/material/FormControl';
import ListItemText from '@mui/material/ListItemText';
import MenuItem from '@mui/material/MenuItem';
import OutlinedInput from '@mui/material/OutlinedInput';
import Select from '@mui/material/Select';
import Typography from '@mui/material/Typography';

const ITEM_HEIGHT = 48;
const ITEM_PADDING_TOP = 8;
const MenuProps = { PaperProps: { style: { maxHeight: ITEM_HEIGHT * 4.5 + ITEM_PADDING_TOP, width: 200 } } };

// ==============================|| COLUMN VISIBILITY - SELECT ||============================== //

export default function SelectColumnVisibility({
  getVisibleLeafColumns,
  getIsAllColumnsVisible,
  getToggleAllColumnsVisibilityHandler,
  getAllColumns
}) {
  return (
    <div sx={{ width: 200 }}>
      <Select
        id="column-hiding"
        multiple
        displayEmpty
        size="small"
        value={getVisibleLeafColumns()}
        input={<OutlinedInput id="select-column-hiding" placeholder="select column" />}
        renderValue={() => {
          if (getIsAllColumnsVisible()) {
            return <Typography variant="body1">All columns</Typography>;
          }

          if (getVisibleLeafColumns().length === 0) {
            return <Typography variant="body1">All columns hidden</Typography>;
          }

          return (
            <Typography style={{ fontSize: '12px' }} variant="body1">
              {getVisibleLeafColumns().length} column(s)
            </Typography>
          );
        }}
        MenuProps={MenuProps}
        className="text-color-grey change_svg"
      >
        <MenuItem value="all" onClick={getToggleAllColumnsVisibilityHandler()}>
          <Checkbox checked={getIsAllColumnsVisible()} />
          <ListItemText primary="All Columns" />
        </MenuItem>
        {getAllColumns().map(
          (column) =>
            // @ts-ignore
            column.columnDef.accessorKey && (
              <MenuItem key={column.id} value={column.id} onClick={column.getToggleVisibilityHandler()}>
                <Checkbox checked={column.getIsVisible()} />
                <ListItemText primary={column.columnDef.header} />
              </MenuItem>
            )
        )}
      </Select>
    </div>
  );
}

SelectColumnVisibility.propTypes = {
  getVisibleLeafColumns: PropTypes.func,
  getIsAllColumnsVisible: PropTypes.func,
  getToggleAllColumnsVisibilityHandler: PropTypes.func,
  getAllColumns: PropTypes.func
};
