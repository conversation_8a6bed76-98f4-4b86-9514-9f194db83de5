import React, { useEffect, useState } from 'react';
import {
  Box,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Typography,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  Paper,
  TextField,
  Button,
  Tooltip,
  IconButton
} from '@mui/material';
import { fetchRSVPDetails } from '../apis/rvsp.service';
import { toast } from 'react-toastify';
import { UploadOutlined } from '@ant-design/icons';

const RSVPDetailsComponent = ({ campaignId }) => {
  const [rsvpValue, setRsvpValue] = useState('all');
  const [rsvpDetails, setRsvpDetails] = useState([]);
  const [filteredDetails, setFilteredDetails] = useState([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);

  useEffect(() => {
    fetchDetails();
  }, [rsvpValue]);

  useEffect(() => {
    applyFilters();
  }, [searchQuery, rsvpDetails]);

  const fetchDetails = async () => {
    try {
      const data = await fetchRSVPDetails(campaignId, rsvpValue === 'all' ? undefined : rsvpValue);
      setRsvpDetails(data);
      setFilteredDetails(data);
    } catch (error) {
      toast.error('Error fetching RSVP details.');
    }
  };

  const applyFilters = () => {
    let filtered = rsvpDetails;

    if (rsvpValue !== 'all') {
      filtered = filtered.filter((detail) => detail.rsvp_value === rsvpValue);
    }

    if (searchQuery) {
      filtered = filtered.filter(
        (detail) =>
          detail.userInfo.fullname.toLowerCase().includes(searchQuery.toLowerCase()) ||
          detail.userInfo.skills.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }

    setFilteredDetails(filtered);
  };

  const handleSearchChange = (e) => {
    setSearchQuery(e.target.value);
  };

  const handleChangePage = (event, newPage) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  const exportToCSV = () => {
    const csvRows = [];
    // Add header row
    csvRows.push(['RSVP Value', 'User Name', 'Gender', 'Age', 'Skills']);

    // Add data rows
    filteredDetails.forEach((detail) => {
      csvRows.push([
        detail.rsvp_value === 'yes' ? 'Yes' : 'No',
        detail.userInfo.fullname,
        detail.userInfo.gender,
        detail.userInfo.age,
        detail.userInfo.skills
      ]);
    });

    // Create CSV content
    const csvContent = csvRows.map((row) => row.join(',')).join('\n');

    // Create a Blob and trigger the download
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    link.href = URL.createObjectURL(blob);
    link.download = 'rsvp_details.csv';
    link.click();
  };

  return (
    <Box>
      <Box sx={{ display: 'flex', gap: 2, mb: 2, mt: 2, alignItems: 'center' }}>
        <FormControl sx={{ width: '300px' }} variant="outlined">
          <InputLabel>RSVP Filter</InputLabel>
          <Select value={rsvpValue} onChange={(e) => setRsvpValue(e.target.value)} label="RSVP Filter">
            <MenuItem value="all">All</MenuItem>
            <MenuItem value="yes">Yes</MenuItem>
            <MenuItem value="no">No</MenuItem>
          </Select>
        </FormControl>
        <TextField
          sx={{ flex: 1 }}
          variant="outlined"
          placeholder="Search by name or skills"
          value={searchQuery}
          onChange={handleSearchChange}
        />
        <Tooltip title="Export to CSV">
          <IconButton size="large" onClick={exportToCSV} color="primary">
            <UploadOutlined />
          </IconButton>
        </Tooltip>
      </Box>

      <Box>
        {filteredDetails.length === 0 ? (
          <Typography>No RSVP details available.</Typography>
        ) : (
          <>
            <TableContainer component={Paper}>
              <Table>
                <TableHead>
                  <TableRow sx={{ backgroundColor: '#f5f5f5' }}>
                    <TableCell>
                      <Typography variant="subtitle2" sx={{ fontWeight: 'bold', color: '#555' }}>
                        RSVP Value
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Typography variant="subtitle2" sx={{ fontWeight: 'bold', color: '#555' }}>
                        User Name
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Typography variant="subtitle2" sx={{ fontWeight: 'bold', color: '#555' }}>
                        Gender
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Typography variant="subtitle2" sx={{ fontWeight: 'bold', color: '#555' }}>
                        Age
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Typography variant="subtitle2" sx={{ fontWeight: 'bold', color: '#555' }}>
                        Skills
                      </Typography>
                    </TableCell>
                  </TableRow>
                </TableHead>

                <TableBody>
                  {filteredDetails.slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage).map((detail) => (
                    <TableRow key={detail.id}>
                      <TableCell>{detail.rsvp_value === 'yes' ? 'Yes' : 'No'}</TableCell>
                      <TableCell>{detail.userInfo.fullname}</TableCell>
                      <TableCell>{detail.userInfo.gender}</TableCell>
                      <TableCell>{detail.userInfo.age}</TableCell>
                      <TableCell>{detail.userInfo.skills}</TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
            <TablePagination
              component="div"
              count={filteredDetails.length}
              page={page}
              onPageChange={handleChangePage}
              rowsPerPage={rowsPerPage}
              onRowsPerPageChange={handleChangeRowsPerPage}
            />
          </>
        )}
      </Box>
    </Box>
  );
};

export default RSVPDetailsComponent;
