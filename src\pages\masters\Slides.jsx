import React, { useEffect, useState } from 'react';
import {
  <PERSON>ton,
  <PERSON>alog,
  <PERSON>alogA<PERSON>,
  DialogContent,
  DialogTitle,
  TextField,
  Grid,
  FormHelperText,
  Stack,
  CircularProgress,
  Box,
  FormControl,
  InputLabel,
  Select,
  MenuItem
} from '@mui/material';
import { toast, ToastContainer } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import CustomerTable from 'sections/apps/customer/CustomerTable';
import { getConvertedFileName, getUserPermissions } from 'utils/permissionUtils';
import useAuth from 'hooks/useAuth';
import { addSlideService, deleteSlideService, fetchSlidesService, updateSlideService } from './apis/slides.service';
import { Formik } from 'formik';
import * as yup from 'yup';
import TableActions from 'components/TableActions';
import CustomProductTable from 'sections/apps/customer/CustomProductTable';
import UploadSingleFile from 'components/third-party/dropzone/SingleFile';

export default function SlidesTable() {
  const { user } = useAuth();
  const [slides, setSlides] = useState([]);
  const [openDialog, setOpenDialog] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  const [currentSlide, setCurrentSlide] = useState({
    title: '',
    image: '',
    text: '',
    buttonText: '',
    link: '',
    showActionButton: 'yes',
    status: 'Active'
  });

  const permissions = getUserPermissions(user);
  const canAdd = permissions?.Add?.Slides || true;
  const canEdit = permissions?.Edit?.Slides || true;
  const canDelete = permissions?.Delete?.Slides || true;

  useEffect(() => {
    fetchSlides();
  }, []);

  const fetchSlides = async () => {
    setIsLoading(true);

    try {
      const data = await fetchSlidesService();
      setSlides(data);
    } catch (error) {
      console.error('Failed to fetch slides:', error);
      toast.error('Failed to fetch slides');
    } finally {
      setIsLoading(false);
    }
  };

  const handleAddOrEdit = async (values) => {
    let formData = new FormData();
    if (!isEditing && !values.files) {
      toast.error('Please add an image');
      return;
    }

    if (values.files && values.files.length > 0) {
      const convertedFileName = getConvertedFileName(values.files[0].name);
      if (isEditing) {
        delete values.imageName;
      }
      formData.append('imageName', convertedFileName);
      formData.append('file', values.files[0], convertedFileName);
    }
    Object.keys(values).forEach((key) => {
      if (key !== 'files') {
        formData.append(key, values[key]);
      }
    });
    try {
      if (isEditing) {
        await updateSlideService(values.id, formData);
        toast.success('Slide updated successfully!');
      } else {
        await addSlideService(formData);
        toast.success('Slide added successfully!');
      }
      setOpenDialog(false);
      fetchSlides();
    } catch (error) {
      console.error('Failed to save slide:', error);
      toast.error(error.response?.data?.message || 'Failed to save slide');
    }
  };

  const handleDelete = async (id) => {
    if (!canDelete) {
      toast.error('You do not have permission to delete slides.');
      return;
    }
    if (window.confirm('Are you sure you want to delete this slide?')) {
      try {
        await deleteSlideService(id);
        toast.success('Slide deleted successfully!');
        fetchSlides();
      } catch (error) {
        console.error('Failed to delete slide:', error);
        toast.error('Failed to delete slide');
      }
    }
  };

  const openAddDialog = () => {
    if (!canAdd) {
      toast.error('You do not have permission to add slides.');
      return;
    }
    setCurrentSlide({ id: 0, title: '', image: '', text: '', buttonText: '', link: '' });
    setOpenDialog(true);
    setIsEditing(false);
  };

  const openEditDialog = (slide) => {
    if (!canEdit) {
      toast.error('You do not have permission to edit slides.');
      return;
    }
    setCurrentSlide(slide);
    setIsEditing(true);
    setOpenDialog(true);
  };

  const columns = [
    { accessorKey: 'image', header: 'Image', showByDefault: true },
    { accessorKey: 'title', header: 'Title', showByDefault: true },
    { accessorKey: 'text', header: 'Text', showByDefault: true },
    { accessorKey: 'buttonText', header: 'Action Button Text', showByDefault: true },
    { accessorKey: 'showActionButton', header: 'Show Action Button', showByDefault: true },
    { accessorKey: 'link', header: 'Link', showByDefault: true },
    { accessorKey: 'status', header: 'Status', showByDefault: true },

    {
      id: 'actions',
      header: 'Action',
      cell: (cell) => <TableActions handleEditClick={openEditDialog} cell={cell} handleDeleteClick={handleDelete} />
    }
  ];

  return (
    <div>
      {isLoading ? (
        <Box
          sx={{
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'center',
            textAlign: 'center'
          }}
        >
          <CircularProgress size={40} color="primary" />
          <Box sx={{ marginTop: 2, fontSize: '16px', color: 'text.secondary' }}>Loading your Slides data...</Box>
        </Box>
      ) : (
        <CustomProductTable data={slides} columns={columns} modalToggler={openAddDialog} category="Slide" statusList={[]} />
      )}

      <Dialog
        open={openDialog}
        onClose={(event, reason) => {
          if (reason !== 'backdropClick' && reason !== 'escapeKeyDown') {
            setOpenDialog(false);
          }
        }}
        maxWidth="50%"
        PaperProps={{
          sx: {
            top: 0,
            position: 'absolute',
            borderRadius: '16px',
            width: '100%',
            maxWidth: '600px'
          }
        }}
      >
        <Formik
          initialValues={{ ...currentSlide }}
          validationSchema={yup.object().shape({
            title: yup.string().required('Title is required'),
            text: yup.string().required('Text is required'),
            buttonText: yup.string().optional(),
            link: yup.string().optional(),
            showActionButton: yup.string().required('Please select any one option'),
            status: yup.string().required('Status is required')
          })}
          onSubmit={async (values) => {
            await handleAddOrEdit(values);
          }}
        >
          {({ values, setFieldValue, handleSubmit, handleChange, handleBlur, touched, errors }) => (
            <form autoComplete="off" onSubmit={handleSubmit}>
              <DialogTitle>{isEditing ? 'Edit Slide' : 'Add Slide'}</DialogTitle>
              <DialogContent>
                <Grid container spacing={2} marginTop={'1px'}>
                  <Grid item xs={12}>
                    <TextField
                      label="Title"
                      fullWidth
                      variant="outlined"
                      name="title"
                      onChange={handleChange}
                      onBlur={handleBlur}
                      value={values.title}
                      error={Boolean(touched.title && errors.title)}
                      helperText={touched.title && errors.title}
                    />
                  </Grid>
                  {/* <Grid item xs={12}>
                    <TextField
                      label="Image URL"
                      fullWidth
                      variant="outlined"
                      name="image"
                      onChange={handleChange}
                      onBlur={handleBlur}
                      value={values.image}
                      error={Boolean(touched.image && errors.image)}
                      helperText={touched.image && errors.image}
                    />
                  </Grid> */}
                  <Grid item xs={12}>
                    <TextField
                      label="Text"
                      fullWidth
                      variant="outlined"
                      name="text"
                      onChange={handleChange}
                      onBlur={handleBlur}
                      value={values.text}
                      error={Boolean(touched.text && errors.text)}
                      helperText={touched.text && errors.text}
                    />
                  </Grid>
                  <Grid item xs={12}>
                    <TextField
                      label="Button Text"
                      fullWidth
                      variant="outlined"
                      name="buttonText"
                      onChange={handleChange}
                      onBlur={handleBlur}
                      value={values.buttonText}
                      error={Boolean(touched.buttonText && errors.buttonText)}
                      helperText={touched.buttonText && errors.buttonText}
                    />
                  </Grid>
                  <Grid item xs={12}>
                    <FormControl required fullWidth variant="outlined">
                      <InputLabel>Show Action Button</InputLabel>
                      <Select value={values.showActionButton} onChange={handleChange} label="Show Action Button" name="showActionButton">
                        <MenuItem value="yes">Yes</MenuItem>
                        <MenuItem value="no">No</MenuItem>
                      </Select>
                      {touched.showActionButton && errors.showActionButton && <FormHelperText>{errors.showActionButton}</FormHelperText>}
                    </FormControl>
                  </Grid>
                  <Grid item xs={12}>
                    <TextField
                      label="Link (Optional)"
                      fullWidth
                      variant="outlined"
                      name="link"
                      onChange={handleChange}
                      onBlur={handleBlur}
                      value={values.link}
                      error={Boolean(touched.link && errors.link)}
                      helperText={touched.link && errors.link}
                    />
                  </Grid>
                  <Grid item xs={12}>
                    <FormControl required fullWidth variant="outlined">
                      <InputLabel>Status</InputLabel>
                      <Select value={values.status} onChange={handleChange} label="Status" name="status">
                        <MenuItem value="Active">Active</MenuItem>
                        <MenuItem value="Inactive">Inactive</MenuItem>
                      </Select>
                      {touched.status && errors.status && <FormHelperText>{errors.status}</FormHelperText>}
                    </FormControl>
                  </Grid>

                  <Grid item xs={12}>
                    <Stack spacing={1.5} alignItems="center">
                      <UploadSingleFile setFieldValue={setFieldValue} file={values.files} error={touched.files && !!errors.files} />
                    </Stack>
                    {touched.files && errors.files && <FormHelperText error>{errors.files}</FormHelperText>}
                  </Grid>
                </Grid>
              </DialogContent>
              <DialogActions>
                <Button onClick={() => setOpenDialog(false)} color="primary">
                  Cancel
                </Button>
                <Button type="submit" color="primary">
                  {isEditing ? 'Update' : 'Add'}
                </Button>
              </DialogActions>
            </form>
          )}
        </Formik>
      </Dialog>

      <ToastContainer />
    </div>
  );
}
