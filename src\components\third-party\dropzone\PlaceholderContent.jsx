import PropTypes from 'prop-types';
// material-ui
import Typography from '@mui/material/Typography';
import Stack from '@mui/material/Stack';
import CardMedia from '@mui/material/CardMedia';

// project-import
import { DropzopType } from 'config';

// assets
import UploadCover from 'assets/images/upload/upload.svg';
import CameraOutlined from '@ant-design/icons/CameraOutlined';

// ==============================|| UPLOAD - PLACEHOLDER ||============================== //

export default function PlaceholderContent({ type }) {
  return (
    <>
      {type !== DropzopType.STANDARD && (
        <Stack
          spacing={2}
          alignItems="center"
          justifyContent="center"
          direction={{ xs: 'column', md: 'row' }}
          sx={{ width: 1, textAlign: { xs: 'center', md: 'left' } }}
        >
          <CardMedia component="img" image={UploadCover} sx={{ width: 150 }} />
          <Stack sx={{ p: 3 }} spacing={1}>
            <Typography variant="h5">AcrossDrag & Drop or Choose a File</Typography>

            <Typography color="secondary">Simply drag and drop your files here, or click 'Browse' to select from your device.</Typography>
            <Typography variant="caption" color="secondary">
            Supported file types: *.png, *.jpeg, *.jpg, *.pdf
            </Typography>
          </Stack>
        </Stack>
      )}
      {type === DropzopType.STANDARD && (
        <Stack alignItems="center" justifyContent="center" sx={{ height: 1 }}>
          <CameraOutlined style={{ fontSize: '32px' }} />
        </Stack>
      )}
    </>
  );
}

PlaceholderContent.propTypes = { type: PropTypes.any };
