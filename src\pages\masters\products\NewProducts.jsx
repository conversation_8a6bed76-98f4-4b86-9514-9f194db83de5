import React, { useCallback, useEffect, useState } from 'react';
import {
  Button,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Stack,
  Divider,
  Grid,
  CircularProgress,
  Box,
  Tabs,
  Tab,
  Tooltip,
  IconButton
} from '@mui/material';
import { toast, ToastContainer } from 'react-toastify';
import { API_BASE_URL } from 'api/categories.service';
import TableActions from 'components/TableActions';
import ProductCard from 'components/cards/e-commerce/ProductCard';
import { ClockCircleOutlined, PlusOutlined, StopOutlined } from '@ant-design/icons';

import dayjs from 'dayjs';
import useAuth from 'hooks/useAuth';
import { deleteProduct, getProducts, patchProduct } from './product.service';
import CustomProductTable from 'sections/apps/customer/CustomProductTable';

import { getNGOSBySearchTerm } from 'api/campaigns.service';
import MainCard from 'components/MainCard';
import { BASE_URL } from 'sections/apps/profiles/profile.service';
import { useNavigate } from 'react-router-dom';
import { Link } from 'react-router-dom';

const Products = () => {
  const { user } = useAuth();
  const [products, setProducts] = useState([]);
  const [loading, setLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [globalFilter, setGlobalFilter] = useState('');

  //pagination
  const [totalCount, setTotalCount] = useState(0);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const navigate = useNavigate();

  const [viewMode, setViewMode] = useState('table');

  const [ngoList, setNgoList] = useState([]);

  useEffect(() => {
    fetchProducts();
  }, [currentPage, pageSize, globalFilter]);

  const fetchProducts = async () => {
    setIsLoading(true);
    try {
      const response = await getProducts(user?.ngo_id, currentPage, pageSize, globalFilter);

      setProducts(response.products);
      setTotalCount(response?.totalCount);
    } catch (error) {
      toast.error('Failed to fetch Products');
    } finally {
      setIsLoading(false);
    }
  };

  const fetchNgos = useCallback(async (query) => {
    if (query.length < 4) return;
    setLoading(true);
    try {
      const response = await getNGOSBySearchTerm(query, 'Verified');
      setNgoList(response);
    } catch (error) {
      console.error('Error fetching NGOs:', error);
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    const debounceFetch = setTimeout(fetchNgos(searchTerm), 500); // Debounce the API call

    return () => clearTimeout(debounceFetch); // Cleanup
  }, [searchTerm]);

  const handleDelete = async (id) => {
    const confirmDelete = window.confirm('Are you sure you want to delete this product?');
    if (confirmDelete) {
      try {
        await deleteProduct(id);
        toast.success('Product deleted successfully!');
        fetchProducts();
      } catch (error) {
        toast.error('Failed to delete product');
      }
    }
  };

  const handleEditClick = (id) => {
    navigate(`/masters/products/edit/${id}`);
  };
  const handleAddClick = () => {
    navigate(`/masters/products/add`);
  };

  const columns = [
    { accessorKey: 'product_image', header: 'Product Image', showByDefault: true },
    {
      accessorKey: 'name',
      header: 'Name',
      showByDefault: true,
      cell: ({ cell }) => {
        return (
          <Link className="ngo-link" to={`/masters/products/edit/${cell.row.original.id}`}>
            {cell.row.original.name}
          </Link>
        );
      }
    },
    { accessorKey: 'description', header: 'Description', showByDefault: true },
    { accessorKey: 'price', header: 'Price', showByDefault: true },

    {
      accessorKey: 'discount',
      header: 'Discount',
      showByDefault: true,
      cell: ({ cell }) => {
        const value = cell.row.original.discount;
        return <span>{value && value != 0 ? `${value}%` : '-'}</span>;
      }
    },
    {
      accessorKey: 'discountedPrice',
      header: 'Discounted Price',
      showByDefault: true,
      cell: ({ cell }) => {
        const value = cell.row.original.discountedPrice;

        return <span>{value && value != 0 ? value : '-'}</span>;
      }
    },
    { accessorKey: 'count', header: 'Count', showByDefault: true },

    { accessorKey: 'unit_of_measure', header: 'Unit of Measure', showByDefault: true },
    {
      accessorKey: 'collectionInfo.name',
      header: 'Collection Name',
      showByDefault: true,
      cell: (cell) => {
        const collectionName = cell.row.original.collectionInfo?.name || 'N/A';
        return <span>{collectionName}</span>;
      }
    },

    { accessorKey: 'isStock', header: 'In Stock', showByDefault: true },

    ...(user?.roleInfo.name.startsWith('DR')
      ? [
          {
            accessorKey: 'ngoInfo.name',
            header: 'NGO Name',
            showByDefault: true,
            cell: (cell) => {
              const ngoName = cell.row.original.ngoInfo?.name || 'N/A';
              return <span>{ngoName}</span>;
            }
          }
        ]
      : []),
    // { accessorKey: 'collection_id', header: 'Collection ID' },

    {
      accessorKey: 'createdAt',
      header: 'Created At',
      cell: (cell) => {
        const formattedDate = dayjs(cell.row.original.createdAt).format('DD-MM-YYYY hh:mm A');
        return <span>{formattedDate}</span>;
      }
    },
    {
      accessorKey: 'updatedAt',
      header: 'Updated At',
      cell: (cell) => {
        const formattedDate = dayjs(cell.row.original.updatedAt).format('DD-MM-YYYY hh:mm A');
        return <span>{formattedDate}</span>;
      }
    },
    {
      id: 'actions',
      header: 'Actions',
      cell: (cell) => {
        const id = cell.row.original.id;
        return (
          <Box display="flex" flexDirection="row">
            <TableActions
              handleEditClick={() => handleEditClick(id)}
              cell={cell}
              handleDeleteClick={() => handleDelete(cell.row.original.id)}
            />
          </Box>
        );
      },
      meta: {
        style: {
          position: 'sticky',
          right: 0,
          background: '#fff', // Important: so the sticky column doesn't look weird
          zIndex: 2 // or higher if needed
        }
      }
    }
  ];

  const handlePageChange = (newPage) => {
    setCurrentPage(newPage);
  };

  const handlePageSizeChange = (newPageSize) => {
    setPageSize(newPageSize);
  };

  return (
    <MainCard>
      <Stack spacing={3}>
        <Stack direction="row" justifyContent="flex-end" alignItems="center" spacing={2}>
          <FormControl sx={{ minWidth: 120 }}>
            <InputLabel id="view-mode-label">View Mode</InputLabel>
            <Select
              labelId="view-mode-label"
              id="view-mode-select"
              size="small"
              value={viewMode}
              label="View Mode"
              onChange={(e) => setViewMode(e.target.value)}
            >
              <MenuItem value="table">Table</MenuItem>
              <MenuItem value="card">Card</MenuItem>
            </Select>
          </FormControl>
          <Button variant="contained" color="primary" size="small" startIcon={<PlusOutlined />} onClick={handleAddClick}>
            Add Product
          </Button>
        </Stack>

        {viewMode === 'table' ? (
          isLoading ? (
            <Box
              sx={{
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'center',
                justifyContent: 'center',
                textAlign: 'center'
              }}
            >
              <CircularProgress size={40} color="primary" />
              <Box sx={{ marginTop: 2, fontSize: '16px', color: 'text.secondary' }}>Loading your products...</Box>
            </Box>
          ) : (
            <CustomProductTable
              data={products}
              columns={columns}
              modalToggler={handleAddClick}
              category="Product"
              totalCount={totalCount}
              setTotalCount={setTotalCount}
              currentPage={currentPage}
              setCurrentPage={setCurrentPage}
              pageSize={pageSize}
              setPageSize={setPageSize}
              onPageChange={handlePageChange}
              onPageSizeChange={handlePageSizeChange}
              setGlobalFilter={setGlobalFilter}
              globalFilter={globalFilter}
            />
          )
        ) : (
          <Grid container spacing={3}>
            {products.map((item) => {
              const imageUrl = `${BASE_URL}/fetchProductImages/${item.imageName}`;
              return (
                <Grid item xs={12} sm={6} md={4} key={item.id}>
                  <ProductCard item={item} imageUrl={imageUrl} ngoName={item?.ngoInfo?.name} collectionName={item?.collectionInfo?.name} />
                </Grid>
              );
            })}
          </Grid>
        )}
      </Stack>
      <ToastContainer />
    </MainCard>
  );
};

export default Products;
