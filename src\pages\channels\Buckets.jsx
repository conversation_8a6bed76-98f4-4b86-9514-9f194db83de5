import React, { useEffect, useState } from 'react';
import {
  <PERSON>ton,
  <PERSON>alog,
  <PERSON>alogA<PERSON>,
  DialogContent,
  DialogTitle,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Grid
} from '@mui/material';
import { toast, ToastContainer } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import CustomerTable from 'sections/apps/customer/CustomerTable';
import { getUserPermissions } from 'utils/permissionUtils';
import { API_BASE_URL } from 'api/categories.service';
import TableActions from 'components/TableActions';
import useAuth from 'hooks/useAuth';
import { deleteBucketservice, fetchBucketsService } from '../masters/apis/buckets.service';
import dayjs from 'dayjs';

import { useNavigate } from 'react-router';

export default function BucketsTable() {
  const { user } = useAuth();
  const [buckets, setBuckets] = useState([]);

  const navigate = useNavigate();

  //role based access
  const permissions = getUserPermissions(user);
  const canAdd = permissions?.Add?.Buckets || false;
  const canEdit = permissions?.Edit?.Buckets || false;
  const canDelete = permissions?.Delete?.Buckets || false;

  useEffect(() => {
    fetchBuckets();
  }, []);

  const fetchBuckets = async () => {
    try {
      const data = await fetchBucketsService();
      setBuckets(data);
    } catch (error) {
      console.error('Failed to fetch buckets:', error);
      toast.error('Failed to fetch buckets');
    }
  };

  const handleDelete = async (id) => {
    if (!canDelete) {
      toast.error('You do not have permission to delete Buckets.');
      return;
    }
    const confirmDelete = window.confirm('Are you sure you want to delete this bucket?');
    if (confirmDelete) {
      try {
        await deleteBucketservice(id);

        toast.success('Bucket deleted successfully!');
        fetchBuckets();
      } catch (error) {
        console.error('Failed to delete Bucket:', error);
        toast.error('Failed to delete Bucket');
      }
    }
  };

  const handleEditClick = (id) => {
    navigate(`/channels/buckets/edit/${id}`);
  };
  const handleAddClick = () => {
    navigate(`/channels/buckets/add`);
  };

  const columns = [
    {
      accessorKey: 'name',
      header: 'Name',
      showByDefault: true,
      cell: ({ cell }) => {
        return (
          <Button variant="text" onClick={() => openEditDialog(cell.row.original)}>
            {cell.row.original.name}
          </Button>
        );
      }
    },
    { accessorKey: 'description', header: 'Description', showByDefault: true },
    {
      accessorKey: 'createdAt',
      header: 'Created At',
      showByDefault: true,
      cell: (cell) => {
        const formattedDate = dayjs(cell.row.original.createdAt).format('DD-MM-YYYY hh:mm A');
        return <span>{formattedDate}</span>;
      }
    },
    {
      accessorKey: 'updatedAt',
      header: 'Updated At',
      showByDefault: true,
      cell: (cell) => {
        const formattedDate = dayjs(cell.row.original.updatedAt).format('DD-MM-YYYY hh:mm A');
        return <span>{formattedDate}</span>;
      }
    },
    {
      id: 'actions',
      header: 'Action',
      cell: (cell) => {
        const id = cell.row.original.id;
        return <TableActions handleEditClick={() => handleEditClick(id)} cell={cell} handleDeleteClick={handleDelete} />;
      }
    }
  ];

  return (
    <div>
      <CustomerTable data={buckets} columns={columns} modalToggler={handleAddClick} category={'Bucket'} />

      <ToastContainer />
    </div>
  );
}
