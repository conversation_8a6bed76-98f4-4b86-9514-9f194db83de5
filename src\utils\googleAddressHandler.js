import { useState } from 'react';
import axios from 'axios';
import { toast } from 'react-toastify';
import { toPairs } from 'lodash';

const DEFAULT_MARKER_POSITION = { lat: 22.199166, lng: 78.476681 };

export const useAddressHandler = (setAddressInformation) => {
  const [isLoadingLocate, setIsLoadingLocate] = useState(false);
  const [error, setError] = useState('');
  const [suggestions, setSuggestions] = useState([]);
  const [isLoadingSuggestion, setIsLoadingSuggestion] = useState(false);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [markerPosition, setMarkerPosition] = useState(DEFAULT_MARKER_POSITION);
  const [isPincodeLoading, setIsPincodeLoading] = useState(false);

  const fetchLocationDetailsFromPincode = async (pincode, setFieldValue) => {
    setIsPincodeLoading(true);
    try {
      const response = await axios.get(`https://api.postalpincode.in/pincode/${pincode}`);
      if (response.data?.length > 0 && response.data[0]?.PostOffice?.length > 0) {
        const postOfficeData = response.data[0].PostOffice[0];
        setFieldValue('country', postOfficeData.Country || '');
        setFieldValue('state', postOfficeData.State || '');
        setFieldValue('place_name', postOfficeData.Block || postOfficeData.District || '');
        setAddressInformation({
          pincode: pincode,
          place_name: postOfficeData.Block || postOfficeData.District,
          country: postOfficeData.Country || '',
          state: postOfficeData.State || '',
          current_address: '',
          latitude: null,
          longitude: null
        });
      } else {
        toast.warning('Could not find details for the provided pincode.');
        setFieldValue('country', '');
        setFieldValue('state', '');
        setFieldValue('place_name', '');
      }
    } catch (error) {
      console.error('Error fetching location details from pincode:', error);
      toast.error('Failed to fetch location details for the pincode.');
      setFieldValue('country', '');
      setFieldValue('state', '');
      setFieldValue('place_name', '');
    } finally {
      setIsPincodeLoading(false);
    }
  };

  const handleSearchSubmit = async (searchQuery, pincode = null) => {
    if (!searchQuery) {
      window.alert('Please enter address');
      return;
    }
    setIsLoadingLocate(true);

    try {
      const geocoder = new window.google.maps.Geocoder();
      const finalQuery = pincode ? `${searchQuery}, ${pincode}` : searchQuery;
      geocoder.geocode({ address: finalQuery }, (results, status) => {
        if (status === 'OK') {
          setSuggestions(results);
          setError('');
          if (results[0]) {
            handleSuggestionClick(results[0]);
          }
        } else {
          toast.warn('Failed to locate your address, can you give us more details');
          setError('Failed to retrieve suggestions. Please try again.');
        }
        setIsLoadingLocate(false);
      });
    } catch (error) {
      setError('Failed to retrieve suggestions. Please try again.');
      setIsLoadingLocate(false);
    }
  };

  const handleMarkerDragEnd = (event) => {
    setMarkerPosition(event.latLng);
    const geocoder = new window.google.maps.Geocoder();
    geocoder.geocode({ location: event.latLng }, (results, status) => {
      if (status === 'OK') {
        const current_address = results[0]?.formatted_address || '';
        setAddressInformation((prev) => ({
          ...prev,
          latitude: event.latLng.lat(),
          longitude: event.latLng.lng()
        }));
      }
    });
  };

  const handleSuggestionClick = async (suggestion) => {
    setIsLoadingSuggestion(true);
    try {
      const { lat, lng } = suggestion.geometry.location;
      const geocoder = new window.google.maps.Geocoder();
      geocoder.geocode({ location: { lat: lat(), lng: lng() } }, (results, status) => {
        if (status === 'OK') {
          const current_address = results[0]?.formatted_address || '';
          setAddressInformation((prev) => ({
            ...prev,
            latitude: lat(),
            longitude: lng()
          }));
          setMarkerPosition({ lat: lat(), lng: lng() });
          setIsDialogOpen(true);
        } else {
          setError('Failed to retrieve location details. Please try again.');
        }
        setIsLoadingSuggestion(false);
      });
    } catch (error) {
      setError('Failed to retrieve location details. Please try again.');
      setIsLoadingSuggestion(false);
    }
  };

  return {
    isLoadingLocate,
    error,
    suggestions,
    isLoadingSuggestion,
    isDialogOpen,
    markerPosition,
    isPincodeLoading,
    fetchLocationDetailsFromPincode,
    handleSearchSubmit,
    handleMarkerDragEnd,
    handleSuggestionClick,
    setIsDialogOpen
  };
};
