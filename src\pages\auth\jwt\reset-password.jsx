// material-ui
import Grid from '@mui/material/Grid';
import Stack from '@mui/material/Stack';
import Typography from '@mui/material/Typography';

// project import
import AuthWrapper from 'sections/auth/AuthWrapper';
import AuthResetPassword from 'sections/auth/jwt/AuthResetPassword';

import Box from '@mui/material/Box';

import { useTheme } from '@mui/system';
import ImageSlider from 'components/ImageSlider';

// ================================|| JWT - RESET PASSWORD ||================================ //

export default function ResetPassword() {
  const theme = useTheme();

  return (
    <Box sx={{ display: 'flex', minHeight: '100vh' }}>
      {/* Left side - Image Slider */}
      <ImageSlider />

      {/* Right side - Registration Form */}
      <Box sx={{ flex: 1, display: 'flex', flexDirection: 'column', bgcolor: theme.palette.primary.main }}>
        <AuthWrapper>
          <Grid container spacing={3}>
            <Grid item xs={12}>
              <Stack sx={{ mb: { xs: -0.5, sm: 0.5 } }} spacing={1}>
                <Typography variant="h3" style={{ color: '#fff' }}>
                  Reset Password
                </Typography>
                <Typography color="secondary" style={{ color: '#fff' }}>
                  Please choose your new password
                </Typography>
              </Stack>
            </Grid>
            <Grid item xs={12}>
              <AuthResetPassword />
            </Grid>
          </Grid>
        </AuthWrapper>
      </Box>
    </Box>
  );
}
