import PropTypes from 'prop-types';
import { useRef, memo } from 'react';
import { Map, Layer, Source } from 'react-map-gl';
import { clusterCountLayer, clusterLayer, unclusteredPointLayer } from './layers';

function MapClusters({ data, ...other }) {
  const mapRef = useRef(null);

  const onClick = (event) => {
    const feature = event.features?.[0];
    const clusterId = feature?.properties?.cluster_id;

    if (mapRef.current) {
      const mapboxSource = mapRef.current.getSource('earthquakes');

      mapboxSource.getClusterExpansionZoom(clusterId, (error, zoom) => {
        if (error) {
          return;
        }

        mapRef.current.easeTo({
          center: feature.geometry.coordinates,
          zoom: Number.isNaN(zoom) ? 3 : zoom,
          duration: 500
        });
      });
    }
  };

  return (
    <Map
      initialViewState={{
        latitude: 20.5937,
        longitude: 78.9629,
        zoom: 4
      }}
      interactiveLayerIds={[clusterLayer.id || '']}
      onClick={onClick}
      ref={mapRef}
      {...other}
    >
      <Source id="earthquakes" type="geojson" data={data} cluster clusterMaxZoom={14} clusterRadius={50}>
        <Layer {...clusterLayer} />
        <Layer {...clusterCountLayer} />
        <Layer {...unclusteredPointLayer} />
      </Source>
    </Map>
  );
}

MapClusters.propTypes = {
  data: PropTypes.object.isRequired,
  other: PropTypes.any
};

export default memo(MapClusters);
