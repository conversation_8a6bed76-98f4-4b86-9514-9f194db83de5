// ImageSlider.js
import { useState } from 'react';
import { IconButton, Box, Typography, Stack } from '@mui/material';
import { LeftOutlined, RightOutlined } from '@ant-design/icons';
import logo from '../assets/images/do-right-logo.png';
import image1 from '../assets/images/1.png';
import image2 from '../assets/images/2.png';
import image3 from '../assets/images/3.png';
import image4 from '../assets/images/4.png';

const slides = [
  {
    image: image1,
    title: '',
    subtitle: ''
  },
  {
    image: image2,
    title: 'Discover Your Cause, Create Meaningful Change.',
    subtitle: 'Join us in creating positive change'
  },
  {
    image: image3,
    title: 'Donate with Confidence, Track Your Impact',
    subtitle: 'Connect with like-minded individuals'
  },
  {
    image: image4,
    title: 'Partner with Trusted NGOs to Achieve Your ESG',
    subtitle: 'Connect with like-minded individuals'
  }
];

export default function ImageSlider() {
  const [currentSlide, setCurrentSlide] = useState(0);

  const nextSlide = () => {
    setCurrentSlide((prev) => (prev + 1) % slides.length);
  };

  const prevSlide = () => {
    setCurrentSlide((prev) => (prev - 1 + slides.length) % slides.length);
  };

  return (
    <Box
      sx={{
        flex: 1,
        position: 'relative',
        bgcolor: '#ff7f04',
        display: { xs: 'none', md: 'block' }
      }}
    >
      {/* Logo at the top-left corner */}
      <Box
        component="img"
        src={logo}
        alt="Logo"
        sx={{
          position: 'absolute',
          top: 40,
          left: 40,
          width: 80,
          height: 'auto',
          zIndex: 2
        }}
      />

      <Box
        sx={{
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          backgroundImage: `url(${slides[currentSlide].image})`,
          backgroundSize: 'cover',
          backgroundPosition: 'center',
          transition: 'opacity 0.5s ease-in-out'
        }}
      />
      <Box
        sx={{
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          bgcolor: 'rgba(0, 0, 0, 0)',
          display: 'flex',
          flexDirection: 'column',
          justifyContent: 'center',
          alignItems: 'center',
          color: 'white',
          padding: 4,
          textAlign: 'center'
        }}
      >
        <Typography variant="h3" component="h1" gutterBottom>
          {slides[currentSlide].title}
        </Typography>
        <Typography variant="h6">{slides[currentSlide].subtitle}</Typography>
      </Box>
      <IconButton
        onClick={prevSlide}
        sx={{
          position: 'absolute',
          left: 16,
          top: '50%',
          transform: 'translateY(-50%)',
          color: 'black', // Change icon color to black for visibility
          bgcolor: 'rgba(255, 255, 255, 0.7)', // Add a semi-transparent white background
          boxShadow: '0 2px 4px rgba(0, 0, 0, 0.2)', // Add a subtle shadow for emphasis
          '&:hover': { bgcolor: 'rgba(255, 255, 255, 0.9)' }
        }}
      >
        <LeftOutlined />
      </IconButton>
      <IconButton
        onClick={nextSlide}
        sx={{
          position: 'absolute',
          right: 16,
          top: '50%',
          transform: 'translateY(-50%)',
          color: 'black', // Change icon color to black for visibility
          bgcolor: 'rgba(255, 255, 255, 0.7)', // Add a semi-transparent white background
          boxShadow: '0 2px 4px rgba(0, 0, 0, 0.2)', // Add a subtle shadow for emphasis
          '&:hover': { bgcolor: 'rgba(255, 255, 255, 0.9)' }
        }}
      >
        <RightOutlined />
      </IconButton>
      <Stack
        direction="row"
        spacing={1}
        sx={{
          position: 'absolute',
          bottom: 24,
          left: '50%',
          transform: 'translateX(-50%)',
          zIndex: 3 // Ensure it appears above other elements
        }}
      >
        {slides.map((_, index) => (
          <Box
            key={index}
            sx={{
              width: 12, // Increased size for better visibility
              height: 12,
              borderRadius: '50%',
              bgcolor: currentSlide === index ? 'white' : 'rgba(255, 255, 255, 0.6)', // Use higher opacity for inactive dots
              boxShadow: currentSlide === index ? '0 0 6px rgba(255, 255, 255, 0.8)' : 'none', // Add a glow to the active dot
              cursor: 'pointer',
              transition: 'all 0.3s ease', // Smooth transition for size and shadow
              '&:hover': {
                transform: 'scale(1.2)' // Slightly enlarge on hover for better interactivity
              }
            }}
            onClick={() => setCurrentSlide(index)}
          />
        ))}
      </Stack>
    </Box>
  );
}
