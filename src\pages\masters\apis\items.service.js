import axiosServices from 'utils/axios_node';

export const API_BASE_URL = `${import.meta.env.VITE_APP_APPLICATION_API_URL}/api`;

export const getAllItems = async (pageName = '') => {
  const pageNameParam = pageName ? `?pageName=${pageName}` : '';
  const response = await axiosServices.get(`${API_BASE_URL}/items${pageNameParam}`);
  return response?.data;
};

export const addItems = async (item) => {
  const response = await axiosServices.post(`${API_BASE_URL}/items`, item);
  return response?.data;
};

export const updateItems = async (id, item) => {
  const response = await axiosServices.put(`${API_BASE_URL}/items/${id}`, item);
  return response?.data;
};

export const deleteItems = async (id) => {
  const response = await axiosServices.delete(`${API_BASE_URL}/items/${id}`);
  return response?.data;
};
