import React from 'react';
import { Dialog, DialogTitle, DialogContent, DialogActions, Button, Typography } from '@mui/material';
import { useNavigate } from 'react-router-dom';

const UnsavedChangesDialog = ({ showUnsavedDialog, setShowUnsavedDialog, pendingNavigationPath }) => {
  const navigate = useNavigate();

  return (
    <Dialog
      open={showUnsavedDialog}
      onClose={(event, reason) => {
        if (reason !== 'backdropClick' && reason !== 'escapeKeyDown') {
          setShowUnsavedDialog(false);
        }
      }}
      maxWidth="50%"
      PaperProps={{
        sx: {
          top: 0,
          position: 'absolute',
          borderRadius: '16px',
          width: '100%',
          maxWidth: '600px'
        }
      }}
    >
      <DialogTitle>Unsaved Changes</DialogTitle>
      <DialogContent>
        <Typography>You have unsaved changes. Are you sure you want to leave this page?</Typography>
      </DialogContent>
      <DialogActions>
        <Button
          onClick={() => {
            setShowUnsavedDialog(false);
            if (pendingNavigationPath) {
              navigate(pendingNavigationPath);
            }
          }}
          color="primary"
        >
          Leave
        </Button>
        <Button onClick={() => setShowUnsavedDialog(false)} color="primary" variant="contained">
          Cancel
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default UnsavedChangesDialog;
