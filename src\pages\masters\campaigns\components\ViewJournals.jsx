import React, { useEffect, useState } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom'; // Import useNavigate

import { Tooltip, IconButton, Button, Box, Typography } from '@mui/material';
import { toast, ToastContainer } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import CustomerTable from 'sections/apps/customer/CustomerTable';

import useAuth from 'hooks/useAuth';
import { deleteJournals, getJournalsBySenderId } from 'pages/masters/apis/notification.service';
import { useParams } from 'react-router';
import { DeleteOutlined } from '@ant-design/icons';
import dayjs from 'dayjs';

export default function ViewJornals() {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  
  const { user } = useAuth();
  const [journals, setJournals] = useState([]);
  const { senderId } = useParams();
  const fullname = searchParams.get('name'); // get auth and set route based on that

  useEffect(() => {
    getJournals();
  }, []);

  const getJournals = async () => {
    try {
      const data = await getJournalsBySenderId(senderId);
      setJournals(data);
    } catch (error) {
      console.error('Failed to fetch journals:', error);
      toast.error('Failed to fetch journals');
    }
  };

  const handleDelete = async (id) => {
    const confirmDelete = window.confirm('Are you sure you want to delete this Journal?');
    if (confirmDelete) {
      try {
        await deleteJournals(id);
        toast.success('Journal deleted successfully!');
        getJournals();
      } catch (error) {
        console.error('Failed to delete Journal:', error);
        toast.error('Failed to delete Journal');
      }
    }
  };

  const columns = [
    // {
    //   accessorKey: 'name',
    //   header: 'Name',
    //   showByDefault: true,
    //   cell: ({ cell }) => {
    //     return (
    //       <Button variant="text" onClick={() => openEditDialog(cell.row.original)}>
    //         {cell.row.original.name}
    //       </Button>
    //     );
    //   }
    // },
    { accessorKey: 'description', header: 'Description', showByDefault: true },
    {
      accessorKey: 'ngo_id',
      header: 'NGO Name',
      showByDefault: true,
      cell: (cell) => {
        const ngoName = cell.row.original.ngoInfo?.name || 'N/A';
        return <span>{ngoName}</span>;
      }
    },
    {
      accessorKey: 'createdAt',
      header: 'Created At',
      showByDefault: true,
      cell: (cell) => {
        const formattedDate = dayjs(cell.row.original.createdAt).format('DD-MM-YYYY hh:mm A');
        return <span>{formattedDate}</span>;
      }
    },

    {
      id: 'actions',
      header: 'Actions',

      cell: (cell) => {
        return (
          <Tooltip title="Delete">
            <IconButton color="error" onClick={() => handleDelete(cell.row.original.id)}>
              <DeleteOutlined />
            </IconButton>
          </Tooltip>
        );
      }
    }
  ];

  return (
    <div>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h2" component="h1">
          View Journals of {fullname}
        </Typography>
        <Button variant="contained" color="primary" onClick={() => navigate(-1)}>
          Back
        </Button>
      </Box>

      <CustomerTable data={journals} columns={columns} />
      <ToastContainer />
    </div>
  );
}
