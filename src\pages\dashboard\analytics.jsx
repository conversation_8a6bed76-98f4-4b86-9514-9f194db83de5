import { useEffect } from 'react';
// material-ui
import Grid from '@mui/material/Grid';
import List from '@mui/material/List';
import ListItemButton from '@mui/material/ListItemButton';
import ListItemText from '@mui/material/ListItemText';
import Stack from '@mui/material/Stack';
import Typography from '@mui/material/Typography';

// project import
import MainCard from 'components/MainCard';
import AnalyticsDataCard from 'components/cards/statistics/AnalyticsDataCard';

import WelcomeBanner from 'sections/dashboard/analytics/WelcomeBanner';
import MarketingCardChart from 'sections/dashboard/analytics/MarketingCardChart';
import OrdersCardChart from 'sections/dashboard/analytics/OrdersCardChart';
import OrdersList from 'sections/dashboard/analytics/OrdersList';
import PageViews from 'sections/dashboard/analytics/PageViews';
import ReportChart from 'sections/dashboard/analytics/ReportChart';
import SalesCardChart from 'sections/dashboard/analytics/SalesCardChart';
import TransactionHistory from 'sections/dashboard/analytics/TransactionHistory';
import UsersCardChart from 'sections/dashboard/analytics/UsersCardChart';
import LabelledTasks from 'sections/dashboard/analytics/LabelledTasks';
import ReaderCard from 'sections/dashboard/analytics/ReaderCard';
import AcquisitionChannels from 'sections/dashboard/analytics/AcquisitionChannels';
import IncomeOverviewCard from 'sections/dashboard/analytics/IncomeOverviewCard';
import SaleReportCard from 'sections/dashboard/analytics/SaleReportCard';
import ProfileCard from 'sections/apps/profiles/user/ProfileCard';
import Map from 'pages/map';
import { API_BASE_URL } from 'api/campaigns.service';
import ClusteredMapComponent from 'pages/maps/ClusteredMapComponent';
import HeatmapComponent from 'pages/maps/HeatMapComponent';
import { Alert, CircularProgress } from '@mui/material';
import { QuestionCircleOutlined } from '@ant-design/icons';
import Button from '@mui/material/Button';
import { Fragment, useState } from 'react';
import { Link } from 'react-router-dom';
import useAuth from 'hooks/useAuth';
import AlertMessage from './Alertmessage';
import AnalyticEcommerce from 'components/cards/statistics/AnalyticEcommerce';
import { getFullCampaignStats, getFullNGOStats } from 'pages/masters/apis/notification.service';
import { getProfileQueries } from 'sections/apps/profiles/account/tabs.service';

// ==============================|| DASHBOARD - ANALYTICS ||============================== //

export default function DashboardAnalytics() {
  const [inReviewNgoCount, setInReviewCount] = useState(3);
  const [inReviewCampaignCount, setInReviewCampaignCount] = useState(2);
  const [isLoadingNgo, setIsLoadingNgo] = useState(false);
  const [isLoadingCampaign, setIsLoadingCampaign] = useState(false);
  const { user } = useAuth();

  const [alertMessages, setAlertMessages] = useState({
    pendingCount: 0,
    inReviewCount: 0
  });
  const [ngoStats, setNgoStats] = useState({
    pendingCount: 0,
    inReviewNgos: 0,
    verifiedNgos: 0,
    totalAssignedNgos: 0
  });
  const [campaignStats, setCampaignStats] = useState({
    totalCampaigns: 0,
    completed: 0,
    live: 0,
    approved: 0,
    inReview: 0,
    draft: 0
  });
  const [alertMessagesAllNgo, setAlertMessagesAllNgo] = useState({
    inReviewNgoCount: 0
  });
  const [alertMessagesAllCam, setAlertMessagesAllCam] = useState({
    inReviewCampaginCount: 0
  });
  const [ngoStatsAll, setNgoStatsAll] = useState({
    totalNgos: 0,
    inReviewNgos: 0,
    pendingNgos: 0,
    verifiedNgos: 0,
    inactiveNgos: 0
  });
  const [campaignStatsAll, setCampaignStatsAll] = useState({
    totalCampaigns: 0,
    live: 0,
    inReview: 0,
    approved: 0,
    completed: 0
  });

  const [profileQueries, setProfileQueries] = useState([]);

  useEffect(() => {
    const fetchFullNgoStats = async () => {
      setIsLoadingNgo(true);
      try {
        const response = await getFullNGOStats();
        setNgoStatsAll(response.ngoStats);
      } catch (error) {
        console.error('Error fetching NGO stats:', error);
      } finally {
        setIsLoadingNgo(false);
      }
    };

    const fetchFullCampaignStats = async () => {
      setIsLoadingCampaign(true);
      try {
        const response = await getFullCampaignStats();
        setCampaignStatsAll(response.campaginStats);
      } catch (error) {
        console.error('Error fetching campaign stats:', error);
      } finally {
        setIsLoadingCampaign(false);
      }
    };

    const fetchProfileQueries = async () => {
      try {
        const response = await getProfileQueries(null, user?.roleInfo?.name === 'DR_Management' ? null : user?.id, 'profile-queries');
        setProfileQueries(response);
      } catch (error) {
        console.error('Error fetching NGO stats:', error);
      }
    };

    fetchProfileQueries();
    if (user.roleInfo.name === 'DR_Management') {
      fetchFullCampaignStats();
      fetchFullNgoStats();
    }
  }, [user]);

  return (
    <Fragment>
      <Grid container rowSpacing={4.5} columnSpacing={3}>
        {/* row 1 */}
        {user?.roleInfo?.name === 'DR_Management' && ngoStatsAll.inReviewNgos === 1 && (
          <Grid item xs={6}>
            <AlertMessage message={`${ngoStatsAll.inReviewNgos} NGO is currently under review.`} status="In Review" type="ngo" />
          </Grid>
        )}
        {user?.roleInfo?.name === 'DR_Management' && ngoStatsAll.inReviewNgos > 1 && (
          <Grid item xs={6}>
            <AlertMessage message={`${ngoStatsAll.inReviewNgos} NGOs are currently under review.`} status="In Review" type="ngo" />
          </Grid>
        )}
        {user?.roleInfo?.name === 'DR_Management' && ngoStatsAll.inactiveNgos === 1 && (
          <Grid item xs={6}>
            <AlertMessage message={`${ngoStatsAll.inactiveNgos} NGO is currently inactive`} status="Inactive" type="portaluser" />
          </Grid>
        )}
        {user?.roleInfo?.name === 'DR_Management' && ngoStatsAll.inactiveNgos > 1 && (
          <Grid item xs={6}>
            <AlertMessage message={`${ngoStatsAll.inactiveNgos} NGOs are currently inactive.`} status="Inactive" type="portaluser" />
          </Grid>
        )}
        {user?.roleInfo?.name === 'DR_Management' && campaignStatsAll.inReview === 1 && (
          <Grid item xs={6}>
            <AlertMessage
              message={`${campaignStatsAll.inReview} campaigns is currently under review.`}
              status="In Review"
              type="campaign"
            />
          </Grid>
        )}
        {user?.roleInfo?.name === 'DR_Management' && campaignStatsAll.inReview > 1 && (
          <Grid item xs={6}>
            <AlertMessage
              message={`${campaignStatsAll.inReview} campaigns are currently under review.`}
              status="In Review"
              type="campaign"
            />
          </Grid>
        )}

        {profileQueries?.length > 0 && (
          <Grid item xs={6} style={{}}>
            <AlertMessage
              message={`${profileQueries?.length} NGO(s) have requested profile edits. Please review their requests.`}
              status="Pending"
              type="profile-query"
            />
          </Grid>
        )}
      </Grid>
      <hr />
      {/* <Grid item xs={12} sm={6} md={4} lg={3}>
        <AnalyticsDataCard title="Total NGO's" count="73,952" percentage={70.5}>
          <UsersCardChart />
        </AnalyticsDataCard>
      </Grid>
      <Grid item xs={12} sm={6} md={4} lg={3}>
        <AnalyticsDataCard title="Total Campaigns" count="10" percentage={27.4} isLoss color="warning">
          <OrdersCardChart />
        </AnalyticsDataCard>
      </Grid>
      <Grid item xs={12} sm={6} md={4} lg={3}>
        <AnalyticsDataCard title="Active Members" count="35,078" percentage={27.4} isLoss color="warning">
          <SalesCardChart />
        </AnalyticsDataCard>
      </Grid>
      <Grid item xs={12} sm={6} md={4} lg={3}>
        <AnalyticsDataCard title="Total Members" count="112083" percentage={70.5}>
          <MarketingCardChart />
        </AnalyticsDataCard>
      </Grid> */}

      <Grid item md={8} sx={{ display: { sm: 'none', md: 'block', lg: 'none' } }} />
      <Grid container rowSpacing={4.5} columnSpacing={3}>
        <Grid item xs={12} sx={{ mb: -2.25, mt: 2 }}>
          <Typography variant="h5">NGO Stats</Typography>
        </Grid>
        {isLoadingNgo ? (
          <Grid item xs={12} display="flex" justifyContent="center" alignItems="center" sx={{ mt: 2 }}>
            <CircularProgress />
            <Typography sx={{ margin: 2 }}>Loading NGO Stats. Please wait....</Typography>
          </Grid>
        ) : (
          <>
            <Grid item xs={12} sm={6} md={4} lg={3}>
              <AnalyticEcommerce title="Total" count={ngoStatsAll.totalNgos} url="/masters/ngos" />
            </Grid>
            <Grid item xs={12} sm={6} md={4} lg={3}>
              <AnalyticEcommerce title="In Review" count={ngoStatsAll.inReviewNgos} url="/masters/ngos/?alertStatus=In Review" />
            </Grid>
            <Grid item xs={12} sm={6} md={4} lg={3}>
              <AnalyticEcommerce title="Pending" count={ngoStatsAll.pendingNgos} url="/masters/ngos?alertStatus=Pending" />
            </Grid>
            <Grid item xs={12} sm={6} md={4} lg={3}>
              <AnalyticEcommerce title="Verified" count={ngoStatsAll.verifiedNgos} url="/masters/ngos?alertStatus=Verified" />
            </Grid>
          </>
        )}
      </Grid>
      <hr />
      <Grid container rowSpacing={4.5} columnSpacing={3}>
        <Grid item xs={12} sx={{ mb: -2.25 }}>
          <Typography variant="h5">Campaign Stats</Typography>
        </Grid>
        {isLoadingCampaign ? (
          <Grid item xs={12} display="flex" justifyContent="center" alignItems="center" sx={{ mt: 2 }}>
            <CircularProgress />
            <Typography sx={{ margin: 2 }}>Loading Campaigns Stats. Please wait....</Typography>
          </Grid>
        ) : (
          <>
            <Grid item xs={12} sm={6} md={4} lg={3}>
              <Link to="/masters/campaigns" style={{ textDecoration: 'none' }}>
                <AnalyticEcommerce title="Total" count={campaignStatsAll.totalCampaigns} url="/masters/campaigns" />
              </Link>
            </Grid>
            <Grid item xs={12} sm={6} md={4} lg={3}>
              <Link to="/masters/campaigns" style={{ textDecoration: 'none' }}>
                <AnalyticEcommerce title="In Review" count={campaignStatsAll.inReview} url="/masters/campaigns/?alertStatus=In Review" />
              </Link>
            </Grid>
            <Grid item xs={12} sm={6} md={4} lg={3}>
              <Link to="/masters/campaigns" style={{ textDecoration: 'none' }}>
                <AnalyticEcommerce title="Approved" count={campaignStatsAll.approved} url="/masters/campaigns/?alertStatus=Approved" />
              </Link>
            </Grid>
            <Grid item xs={12} sm={6} md={4} lg={3}>
              <Link to="/masters/campaigns" style={{ textDecoration: 'none' }}>
                <AnalyticEcommerce title="Live" count={campaignStatsAll.live} url="/masters/campaigns/?alertStatus=Live" />
              </Link>
            </Grid>
          </>
        )}
      </Grid>
      <hr />
      <Grid container rowSpacing={4.5} columnSpacing={3}>
        {/* row 2 */}
        <Grid item xs={12} md={12} lg={12}>
          {/* <Grid container alignItems="center" justifyContent="space-between">
          <Grid item>
            <Typography variant="h5">Income Overview</Typography>
          </Grid>
        </Grid>
        <IncomeOverviewCard /> */}
          <ClusteredMapComponent
            setInReviewCount={setInReviewCount}
            url={`${API_BASE_URL}/ngos/allNgos`}
            headerName={'NGOs Cluster Map'}
            type="NGO"
          />
        </Grid>
        <Grid item xs={12} md={6} lg={6}>
          {/* <PageViews /> */}
          {/* <HeatmapComponent url={`${API_BASE_URL}/campaigns/status/Live`} headerName={'Campaigns HeatMap'} /> */}
        </Grid>

        {/* row 2 */}
        {/* <Grid item xs={12} md={6} lg={6}>
        <Grid container alignItems="center" justifyContent="space-between">
          <Grid item>
            <Typography variant="h5">Income Overview</Typography>
          </Grid>
        </Grid>
        <IncomeOverviewCard />
      </Grid>
      <Grid item xs={12} md={6} lg={6}>
        <PageViews />
      </Grid> */}

        {/* row 3 */}
        {/* <Grid item xs={12} md={7} lg={8}>
        <Grid container alignItems="center" justifyContent="space-between">
          <Grid item>
            <Typography variant="h5">Recent Orders</Typography>
          </Grid>
          <Grid item />
        </Grid>
        <MainCard sx={{ mt: 2 }} content={false}>
          <OrdersList />
        </MainCard>
      </Grid>
      <Grid item xs={12} md={5} lg={4}>
        <Grid container alignItems="center" justifyContent="space-between">
          <Grid item>
            <Typography variant="h5">Analytics Report</Typography>
          </Grid>
          <Grid item />
        </Grid>
        <MainCard sx={{ mt: 2 }} content={false}>
          <List sx={{ p: 0, '& .MuiListItemButton-root': { py: 2 } }}>
            <ListItemButton divider>
              <ListItemText primary="Company Finance Growth" />
              <Typography variant="h5">+45.14%</Typography>
            </ListItemButton>
            <ListItemButton divider>
              <ListItemText primary="Company Expenses Ratio" />
              <Typography variant="h5">0.58%</Typography>
            </ListItemButton>
            <ListItemButton>
              <ListItemText primary="Business Risk Cases" />
              <Typography variant="h5">Low</Typography>
            </ListItemButton>
          </List>
          <ReportChart />
        </MainCard>
      </Grid> */}

        {/* row 4 */}
        {/* <Grid item xs={12} md={7} lg={8}>
        <SaleReportCard />
      </Grid>
      <Grid item xs={12} md={5} lg={4}>
        <TransactionHistory />
      </Grid> */}

        {/* row 5 */}
        {/* <Grid item xs={12} md={7} lg={8}>
        <Stack spacing={3}>
          <LabelledTasks />
          <ReaderCard />
        </Stack>
      </Grid>
      <Grid item xs={12} md={5} lg={4}>
        <AcquisitionChannels />
      </Grid> */}
      </Grid>
    </Fragment>
  );
}
