import React, { useState, useEffect } from 'react';
import { usePara<PERSON>, useNavigate } from 'react-router-dom';
import { useFormik } from 'formik';
import * as yup from 'yup';
import { TextField, Button, FormControl, InputLabel, Select, MenuItem, Autocomplete, Box, Stepper, Step, StepLabel } from '@mui/material';
import { DatePicker, LocalizationProvider } from '@mui/x-date-pickers';
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';
import { toast } from 'react-toastify';
import { addCampaign, updateCampaign, getCampaignById } from 'api/campaigns.service';
import { getAllCategories } from 'api/categories.service';
import { getAllNgos } from 'api/ngos.service';
import { getPlaceSuggestions, getPlaceDetails, getStateFromCoordinates } from 'api/campaigns.service';
import dayjs from 'dayjs';
import MainCard from 'components/MainCard';

const validationSchema = yup.object({
  name: yup.string().required('Name is required'),
  campaign_start_date: yup.date().required('Scheduled date is required'),
  campaign_end_date: yup.date().required('Scheduled date is required'),
  impact_opportunity: yup.string().required('Impact opportunity is required'),
  is_onsite_virtual: yup.string().required('Onsite/Virtual selection is required'),
  is_policy: yup.string().required('Policy selection is required'),
  // policy_description: yup.string().when('is_policy', {
  //   is: 'yes',
  //   then: yup.string().required('Policy description is required'),
  //   otherwise: yup.string()
  // }),
  // qr_text: yup.string().required('QR text is required'),
  // donor_target_type: yup.string().required('Donor target type is required'),
  // amount: yup.number().when('donor_target_type', {
  //   is: 'Amount',
  //   then: yup.number().required('Amount is required').positive('Amount must be positive'),
  //   otherwise: yup.string()
  // }),
  // count: yup.number().when('donor_target_type', {
  //   is: 'Count',
  //   then: yup.number().required('Count is required').positive('Count must be positive').integer('Count must be an integer'),
  //   otherwise: yup.string()
  // }),
  streetone: yup.string().required('Street 1 is required'),
  city: yup.string().required('City is required'),
  state: yup.string().required('State is required'),
  pincode: yup.string().required('Pincode is required'),
  status: yup.string().required('Status is required')
});

export default function CampaignForm({ isEdit }) {
  const { campaignId } = useParams();
  const navigate = useNavigate();
  const [activeStep, setActiveStep] = useState(0);
  const [categories, setCategories] = useState([]);
  const [ngos, setNgos] = useState([]);
  const [suggestions, setSuggestions] = useState([]);
  const [selectedLocation, setSelectedLocation] = useState(null);

  const steps = ['Basic Details', 'Location Details', 'Additional Details'];

  const formik = useFormik({
    initialValues: {
      name: '',
      campaign_start_date: null,
      campaign_end_on: null,
      impact_opportunity: '',
      is_onsite_virtual: '',
      is_policy: '',
      policy_description: '',
      qr_text: '',
      donor_target_type: '',
      amount: 0,
      count: 0,
      streetone: '',
      streetwo: '',
      city: '',
      pincode: '',
      latitude: '',
      longitude: '',
      status: 'Active',
      category_id: null,
      ngo_id: null
    },
    validationSchema,
    onSubmit: async (values) => {
      try {
        if (isEdit) {
          await updateCampaign(campaignId, values);
          toast.success('Campaign updated successfully!');
        } else {
          await addCampaign(values);
          toast.success('Campaign added successfully!');
        }
        navigate('/masters/campaigns');
      } catch (error) {
        console.error('Failed to save campaign:', error);
        toast.error('Failed to save campaign');
      }
    }
  });

  useEffect(() => {
    fetchCategories();
    fetchNGOS();
    if (isEdit && campaignId) {
      fetchCampaign(campaignId);
    }
  }, [isEdit, campaignId]);

  const fetchCampaign = async (id) => {
    try {
      const response = await getCampaignById(id);
      formik.setValues({
        ...response,
        campaign_start_date: dayjs(response.campaign_start_date),
        campaign_end_date: dayjs(response.campaign_end_date)
      });
    } catch (error) {
      console.error('Failed to fetch campaign:', error);
      toast.error('Failed to fetch campaign');
    }
  };

  const fetchCategories = async () => {
    try {
      const response = await getAllCategories();
      setCategories(response);
    } catch (error) {
      console.error('Failed to fetch categories:', error);
      toast.error('Failed to fetch categories');
    }
  };

  const fetchNGOS = async () => {
    try {
      const response = await getAllNgos();
      setNgos(response);
    } catch (error) {
      console.error('Failed to fetch ngos:', error);
      toast.error('Failed to fetch ngos');
    }
  };

  const handleNext = () => setActiveStep((prevStep) => prevStep + 1);
  const handleBack = () => setActiveStep((prevStep) => prevStep - 1);

  const handleSearchSubmit = async () => {
    if (!formik.values.city) return;
    try {
      const response = await getPlaceSuggestions(formik.values.city);
      setSuggestions(response.data.suggestions);
    } catch (error) {
      console.error('Error fetching suggestions:', error);
    }
  };

  const handleSuggestionClick = async (suggestion) => {
    try {
      const { response, latitude, longitude } = await getPlaceDetails(suggestion);
      const locationData = response.data.features[0];
      const stateName = await getStateFromCoordinates(longitude, latitude);
      setSelectedLocation(locationData);
      formik.setValues({
        ...formik.values,
        pincode: locationData.properties.context.postcode?.name || '',
        state: stateName,
        latitude: locationData.geometry.coordinates[1],
        longitude: locationData.geometry.coordinates[0]
      });
    } catch (error) {
      console.error('Error fetching location details:', error);
    }
  };

  const BasicDetailsStep = () => (
    <>
      <TextField
        label="Name"
        fullWidth
        id="name"
        name="name"
        value={formik.values.name}
        onChange={formik.handleChange}
        onBlur={formik.handleBlur}
        error={formik.touched.name && Boolean(formik.errors.name)}
        margin="normal"
      />
      <LocalizationProvider dateAdapter={AdapterDayjs}>
        <DatePicker
          label="Start Date"
          format="DD/MM/YYYY"
          value={formik.values.campaign_start_date}
          onChange={(newValue) => formik.setFieldValue('campaign_start_date', newValue)}
          renderInput={(params) => (
            <TextField
              {...params}
              fullWidth
              margin="normal"
              error={formik.touched.campaign_start_date && Boolean(formik.errors.campaign_start_date)}
              helperText={formik.touched.campaign_start_date && formik.errors.campaign_start_date}
            />
          )}
        />
      </LocalizationProvider>
      <LocalizationProvider dateAdapter={AdapterDayjs}>
        <DatePicker
          label="End Date"
          format="DD/MM/YYYY"
          value={formik.values.campaign_start_date}
          onChange={(newValue) => formik.setFieldValue('campaign_end_date', newValue)}
          renderInput={(params) => (
            <TextField
              {...params}
              fullWidth
              margin="normal"
              error={formik.touched.campaign_end_date && Boolean(formik.errors.campaign_end_date)}
              helperText={formik.touched.campaign_end_date && formik.errors.campaign_end_date}
            />
          )}
        />
      </LocalizationProvider>
      <FormControl fullWidth margin="normal">
        <InputLabel>Policy</InputLabel>
        <Select
          name="is_policy"
          value={formik.values.is_policy}
          onChange={formik.handleChange}
          error={formik.touched.is_policy && Boolean(formik.errors.is_policy)}
          label="Policy"
        >
          <MenuItem value="yes">Yes</MenuItem>
          <MenuItem value="no">No</MenuItem>
        </Select>
      </FormControl>
      {formik.values.is_policy === 'yes' && (
        <TextField
          label="Policy Description"
          fullWidth
          id="policy_description"
          name="policy_description"
          value={formik.values.policy_description}
          onChange={formik.handleChange}
          error={formik.touched.policy_description && Boolean(formik.errors.policy_description)}
          helperText={formik.touched.policy_description && formik.errors.policy_description}
          margin="normal"
        />
      )}
      <Autocomplete
        options={categories}
        getOptionLabel={(option) => option.name}
        value={categories.find((cat) => cat.id === formik.values.category_id) || null}
        onChange={(event, newValue) => formik.setFieldValue('category_id', newValue?.id || null)}
        renderInput={(params) => <TextField {...params} label="Category" margin="normal" />}
      />
      <Autocomplete
        options={ngos}
        getOptionLabel={(option) => option.fullname}
        value={ngos.find((ngo) => ngo.id === formik.values.ngo_id) || null}
        onChange={(event, newValue) => formik.setFieldValue('ngo_id', newValue?.id || null)}
        renderInput={(params) => <TextField {...params} label="Select NGO" margin="normal" />}
      />
    </>
  );

  const LocationDetailsStep = () => (
    <>
      <TextField label="Search Location" fullWidth value={formik.values.city} onChange={formik.handleChange} name="city" margin="normal" />
      <Button onClick={handleSearchSubmit} variant="contained" color="primary">
        Search
      </Button>
      {suggestions.length > 0 && (
        <Box mt={2}>
          {suggestions.map((suggestion) => (
            <Button key={suggestion.mapbox_id} onClick={() => handleSuggestionClick(suggestion)}>
              {suggestion.name}
            </Button>
          ))}
        </Box>
      )}
      {selectedLocation && (
        <>
          <TextField
            label="Place name"
            fullWidth
            value={selectedLocation.properties?.name || ''}
            onChange={(e) => formik.setFieldValue('city', e.target.value)}
            margin="normal"
          />
          <TextField
            label="Pincode"
            fullWidth
            name="pincode"
            value={formik.values.pincode}
            onChange={formik.handleChange}
            error={formik.touched.pincode && Boolean(formik.errors.pincode)}
            helperText={formik.touched.pincode && formik.errors.pincode}
            margin="normal"
          />
          <TextField
            label="State"
            fullWidth
            name="state"
            value={formik.values.state}
            onChange={formik.handleChange}
            error={formik.touched.state && Boolean(formik.errors.state)}
            helperText={formik.touched.state && formik.errors.state}
            margin="normal"
          />
          <TextField
            label="Latitude"
            fullWidth
            name="latitude"
            value={formik.values.latitude}
            onChange={formik.handleChange}
            margin="normal"
          />
          <TextField
            label="Longitude"
            fullWidth
            name="longitude"
            value={formik.values.longitude}
            onChange={formik.handleChange}
            margin="normal"
          />
        </>
      )}
    </>
  );

  const AdditionalDetailsStep = () => (
    <>
      <TextField
        label="Impact Opportunity"
        fullWidth
        name="impact_opportunity"
        value={formik.values.impact_opportunity}
        onChange={formik.handleChange}
        error={formik.touched.impact_opportunity && Boolean(formik.errors.impact_opportunity)}
        helperText={formik.touched.impact_opportunity && formik.errors.impact_opportunity}
        margin="normal"
      />
      <FormControl fullWidth margin="normal">
        <InputLabel>Virtual</InputLabel>
        <Select
          name="is_onsite_virtual"
          value={formik.values.is_onsite_virtual}
          onChange={formik.handleChange}
          error={formik.touched.is_onsite_virtual && Boolean(formik.errors.is_onsite_virtual)}
          label="Virtual"
        >
          <MenuItem value="yes">Yes</MenuItem>
          <MenuItem value="no">No</MenuItem>
        </Select>
      </FormControl>
      <TextField
        label="Qr Text"
        fullWidth
        name="qr_text"
        value={formik.values.qr_text}
        onChange={formik.handleChange}
        error={formik.touched.qr_text && Boolean(formik.errors.qr_text)}
        helperText={formik.touched.qr_text && formik.errors.qr_text}
        margin="normal"
      />
      <FormControl fullWidth margin="normal">
        <InputLabel>Donor Target Type</InputLabel>
        <Select
          name="donor_target_type"
          value={formik.values.donor_target_type}
          onChange={formik.handleChange}
          error={formik.touched.donor_target_type && Boolean(formik.errors.donor_target_type)}
          label="Donor Target Type"
        >
          <MenuItem value="Count">Count</MenuItem>
          <MenuItem value="Amount">Amount</MenuItem>
        </Select>
      </FormControl>
      {formik.values.donor_target_type === 'Amount' && (
        <TextField
          label="Amount"
          type="number"
          fullWidth
          name="amount"
          value={formik.values.amount}
          onChange={formik.handleChange}
          error={formik.touched.amount && Boolean(formik.errors.amount)}
          helperText={formik.touched.amount && formik.errors.amount}
          margin="normal"
        />
      )}
      {formik.values.donor_target_type === 'Count' && (
        <TextField
          label="Count"
          type="number"
          fullWidth
          name="count"
          value={formik.values.count}
          onChange={formik.handleChange}
          error={formik.touched.count && Boolean(formik.errors.count)}
          helperText={formik.touched.count && formik.errors.count}
          margin="normal"
        />
      )}
      <TextField
        label="Street 1"
        fullWidth
        name="streetone"
        value={formik.values.streetone}
        onChange={formik.handleChange}
        error={formik.touched.streetone && Boolean(formik.errors.streetone)}
        helperText={formik.touched.streetone && formik.errors.streetone}
        margin="normal"
      />
      <TextField label="Street 2" fullWidth name="streetwo" value={formik.values.streetwo} onChange={formik.handleChange} margin="normal" />
      <FormControl fullWidth margin="normal">
        <InputLabel>Status</InputLabel>
        <Select
          name="status"
          value={formik.values.status}
          onChange={formik.handleChange}
          error={formik.touched.status && Boolean(formik.errors.status)}
          label="Status"
        >
          <MenuItem value="Active">Active</MenuItem>
          <MenuItem value="Inactive">Inactive</MenuItem>
        </Select>
      </FormControl>
    </>
  );

  const getStepContent = (step) => {
    switch (step) {
      case 0:
        return <BasicDetailsStep />;
      case 1:
        return <LocationDetailsStep />;
      case 2:
        return <AdditionalDetailsStep />;
      default:
        return 'Unknown step';
    }
  };

  return (
    <MainCard>
      <form onSubmit={formik.handleSubmit}>
        <Box>
          <Stepper activeStep={activeStep}>
            {steps.map((label) => (
              <Step key={label}>
                <StepLabel>{label}</StepLabel>
              </Step>
            ))}
          </Stepper>
          <Box mt={2}>{getStepContent(activeStep)}</Box>
          <Box mt={2}>
            <Button disabled={activeStep === 0} onClick={handleBack}>
              Back
            </Button>
            {activeStep === steps.length - 1 ? (
              <Button variant="contained" color="primary" type="submit">
                {isEdit ? 'Update' : 'Save'}
              </Button>
            ) : (
              <Button variant="contained" color="primary" onClick={handleNext}>
                Next
              </Button>
            )}
          </Box>
        </Box>
      </form>
    </MainCard>
  );
}
