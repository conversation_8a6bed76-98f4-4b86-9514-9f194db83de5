import React, { useEffect, useState } from 'react';
import axios from 'axios';
import {
  Button,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  TextField,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Box
} from '@mui/material';
import { DatePicker, LocalizationProvider } from '@mui/x-date-pickers/';
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';
import { toast, ToastContainer } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import { API_BASE_URL } from 'api/categories.service';
import CustomerTable from 'sections/apps/customer/CustomerTable';
import { getUserPermissions } from 'utils/permissionUtils';
import dayjs from 'dayjs';
import useAuth from 'hooks/useAuth';
import { addUser, deleteUser, editUser, getAllUsers } from './apis/user.service';

export default function UsersTable() {
  const { user } = useAuth();
  const [users, setUsers] = useState([]);
  const [openDialog, setOpenDialog] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [currentUser, setCurrentUser] = useState(null);

  //role based access
  const permissions = getUserPermissions(user);
  const canAdd = permissions?.Add?.Users || false;
  const canEdit = permissions?.Edit?.Users || false;
  const canDelete = permissions?.Delete?.Users || false;

  useEffect(() => {
    fetchUsers();
  }, []);

  const fetchUsers = async () => {
    try {
      const response = await getAllUsers();
      setUsers(response);
    } catch (error) {
      console.error('Failed to fetch users:', error);
      toast.error('Failed to fetch users');
    }
  };

  const handleAddOrEdit = async () => {
    try {
      if (isEditing) {
        await editUser(currentUser.id, currentUser);
        toast.success('User updated successfully!');
      } else {
        await addUser(currentUser);
        toast.success('User added successfully!');
      }
      setOpenDialog(false);
      fetchUsers();
    } catch (error) {
      console.error('Failed to save user:', error);
      toast.error('Failed to save user');
    }
  };

  const handleDelete = async (id) => {
    if (!canDelete) {
      toast.error('You do not have permission to delete Users.');
      return;
    }
    const confirmDelete = window.confirm('Are you sure you want to delete this user?');
    if (confirmDelete) {
      try {
        await deleteUser(id);
        toast.success('User deleted successfully!');
        fetchUsers();
      } catch (error) {
        console.error('Failed to delete user:', error);
        toast.error('Failed to delete user');
      }
    }
  };

  const openAddDialog = () => {
    if (!canAdd) {
      toast.error('You do not have permission to Add Users.');
      return;
    }
    setCurrentUser({
      id: 0,
      firstname: '',
      middlename: '',
      lastname: '',
      email: '',
      mobile_number: '',
      password: '',
      date_of_birth: null,
      status: 'Active'
    });
    setIsEditing(false);
    setOpenDialog(true);
  };

  const openEditDialog = (user) => {
    if (!canEdit) {
      toast.error('You do not have permission to edit Users.');
      return;
    }
    setCurrentUser({
      ...user,
      date_of_birth: user.date_of_birth ? dayjs(user.date_of_birth) : null
    });
    setIsEditing(true);
    setOpenDialog(true);
  };
  const columns = [
    { accessorKey: 'fullname', header: 'Full Name', showByDefault: true },
    { accessorKey: 'email', header: 'Email', showByDefault: true },
    { accessorKey: 'mobile_number', header: 'Mobile Number', showByDefault: true },
    { accessorKey: 'gender', header: 'Gender', showByDefault: false },
    { accessorKey: 'about', header: 'About', showByDefault: false },
    { accessorKey: 'skills', header: 'Skills', showByDefault: false },
    { accessorKey: 'monthlyDonationGoal', header: 'Monthly Donation Goal', showByDefault: true },
    { accessorKey: 'primary_motivation', header: 'Primary Motivation', showByDefault: false },
    { accessorKey: 'events_participation', header: 'Events Participation', showByDefault: false },
    { accessorKey: 'interests', header: 'Interests', showByDefault: false },
    {
      accessorKey: 'dob',
      header: 'Date of Birth',
      showByDefault: true,
      cell: (cell) => cell.row.original.date_of_birth ? new Date(cell.row.original.date_of_birth).toLocaleDateString() : "-"
    },
    { accessorKey: 'status', header: 'Status', showByDefault: true },
    {
      accessorKey: 'createdAt',
      header: 'Created At',
      showByDefault: true,
      cell: (cell) => {
        const formattedDate = dayjs(cell.row.original.createdAt).format('DD-MM-YYYY hh:mm A');
        return <span>{formattedDate}</span>;
      }
    },
    {
      accessorKey: 'updatedAt',
      header: 'Updated At',
      showByDefault: true,
      cell: (cell) => {
        const formattedDate = dayjs(cell.row.original.updatedAt).format('DD-MM-YYYY hh:mm A');
        return <span>{formattedDate}</span>;
      }
    },
    // {
    //   id: 'actions',
    //   header: 'Actions',
    //   cell: (cell) => (
    //     <div style={{ display: 'flex', gap: '0.5rem' }}>
    //       <Button variant="contained" color="primary" onClick={() => openEditDialog(cell.row.original)}>
    //         Edit
    //       </Button>
    //       <Button variant="contained" color="secondary" onClick={() => handleDelete(cell.row.original.id)}>
    //         Delete
    //       </Button>
    //     </div>
    //   )
    // }
  ];

  return (
    <div>
      <CustomerTable data={users} columns={columns} modalToggler={openAddDialog} category={'User'} />
      <Dialog
        open={openDialog}
        onClose={() => setOpenDialog(false)}
        PaperProps={{
          sx: {
            borderRadius: '16px'
          }
        }}
      >
        <DialogTitle>{isEditing ? 'Edit User' : 'Add User'}</DialogTitle>
        <DialogContent>
          <TextField
            autoFocus
            margin="dense"
            label="First Name"
            type="text"
            fullWidth
            variant="outlined"
            value={currentUser?.firstname || ''}
            onChange={(e) => setCurrentUser({ ...currentUser, firstname: e.target.value })}
          />
          <TextField
            margin="dense"
            label="Middle Name"
            type="text"
            fullWidth
            variant="outlined"
            value={currentUser?.middlename || ''}
            onChange={(e) => setCurrentUser({ ...currentUser, middlename: e.target.value })}
          />
          <TextField
            margin="dense"
            label="Last Name"
            type="text"
            fullWidth
            variant="outlined"
            value={currentUser?.lastname || ''}
            onChange={(e) => setCurrentUser({ ...currentUser, lastname: e.target.value })}
          />
          <Box sx={{ display: 'flex', alignItems: 'center', gap: '1rem' }}>
            <LocalizationProvider dateAdapter={AdapterDayjs}>
              <DatePicker
                label="Date of Birth"
                format="DD/MM/YYYY"
                value={currentUser?.date_of_birth || null}
                onChange={(newDate) => setCurrentUser({ ...currentUser, date_of_birth: dayjs(newDate) })}
                renderInput={(params) => <TextField {...params} fullWidth margin="dense" variant="outlined" />}
              />
            </LocalizationProvider>
            <TextField
              margin="dense"
              label="Email"
              type="email"
              fullWidth
              variant="outlined"
              value={currentUser?.email || ''}
              onChange={(e) => setCurrentUser({ ...currentUser, email: e.target.value })}
            />
          </Box>
          <TextField
            margin="dense"
            label="Mobile Number"
            type="text"
            fullWidth
            variant="outlined"
            value={currentUser?.mobile_number || ''}
            onChange={(e) => setCurrentUser({ ...currentUser, mobile_number: e.target.value })}
          />
          <TextField
            margin="dense"
            label="Password"
            type="password"
            fullWidth
            variant="outlined"
            value={currentUser?.password || ''}
            onChange={(e) => setCurrentUser({ ...currentUser, password: e.target.value })}
          />

          <FormControl fullWidth margin="dense" variant="outlined">
            <InputLabel>Status</InputLabel>
            <Select
              value={currentUser?.status || ''}
              onChange={(e) => setCurrentUser({ ...currentUser, status: e.target.value })}
              label="Status"
            >
              <MenuItem value="Active">Active</MenuItem>
              <MenuItem value="Inactive">Inactive</MenuItem>
            </Select>
          </FormControl>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setOpenDialog(false)} color="primary">
            Cancel
          </Button>
          <Button onClick={handleAddOrEdit} color="primary">
            {isEditing ? 'Update' : 'Add'}
          </Button>
        </DialogActions>
      </Dialog>

      <ToastContainer />
    </div>
  );
}
