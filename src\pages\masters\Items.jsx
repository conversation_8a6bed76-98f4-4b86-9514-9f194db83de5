import React, { useEffect, useState } from 'react';
import {
  Button,
  <PERSON>alog,
  DialogActions,
  DialogContent,
  DialogTitle,
  TextField,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Grid
} from '@mui/material';
import CustomerTable from 'sections/apps/customer/CustomerTable';
import { getUserPermissions } from 'utils/permissionUtils';
import { toast, ToastContainer } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import { getAllItems, addItems, deleteItems, updateItems } from './apis/items.service';
import TableActions from 'components/TableActions';
import useAuth from 'hooks/useAuth';
import dayjs from 'dayjs';
import { Formik } from 'formik';
import * as yup from 'yup';

export default function ItemsTable() {
  const { user } = useAuth();
  const [items, setItems] = useState([]);
  const [openDialog, setOpenDialog] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [currentItem, setCurrentItem] = useState({ name: '', description: '', status: 'Active' });

  const permissions = getUserPermissions(user);
  const canAdd = permissions?.Add?.Items || true;
  const canEdit = permissions?.Edit?.Items || true;
  const canDelete = permissions?.Delete?.Items || true;

  useEffect(() => {
    fetchItems();
  }, []);

  const fetchItems = async () => {
    try {
      const response = await getAllItems();
      setItems(response);
    } catch (error) {
      console.error('Failed to fetch items:', error);
      toast.error('Failed to fetch items');
    }
  };

  const handleAddOrEdit = async (values) => {
    try {
      if (isEditing) {
        await updateItems(values.id, values);
        toast.success('Item updated successfully!');
      } else {
        await addItems(values);
        toast.success('Item added successfully!');
      }
      setOpenDialog(false);
      fetchItems();
    } catch (error) {
      console.error('Failed to save item:', error);
      toast.error(error.response?.data?.message || 'Failed to save item');
    }
  };

  const handleDelete = async (id) => {
    const confirmDelete = window.confirm('Are you sure you want to delete this item?');
    if (confirmDelete) {
      try {
        await deleteItems(id);
        toast.success('Item deleted successfully!');
        fetchItems();
      } catch (error) {
        console.error('Failed to delete item:', error);
        toast.error('Failed to delete item');
      }
    }
  };

  const openAddDialog = () => {
    if (!canAdd) {
      toast.error('You do not have permission to Add Items.');
      return;
    }
    setCurrentItem({ id: 0, name: '', status: 'Active' });
    setIsEditing(false);
    setOpenDialog(true);
  };

  const openEditDialog = (item) => {
    if (!canEdit) {
      toast.error('You do not have permission to edit Items.');
      return;
    }
    setCurrentItem(item);
    setIsEditing(true);
    setOpenDialog(true);
  };

  const columns = [
    {
      accessorKey: 'name',
      header: 'Name',
      showByDefault: true,
      cell: ({ cell }) => (
        <Button variant="text" onClick={() => openEditDialog(cell.row.original)}>
          {cell.row.original.name}
        </Button>
      )
    },
    { accessorKey: 'description', header: 'Description', showByDefault: true },
    { accessorKey: 'status', header: 'Status', showByDefault: true },
    {
      accessorKey: 'createdAt',
      header: 'Created At',
      showByDefault: true,
      cell: (cell) => {
        const formattedDate = dayjs(cell.row.original.createdAt).format('DD-MM-YYYY hh:mm A');
        return <span>{formattedDate}</span>;
      }
    },
    {
      accessorKey: 'updatedAt',
      header: 'Updated At',
      showByDefault: true,
      cell: (cell) => {
        const formattedDate = dayjs(cell.row.original.updatedAt).format('DD-MM-YYYY hh:mm A');
        return <span>{formattedDate}</span>;
      }
    },
    {
      id: 'actions',
      header: 'Action',
      cell: (cell, { row }) => <TableActions handleEditClick={openEditDialog} cell={cell} handleDeleteClick={handleDelete} />
    }
  ];

  return (
    <div>
      <CustomerTable data={items} columns={columns} modalToggler={openAddDialog} category="Item" />

      <Dialog
        open={openDialog}
        onClose={(event, reason) => {
          if (reason !== 'backdropClick' && reason !== 'escapeKeyDown') {
            setOpenDialog(false);
          }
        }}
        maxWidth="50%"
        PaperProps={{
          sx: {
            top: 0,
            position: 'absolute',
            borderRadius: '16px',
            width: '100%',
            maxWidth: '600px'
          }
        }}
      >
        <Formik
          initialValues={{
            ...currentItem
          }}
          validationSchema={yup.object().shape({
            name: yup.string().max(100, 'Name must be at most 100 characters long').required('Name is required'),
            description: yup.string().max(300, 'Description must be at most 300 characters long'),

            status: yup.string().required('Please select a status')
          })}
          onSubmit={async (values) => {
            await handleAddOrEdit(values);
          }}
        >
          {({ values, handleSubmit, handleChange, handleBlur, setFieldValue, touched, errors }) => (
            <form autoComplete="off" onSubmit={handleSubmit}>
              <DialogTitle>{isEditing ? 'Edit Item' : 'Add Item'}</DialogTitle>
              <DialogContent>
                <Grid container spacing={1}>
                  <Grid item xs={12}>
                    <TextField
                      autoFocus
                      margin="dense"
                      label="Name"
                      type="text"
                      fullWidth
                      variant="outlined"
                      name="name"
                      required
                      // value={currentSkill?.name || ''}
                      // onChange={(e) => setCurrentSkill({ ...currentSkill, name: e.target.value })}
                      onChange={handleChange}
                      onBlur={handleBlur}
                      value={values.name}
                      error={Boolean(touched.name && errors.name)}
                      helperText={touched.name && errors.name}
                    />
                  </Grid>

                  <Grid item xs={12}>
                    <TextField
                      multiline
                      margin="dense"
                      label="Description"
                      type="text"
                      fullWidth
                      variant="outlined"
                      name="description"
                      // value={currentSkill?.name || ''}
                      // onChange={(e) => setCurrentSkill({ ...currentSkill, name: e.target.value })}
                      onChange={handleChange}
                      onBlur={handleBlur}
                      value={values.description}
                      error={Boolean(touched.description && errors.description)}
                      helperText={touched.description && errors.description}
                    />
                  </Grid>

                  <Grid item xs={12}>
                    <FormControl fullWidth margin="dense" variant="outlined">
                      <InputLabel>Status</InputLabel>
                      <Select value={values?.status || ''} onChange={handleChange} label="Status" name="status">
                        <MenuItem value="Active">Active</MenuItem>
                        <MenuItem value="Inactive">Inactive</MenuItem>
                      </Select>
                    </FormControl>
                  </Grid>
                </Grid>
              </DialogContent>
              <DialogActions>
                <Button onClick={() => setOpenDialog(false)} color="primary">
                  Cancel
                </Button>
                <Button type="submit" color="primary">
                  {isEditing ? 'Update' : 'Add'}
                </Button>
              </DialogActions>
            </form>
          )}
        </Formik>
      </Dialog>

      <ToastContainer />
    </div>
  );
}
