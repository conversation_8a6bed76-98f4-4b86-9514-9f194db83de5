import PropTypes from 'prop-types';
// material-ui
import { styled } from '@mui/material/styles';
import Box from '@mui/material/Box';
import Button from '@mui/material/Button';
import CardMedia from '@mui/material/CardMedia';
import Stack from '@mui/material/Stack';

// third-party
import { useDropzone } from 'react-dropzone';

// project import
import RejectionFiles from './RejectionFiles';
import PlaceholderContent from './PlaceholderContent';
import { Typography } from '@mui/material';
const MAX_FILE_SIZE = 2 * 1024 * 1024; // 2MB

const DropzoneWrapper = styled('div')(({ theme }) => ({
  outline: 'none',
  overflow: 'hidden',
  position: 'relative',
  padding: theme.spacing(5, 1),
  borderRadius: theme.shape.borderRadius,
  transition: theme.transitions.create('padding'),
  background: theme.palette.background.paper,
  border: `1px dashed ${theme.palette.secondary.main}`,
  '&:hover': { opacity: 0.72, cursor: 'pointer' }
}));

// ==============================|| UPLOAD - SINGLE FILE ||============================== //

export default function SingleFileUpload({
  error,
  file,
  setFieldValue,
  setFieldError,
  sx,
  maxfileSize = 2 * 1024 * 1024,
  checkFileType = false,
  allowedTypes = []
}) {
  const { getRootProps, getInputProps, isDragActive, isDragReject, fileRejections } = useDropzone({
    accept: {
      'image/*': ['.jpeg', '.png'],
      'application/pdf': ['.pdf']
    },
    multiple: false,
    maxSize: maxfileSize,
    onDrop: (acceptedFiles, rejectedFiles) => {
      const fileErrors = [];
      if (checkFileType) {
        acceptedFiles.forEach((file) => {
          const fileExtension = file.name.split('.').pop().toLowerCase(); // Get the file extension in lowercase

          if (!allowedTypes.includes(fileExtension)) {
            fileErrors.push({
              code: 'invalid-file-type',
              message: `Invalid file format. Please upload a file of type: ${allowedTypes.join(', ')}. Your file is of type: ${fileExtension.toUpperCase()}.`
            });
          }
        });
      }
      if (fileErrors.length > 0) {
        setFieldError('files', fileErrors[0].message); // ✅ This prevents form submission
        return;
      }

      {
        fileRejections.length > 0 && (
          <Typography color="error" sx={{ mt: 1 }}>
            {fileRejections[0]?.errors[0]?.code === 'file-too-large'
              ? `File size exceeds the limit. Please upload a file smaller than ${(maxfileSize / (1024 * 1024)).toFixed(2)}MB. Your file is ${(fileRejections[0].file.size / (1024 * 1024)).toFixed(2)}MB.`
              : 'Invalid file format. Please upload only files with allowed types'}
          </Typography>
        );
      }
      setFieldValue(
        'files',
        acceptedFiles.map((file) => {
          return Object.assign(file, {
            preview: URL.createObjectURL(file)
          });
        })
      );
    }
  });

  const thumbs =
    file &&
    file.map((item) => {
      const splitFile = item.name.split('.');
      const fileExtension = splitFile[splitFile.length - 1];
      return (
        <CardMedia
          key={item.name}
          component={fileExtension === 'pdf' ? 'iframe' : 'img'}
          src={item.preview}
          sx={{
            top: 8,
            left: 8,
            borderRadius: 2,
            position: 'absolute',
            width: 'calc(100% - 16px)',
            height: 'calc(100% - 16px)',
            bgcolor: 'background.paper',
            objectFit: 'contain'
          }}
          onLoad={() => {
            URL.revokeObjectURL(item.preview);
          }}
        />
      );
    });

  const onRemove = () => {
    setFieldValue('files', null);
  };

  return (
    <Box sx={{ width: '100%', ...sx }}>
      <DropzoneWrapper
        {...getRootProps()}
        sx={{
          ...(isDragActive && { opacity: 0.72 }),
          ...((isDragReject || error) && {
            color: 'error.main',
            borderColor: 'error.light',
            bgcolor: 'error.lighter'
          }),
          ...(file && {
            padding: '12% 0'
          })
        }}
      >
        <input {...getInputProps()} />
        <PlaceholderContent />
        {thumbs}
      </DropzoneWrapper>

      {/* {fileRejections.length > 0 && <RejectionFiles fileRejections={fileRejections} />} */}
      {fileRejections.length > 0 && (
        <Typography color="error" sx={{ mt: 1 }}>
          {fileRejections[0]?.errors[0]?.code === 'file-too-large'
            ? `File size exceeds the limit. Please upload a file smaller than ${(maxfileSize / (1024 * 1024)).toFixed(2)}MB. Your file is ${(fileRejections[0].file.size / (1024 * 1024)).toFixed(2)}MB.`
            : `Invalid file format. ${checkFileType ? `Please upload a file of type: ${allowedTypes.join(', ')}.` : ''}`}
        </Typography>
      )}

      {file && file.length > 0 && (
        <Stack direction="row" justifyContent="flex-end" sx={{ mt: 1.5 }}>
          <Button variant="contained" color="error" onClick={onRemove}>
            Remove
          </Button>
        </Stack>
      )}
    </Box>
  );
}

SingleFileUpload.propTypes = { error: PropTypes.any, file: PropTypes.any, setFieldValue: PropTypes.any, sx: PropTypes.any };
