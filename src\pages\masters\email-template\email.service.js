import axiosServices from 'utils/axios_node';

export const API_BASE_URL = `${import.meta.env.VITE_APP_APPLICATION_API_URL}/api`;

export const getAllEmailTemplates = async () => {
  const response = await axiosServices.get(`${API_BASE_URL}/communication-email`);
  return response?.data;
};
export const getSingleEmailTemplate = async (id) => {
  const response = await axiosServices.get(`${API_BASE_URL}/communication-email/${id}`);
  return response?.data;
};
export const addEmailTemplate = async (payload) => {
  const response = await axiosServices.post(`${API_BASE_URL}/communication-email`, payload);
  return response?.data;
};
export const editEmailTemplate = async (id, payload) => {
  const response = await axiosServices.put(`${API_BASE_URL}/communication-email/${id}`, payload);
  return response?.data;
};
export const deleteEmailTemplate = async (id) => {
  const response = await axiosServices.delete(`${API_BASE_URL}/communication-email/${id}`);
  return response?.data;
};
export const sendTestEmails = async (templateId, emailList) => {
  const response = await axiosServices.post(`${API_BASE_URL}/communication-email/sendTestEmails/${templateId}`, emailList);
  return response?.data;
};
