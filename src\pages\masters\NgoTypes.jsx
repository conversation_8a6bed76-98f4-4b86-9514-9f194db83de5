import React, { useEffect, useState } from 'react';
import {
  Button,
  <PERSON>alog,
  DialogActions,
  DialogContent,
  DialogTitle,
  TextField,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Grid,
  CircularProgress,
  Backdrop
} from '@mui/material';
import CustomerTable from 'sections/apps/customer/CustomerTable';
import { getUserPermissions } from 'utils/permissionUtils';
import { toast, ToastContainer } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import { getAllNgoTypes, addNgoType, updateNgoType, deleteNgoType } from 'api/ngotypes.service';
import TableActions from 'components/TableActions';
import useAuth from 'hooks/useAuth';
import dayjs from 'dayjs';
import { Formik } from 'formik';
import * as yup from 'yup';
import CustomCircularLoaded from 'components/CustomCircularLoaded';

export default function NgoTypes() {
  const { user } = useAuth();
  const [ngoTypes, setNgoTypes] = useState([]);
  const [openDialog, setOpenDialog] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [currentNgoType, setCurrentNgoType] = useState(null);
  const [isLoading, setIsLoading] = useState(false);

  const permissions = getUserPermissions(user);
  const canAdd = permissions?.Add?.NgoTypes || false;
  const canEdit = permissions?.Edit?.NgoTypes || false;
  const canDelete = permissions?.Delete?.NgoTypes || false;

  useEffect(() => {
    fetchNgoTypes();
  }, []);

  const fetchNgoTypes = async () => {
    setIsLoading(true);
    try {
      const response = await getAllNgoTypes();
      setNgoTypes(response);
    } catch (error) {
      console.error('Failed to fetch NGO Types:', error);
      toast.error('Failed to fetch NGO Types');
    } finally {
      setIsLoading(false);
    }
  };

  const handleAddOrEdit = async (values) => {
    try {
      if (isEditing) {
        await updateNgoType(values.id, values);
        toast.success('NGO Type updated successfully!');
      } else {
        await addNgoType(values);
        toast.success('NGO Type added successfully!');
      }
      setOpenDialog(false);
      fetchNgoTypes();
    } catch (error) {
      console.error('Failed to save NGO Type:', error);
      toast.error(error.response?.data?.message || 'Failed to save NGO Type');
    }
  };

  const handleDelete = async (id) => {
    const confirmDelete = window.confirm('Are you sure you want to delete this NGO Type?');
    if (confirmDelete) {
      try {
        await deleteNgoType(id);
        toast.success('NGO Type deleted successfully!');
        fetchNgoTypes();
      } catch (error) {
        console.error('Failed to delete NGO Type:', error);
        toast.error(error.response?.data?.message || 'Failed to delete NGO Type');
      }
    }
  };

  const openAddDialog = () => {
    if (!canAdd) {
      toast.error('You do not have permission to Add NGO Type.');
      return;
    }
    setCurrentNgoType({ id: 0, name: '', description: '', status: 'Active' });
    setIsEditing(false);
    setOpenDialog(true);
  };

  const openEditDialog = (category) => {
    if (!canEdit) {
      toast.error('You do not have permission to edit Ngo Tyoe.');
      return;
    }
    setCurrentNgoType(category);
    setIsEditing(true);
    setOpenDialog(true);
  };

  const columns = [
    {
      accessorKey: 'name',
      header: 'Name',
      showByDefault: true,
      cell: ({ cell }) => (
        <Button className="text-align-left" variant="text" onClick={() => openEditDialog(cell.row.original)}>
          {cell.row.original.name}
        </Button>
      )
    },
    { accessorKey: 'description', header: 'Description', showByDefault: true },
    { accessorKey: 'status', header: 'Status', showByDefault: true },
    {
      accessorKey: 'createdAt',
      header: 'Created At',
      showByDefault: true,
      cell: (cell) => {
        const formattedDate = dayjs(cell.row.original.createdAt).format('DD-MM-YYYY hh:mm A');
        return <span>{formattedDate}</span>;
      }
    },
    {
      accessorKey: 'updatedAt',
      header: 'Updated At',
      showByDefault: true,
      cell: (cell) => {
        const formattedDate = dayjs(cell.row.original.updatedAt).format('DD-MM-YYYY hh:mm A');
        return <span>{formattedDate}</span>;
      }
    },
    {
      id: 'actions',
      header: 'Action',
      cell: (cell, { row }) => (
        <TableActions handleEditClick={openEditDialog} cell={cell} showDelete={false} handleDeleteClick={handleDelete} />
      )
    }
  ];

  return (
    <div>
      {isLoading ? (
        <CustomCircularLoaded open={isLoading} />
      ) : (
        <CustomerTable data={ngoTypes} columns={columns} modalToggler={openAddDialog} category="NGO Type" />
      )}

      <Dialog
        open={openDialog}
        onClose={(event, reason) => {
          if (reason !== 'backdropClick' && reason !== 'escapeKeyDown') {
            setOpenDialog(false);
          }
        }}
        maxWidth="50%"
        PaperProps={{
          sx: {
            top: 0,
            position: 'absolute',
            borderRadius: '16px',
            width: '100%',
            maxWidth: '600px'
          }
        }}
      >
        <Formik
          initialValues={{
            ...currentNgoType
          }}
          validationSchema={yup.object().shape({
            name: yup.string().max(100, 'Name must be at most 100 characters long').required('Name is required'),
            description: yup.string().max(300, 'Description must be at most 300 characters long'),

            status: yup.string().required('Please select a status')
          })}
          onSubmit={async (values) => {
            await handleAddOrEdit(values);
          }}
        >
          {({ values, handleSubmit, handleChange, handleBlur, setFieldValue, touched, errors }) => (
            <form autoComplete="off" onSubmit={handleSubmit}>
              <DialogTitle>{isEditing ? 'Edit NGO Type' : 'Add NGO Type'}</DialogTitle>
              <DialogContent>
                <Grid container spacing={1}>
                  <Grid item xs={12}>
                    <TextField
                      autoFocus
                      margin="dense"
                      label="Name"
                      type="text"
                      fullWidth
                      inputProps={{ maxLength: 200 }}
                      variant="outlined"
                      name="name"
                      onChange={handleChange}
                      onBlur={handleBlur}
                      value={values.name}
                      error={Boolean(touched.name && errors.name)}
                      helperText={touched.name && errors.name}
                    />
                  </Grid>
                  <Grid item xs={12}>
                    <TextField
                      multiline
                      margin="dense"
                      label="Description"
                      type="text"
                      fullWidth
                      variant="outlined"
                      name="description"
                      onChange={handleChange}
                      onBlur={handleBlur}
                      value={values.description}
                      error={Boolean(touched.description && errors.description)}
                      helperText={touched.description && errors.description}
                    />
                  </Grid>
                  <Grid item xs={12}>
                    <FormControl fullWidth margin="dense" variant="outlined">
                      <InputLabel>Status</InputLabel>
                      <Select value={values?.status} onChange={handleChange} label="Status" name="status">
                        <MenuItem value="Active">Active</MenuItem>
                        <MenuItem value="Inactive">Inactive</MenuItem>
                      </Select>
                    </FormControl>
                  </Grid>
                </Grid>
              </DialogContent>
              <DialogActions>
                <Button onClick={() => setOpenDialog(false)} color="primary">
                  Cancel
                </Button>
                <Button type="submit" color="primary">
                  {isEditing ? 'Update' : 'Add'}
                </Button>
              </DialogActions>
            </form>
          )}
        </Formik>
      </Dialog>

      <ToastContainer />
    </div>
  );
}
