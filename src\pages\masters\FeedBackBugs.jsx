import React, { useEffect, useState } from 'react';
import { addProfileQueries, getProfileQueries, updateProfileQueries } from 'sections/apps/profiles/account/tabs.service';
import {
  Tabs,
  Tab,
  Box,
  CircularProgress,
  Typography,
  Dialog,
  DialogTitle,
  DialogContent,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  DialogActions,
  Button,
  Chip,
  TextField,
  Grid
} from '@mui/material';
import CustomerTable from 'sections/apps/customer/CustomerTable';
import { toast, ToastContainer } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import dayjs from 'dayjs';
import MainCard from 'components/MainCard';
import useAuth from 'hooks/useAuth';
import ProfileCard from 'sections/apps/profiles/user/ProfileCard';
import { getNgoById } from 'api/ngos.service';
import { calculateProfileCompletion } from 'utils/permissionUtils';

const statusColors = {
  Pending: '#FFC107',
  'In Progress': '#17A2B8',
  Completed: '#28A745',
  Rejected: '#DC3545'
};

const FeedBackBugsTable = () => {
  const { user } = useAuth();
  const [queries, setQueries] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [activeTab, setActiveTab] = useState('feedbacks');
  const [openDialog, setOpenDialog] = useState(false);
  const [selectedQuery, setSelectedQuery] = useState(null);
  const [newStatus, setNewStatus] = useState('');
  const [description, setDescription] = useState('');
  const [profileCompletePercentage, setProfileCompletePercentage] = useState(0);
  const [ngoInfo, setNgoInfo] = useState(null);

  const statusOptions = ['Pending', 'In Progress', 'Completed', 'Rejected'];

  useEffect(() => {
    fetchQueries();
  }, [activeTab]);

  useEffect(() => {
    const fetchNgoDataAndCalculate = async () => {
      if (user?.ngo_id) {
        try {
          const ngoRecords = await getNgoById(user?.ngo_id);
          setNgoInfo(ngoRecords);
          const profilepercentage = await calculateProfileCompletion(ngoRecords);
          setProfileCompletePercentage(profilepercentage);
        } catch (error) {
          console.error('Error fetching NGO data:', error);
        }
      } else {
        setProfileCompletePercentage(100);
      }
    };

    fetchNgoDataAndCalculate();
  }, [user?.ngo_id]);

  const fetchQueries = async () => {
    setLoading(true);
    try {
      const response = await getProfileQueries(user?.ngo_id, user?.roleInfo?.name === 'DR_Management' ? null : user?.id, activeTab);
      setQueries(response);
    } catch (err) {
      setError('Failed to fetch data. Please try again later.');
    } finally {
      setLoading(false);
    }
  };

  const handleOpenDialog = (query) => {
    setSelectedQuery(query);
    setNewStatus(query.status);
    setOpenDialog(true);
  };

  const handleCloseDialog = () => {
    setOpenDialog(false);
    setSelectedQuery(null);
    setNewStatus('');
    setDescription('');
  };

  const handleUpdateStatus = async () => {
    if (!description.trim()) return;

    const payload = {
      description,
      type: activeTab, // 'feedbacks' or 'bugs'
      portal_user_id: user?.id,
      ngo_id: user?.ngo_id
    };

    try {
      await addProfileQueries(payload);
      handleCloseDialog();

      toast.success(`Your ${activeTab === 'feedbacks' ? 'feedback' : 'bug description'} submiteed Successfully!`);
      setDescription('');
      setTimeout(() => {
        fetchQueries();
      }, 2000);
    } catch (error) {
      console.error('Error updating status:', error);
    }
  };

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" height="200px">
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Typography color="error" align="center">
        {error}
      </Typography>
    );
  }

  const columns = [
    ...(user?.ngo_id ? [] : [{ accessorKey: 'ngoInfo.name', header: 'NGO Name', showByDefault: true }]),
    { accessorKey: 'userInfo.fullname', header: 'User Name', showByDefault: true },
    { accessorKey: 'portaluserInfo.fullname', header: 'Portal User Name', showByDefault: true },

    { accessorKey: 'description', header: 'Description', showByDefault: true },
    // {
    //   accessorKey: 'status',
    //   header: 'Status',
    //   showByDefault: true,
    //   cell: ({ cell }) => {
    //     const status = cell.getValue();
    //     return <Chip label={status} sx={{ backgroundColor: statusColors[status], color: '#fff', fontWeight: 500 }} />;
    //   }
    // },
    {
      accessorKey: 'createdAt',
      header: 'Created On',
      showByDefault: true,
      cell: ({ cell }) => dayjs(cell.row.original.createdAt).format('DD-MM-YYYY')
    },
    {
      accessorKey: 'updatedAt',
      header: 'Last Updated',
      showByDefault: true,
      cell: ({ cell }) => dayjs(cell.row.original.updatedAt).format('DD-MM-YYYY')
    }
  ];

  return (
    <>
      {user?.ngo_id && ngoInfo && profileCompletePercentage < 100 && (
        <Grid item xs={12} sx={{ marginBottom: 2 }}>
          <ProfileCard ngoInfo={ngoInfo} profileCompletePercentage={profileCompletePercentage} />
        </Grid>
      )}
      <MainCard>
        <Tabs value={activeTab} onChange={(_, newValue) => setActiveTab(newValue)}>
          <Tab label="Feedback" value="feedbacks" />
          <Tab label="Bugs" value="bugs" />
        </Tabs>

        {user?.ngo_id ? (
          <CustomerTable data={queries} columns={columns} category={activeTab} modalToggler={handleOpenDialog} />
        ) : (
          <CustomerTable data={queries} columns={columns} category={'feedbackandbugs'} />
        )}

        <Dialog
          open={openDialog}
          onClose={(event, reason) => {
            if (reason !== 'backdropClick' && reason !== 'escapeKeyDown') {
              handleCloseDialog();
            }
          }}
          maxWidth="50%"
          PaperProps={{
            sx: {
              top: 0,
              position: 'absolute',
              borderRadius: '16px',
              width: '100%',
              maxWidth: '600px'
            }
          }}
        >
          <DialogTitle>Add {activeTab === 'feedbacks' ? 'Feedback Description' : 'Bug Description'}</DialogTitle>
          <DialogContent>
            <TextField
              label={activeTab === 'feedbacks' ? 'Feedback Description' : 'Bug Description'}
              fullWidth
              multiline
              rows={4}
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              sx={{ marginTop: 2 }}
              placeholder={`Enter the ${activeTab === 'feedbacks' ? 'feedback' : 'bug'} description`}
            />
          </DialogContent>
          <DialogActions sx={{ marginRight: 2 }}>
            <Button onClick={handleCloseDialog} color="secondary">
              Cancel
            </Button>
            <Button
              onClick={handleUpdateStatus}
              color="primary"
              variant="contained"
              disabled={user?.ngo_id && profileCompletePercentage < 100}
            >
              Update
            </Button>
          </DialogActions>
        </Dialog>
        <ToastContainer autoClose={6000} />
      </MainCard>
    </>
  );
};

export default FeedBackBugsTable;
