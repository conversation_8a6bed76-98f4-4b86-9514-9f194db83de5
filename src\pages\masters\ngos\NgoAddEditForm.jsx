import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { <PERSON><PERSON>, Card, CardContent, Grid, Step, StepLabel, Stepper, Typography } from '@mui/material';
import { ToastContainer, toast } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import { getLoggedInNGOInfo, updateNGO, addNGO } from 'sections/apps/profiles/profile.service';
import TabLocation from 'sections/apps/profiles/account/TabAccount';
import TabProfile from 'sections/apps/profiles/account/TabProfile';
import TabOrganizationInfo from 'sections/apps/profiles/account/TabPersonal';
import { getSessionStorageItem } from 'utils/permissionUtils';

const steps = ['Basic Information', 'About NGO', 'Location and Categories'];

export default function NGOAddEditForm() {
  const [activeStep, setActiveStep] = useState(0);
  const [ngoDetails, setNgoDetails] = useState({
    basicInfo: {},
    aboutNGO: {},
    locationCategories: {}
  });
  const navigate = useNavigate();
  const userInfo = getSessionStorageItem('user');

  useEffect(() => {
    if (userInfo?.ngo_id) {
      fetchNGODetails();
    }
  }, []);

  const fetchNGODetails = async () => {
    try {
      const response = await getLoggedInNGOInfo(userInfo.ngo_id);
      setNgoDetails({
        basicInfo: {
          name: response.name,
          point_of_contact_name: response.point_of_contact_name,
          email: response.email,
          point_of_contact_mobile_number: response.point_of_contact_mobile_number,
          pan: response.pan
        },
        aboutNGO: {
          vision: response.vision,
          mission: response.mission,
          about_us: response.about_us,
          website_url: response.website_url,
          darpan_id: response.darpan_id
        },
        locationCategories: {
          place_name: response.place_name,
          state: response.state,
          pincode: response.pincode,
          latitude: response.latitude,
          longitude: response.longitude,
          full_address: response.full_address,
          categories: response.categories
        }
      });
    } catch (error) {
      console.error('Error fetching NGO details:', error);
      toast.error('Failed to fetch NGO details');
    }
  };

  const handleNext = () => {
    setActiveStep((prevActiveStep) => prevActiveStep + 1);
  };

  const handleBack = () => {
    setActiveStep((prevActiveStep) => prevActiveStep - 1);
  };

  const handleSubmit = async () => {
    try {
      const combinedNGODetails = {
        ...ngoDetails.basicInfo,
        ...ngoDetails.aboutNGO,
        ...ngoDetails.locationCategories
      };

      if (userInfo?.ngo_id) {
        await updateNGO(userInfo.ngo_id, combinedNGODetails);
        toast.success('NGO profile updated successfully');
      } else {
        const { ngoRecord } = await addNGO(combinedNGODetails);
        toast.success('NGO registered successfully');
        navigate(`/masters/ngos/add/${ngoRecord.id}`);
      }
      navigate('/masters/ngos');
    } catch (error) {
      console.error('Error submitting NGO details:', error);
      toast.error('Failed to submit NGO details');
    }
  };

  const getStepContent = (step) => {
    switch (step) {
      case 0:
        return <TabProfile />;
      case 1:
        return <TabOrganizationInfo />;
      case 2:
        return <TabLocation />;
      default:
        return 'Unknown step';
    }
  };

  return (
    <Card>
      <CardContent>
        <Typography variant="h5" gutterBottom>
          NGO Registration
        </Typography>
        <Stepper activeStep={activeStep} alternativeLabel>
          {steps.map((label) => (
            <Step key={label}>
              <StepLabel>{label}</StepLabel>
            </Step>
          ))}
        </Stepper>
        <Grid container spacing={3}>
          <Grid item xs={12}>
            {getStepContent(activeStep)}
          </Grid>
          <Grid item xs={12}>
            <div style={{ marginTop: '20px', display: 'flex', justifyContent: 'space-between' }}>
              <Button variant="contained" color="secondary" onClick={handleBack} disabled={activeStep === 0}>
                Back
              </Button>
              <Button variant="contained" color="primary" onClick={activeStep === steps.length - 1 ? handleSubmit : handleNext}>
                {activeStep === steps.length - 1 ? 'Submit' : 'Next'}
              </Button>
            </div>
          </Grid>
        </Grid>
      </CardContent>
      <ToastContainer />
    </Card>
  );
}
