import axiosServices from 'utils/axios_node';

const API_BASE_URL = `${import.meta.env.VITE_APP_APPLICATION_API_URL}/api`;

export const getAllNGOs = async (statusParam, pageName) => {
  const queryParams = new URLSearchParams();

  if (pageName) queryParams.append('pageName', pageName);

  const url = `${API_BASE_URL}/ngos${statusParam ? `/${statusParam}` : ''}${queryParams.toString() ? `?${queryParams}` : ''}`;

  const response = await axiosServices.get(url);
  return response?.data;
};

export const insertDocumentList = async (payload) => {
  const response = await axiosServices.post(`${API_BASE_URL}/documents-master`, payload);
  return response?.data;
};

export const updateDocumentList = async (id, payload) => {
  const response = await axiosServices.put(`${API_BASE_URL}/documents-master/${id}`, payload);
  return response?.data;
};

export const getAllUserSkills = async (pageName = '') => {
  const pageNameParam = pageName ? `?pageName=${pageName}` : '';
  const response = await axiosServices.get(`${API_BASE_URL}/skills${pageNameParam}`);
  return response?.data;
};

// export const getFilteredNGO = async (queryParams, page, limit) => {
//   const response = await axiosServices.get(`${API_BASE_URL}/ngos/filters?${queryParams}&page=${page}&limit=${limit}`);
//   return response?.data;
// };
export const getFilteredNGO = async (queryParams, page, limit, globalFilter, portalUserId) => {
  let url = `${API_BASE_URL}/ngos/filters?${queryParams}`;

  if (globalFilter) {
    if (queryParams) {
      url += `&searchTerm=${encodeURIComponent(globalFilter)}`;
    } else {
      url += `searchTerm=${encodeURIComponent(globalFilter)}`;
    }
  }

  if (page && limit) {
    url += `&page=${page}&limit=${limit}`;
  }

  try {
    const response = await axiosServices.get(url);
    return response?.data;
  } catch (error) {
    console.error('Error fetching filtered NGOs:', error.message);
    throw new Error('Failed to fetch filtered NGOs.');
  }
};
