import { Link, useSearchParams } from 'react-router-dom';
import { useState } from 'react';

// material-ui
import Grid from '@mui/material/Grid';
import Stack from '@mui/material/Stack';
import Typography from '@mui/material/Typography';
import Box from '@mui/material/Box';
import IconButton from '@mui/material/IconButton';
import { LeftOutlined, RightOutlined } from '@ant-design/icons';
import image1 from '../../../assets/images/widget/dashborad-3.jpg';


// project import
import useAuth from 'hooks/useAuth';
import AuthWrapper from 'sections/auth/AuthWrapper';
import FirebaseRegister from 'sections/auth/jwt/AuthRegister';
import AuthClaimNgo from 'sections/auth/jwt/AuthClaimNgo';

const slides = [
  {
    image: image1,
    title: 'Join our community of change-makers',
    subtitle: 'Start your journey of making a difference today!'
  },
  {
    image: '/placeholder.svg?height=600&width=800',
    title: 'Create positive impact together',
    subtitle: 'Connect with like-minded individuals and organizations'
  },
  {
    image: '/placeholder.svg?height=600&width=800',
    title: 'Empower your cause',
    subtitle: 'Access tools and resources to amplify your mission'
  }
];

export default function Register() {
  const { isLoggedIn } = useAuth();
  const [searchParams] = useSearchParams();
  const auth = searchParams.get('auth');
  const [currentSlide, setCurrentSlide] = useState(0);

  const nextSlide = () => {
    setCurrentSlide((prev) => (prev + 1) % slides.length);
  };

  const prevSlide = () => {
    setCurrentSlide((prev) => (prev - 1 + slides.length) % slides.length);
  };

  return (
    <Box sx={{ display: 'flex', minHeight: '100vh' }}>
      {/* Left side - Image Slider */}
      <Box
        sx={{
          flex: 1,
          position: 'relative',
          bgcolor: 'primary.main',
          display: { xs: 'none', md: 'block' }
        }}
      >
        <Box
          sx={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            backgroundImage: `url(${slides[currentSlide].image})`,
            backgroundSize: 'cover',
            backgroundPosition: 'center',
            transition: 'opacity 0.5s ease-in-out'
          }}
        />
        <Box
          sx={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            bgcolor: 'rgba(0, 0, 0, 0.4)',
            display: 'flex',
            flexDirection: 'column',
            justifyContent: 'center',
            alignItems: 'center',
            color: 'white',
            padding: 4,
            textAlign: 'center'
          }}
        >
          <Typography variant="h3" component="h1" gutterBottom>
            {slides[currentSlide].title}
          </Typography>
          <Typography variant="h6">{slides[currentSlide].subtitle}</Typography>
        </Box>
        <IconButton
          onClick={prevSlide}
          sx={{
            position: 'absolute',
            left: 16,
            top: '50%',
            transform: 'translateY(-50%)',
            color: 'white',
            bgcolor: 'rgba(255, 255, 255, 0.1)',
            '&:hover': { bgcolor: 'rgba(255, 255, 255, 0.2)' }
          }}
        >
          <LeftOutlined />
        </IconButton>
        <IconButton
          onClick={nextSlide}
          sx={{
            position: 'absolute',
            right: 16,
            top: '50%',
            transform: 'translateY(-50%)',
            color: 'white',
            bgcolor: 'rgba(255, 255, 255, 0.1)',
            '&:hover': { bgcolor: 'rgba(255, 255, 255, 0.2)' }
          }}
        >
          <RightOutlined />
        </IconButton>
        <Stack
          direction="row"
          spacing={1}
          sx={{
            position: 'absolute',
            bottom: 24,
            left: '50%',
            transform: 'translateX(-50%)'
          }}
        >
          {slides.map((_, index) => (
            <Box
              key={index}
              sx={{
                width: 8,
                height: 8,
                borderRadius: '50%',
                bgcolor: currentSlide === index ? 'white' : 'rgba(255, 255, 255, 0.5)',
                cursor: 'pointer'
              }}
              onClick={() => setCurrentSlide(index)}
            />
          ))}
        </Stack>
      </Box>

      {/* Right side - Registration Form */}
      <Box sx={{ flex: 1, display: 'flex', flexDirection: 'column' }}>
        <AuthWrapper>
          <Grid container spacing={3}>
            <Grid item xs={12}>
              <Stack direction="row" justifyContent="space-between" alignItems="baseline" sx={{ mb: { xs: -0.5, sm: 0.5 } }}>
                <Typography variant="h3">Claim Your NGO</Typography>
                <Typography
                  component={Link}
                  to={isLoggedIn ? '/auth/login' : auth ? `/${auth}/login?auth=jwt` : '/login'}
                  variant="body1"
                  sx={{ textDecoration: 'none' }}
                  color="primary"
                >
                  Already have an account?
                </Typography>
              </Stack>
            </Grid>
            <Grid item xs={12}>
              <AuthClaimNgo />
            </Grid>
          </Grid>
        </AuthWrapper>
      </Box>
    </Box>
  );
}
