import axiosServices from 'utils/axios_node';

const API_BASE_URL = `${import.meta.env.VITE_APP_APPLICATION_API_URL}/api`;

export const getNotifications = async (newNgoId, type, typeId) => {
  const response = await axiosServices.get(`${API_BASE_URL}/ngo-notifications?ngoId=${newNgoId}type=${type}&typeId=${typeId}`);
  return response?.data;
};
export const addNotifications = async (payload) => {
  const response = await axiosServices.post(`${API_BASE_URL}/ngo-notifications`, payload);
  return response?.data;
};
export const patchNotifications = async (newNgoId, payload) => {
  const response = await axiosServices.patch(`${API_BASE_URL}/ngo-notifications/${newNgoId}`, payload);
  return response?.data;
};
export const getJournals = async (newNgoId) => {
  const response = await axiosServices.get(`${API_BASE_URL}/journals/${newNgoId}`);
  return response?.data;
};
export const getJournalsBySenderId = async (senderId) => {
  const response = await axiosServices.get(`${API_BASE_URL}/journals/sender/${senderId}`);
  return response?.data;
};
export const addJournals = async (payload) => {
  const response = await axiosServices.post(`${API_BASE_URL}/journals`, payload);
  return response?.data;
};
export const patchJournals = async (newNgoId, payload) => {
  const response = await axiosServices.patch(`${API_BASE_URL}/journals/${newNgoId}`, payload);
  return response?.data;
};
export const deleteJournals = async (id) => {
  const response = await axiosServices.delete(`${API_BASE_URL}/journals/${id}`);
  return response?.data;
};
export const addAssignJournals = async (senderId, assigneeId, selectedNgoIds) => {
  const response = await axiosServices.post(
    `${API_BASE_URL}/journals/assignAndReassignJournals?senderId=${senderId}&assigneeId=${assigneeId}`,
    selectedNgoIds
  );
  return response?.data;
};

export const getFullNGOStats = async () => {
  const response = await axiosServices.get(`${API_BASE_URL}/ngos/ngoStats/fullNgoStats`);
  return response?.data;
};
export const getFullCampaignStats = async () => {
  const response = await axiosServices.get(`${API_BASE_URL}/campaigns/stats/fullcampaignStats`);
  return response?.data;
};

export const getNGOStats = async (assigneeId) => {
  const response = await axiosServices.get(`${API_BASE_URL}/ngos/ngoStats/allStats?assignee_id=${assigneeId}`);
  return response?.data;
};

export const getCampaignStats = async (ngoId) => {
  const response = await axiosServices.get(`${API_BASE_URL}/campaigns/stats/campaignStats?ngo_id=${ngoId}`);
  return response?.data;
};


//send notification fucntions
