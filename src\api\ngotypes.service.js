import axiosServices from 'utils/axios_node';

export const API_BASE_URL = `${import.meta.env.VITE_APP_APPLICATION_API_URL}/api`;

export const getAllNgoTypes = async (pageName = '') => {
  const response = await axiosServices.get(`${API_BASE_URL}/ngo-types?pageName=${pageName}`);
  return response?.data;
};

export const addNgoType = async (payload) => {
  const response = await axiosServices.post(`${API_BASE_URL}/ngo-types`, payload);
  return response?.data;
};

export const updateNgoType = async (id, payload) => {
  const response = await axiosServices.put(`${API_BASE_URL}/ngo-types/${id}`, payload);
  return response?.data;
};

export const deleteNgoType = async (id) => {
  const response = await axiosServices.delete(`${API_BASE_URL}/ngo-types/${id}`);
  return response?.data;
};
