import { useEffect, useState } from 'react';
import { useLocation, Link, Outlet, useParams } from 'react-router-dom';

// material-ui
import Box from '@mui/material/Box';
import Tab from '@mui/material/Tab';
import Tabs from '@mui/material/Tabs';

// project import
import MainCard from 'components/MainCard';
import Breadcrumbs from 'components/@extended/Breadcrumbs';
import { APP_DEFAULT_PATH } from 'config';

// assets
import ContainerOutlined from '@ant-design/icons/ContainerOutlined';
import FileTextOutlined from '@ant-design/icons/FileTextOutlined';
import LockOutlined from '@ant-design/icons/LockOutlined';
import SettingOutlined from '@ant-design/icons/SettingOutlined';
import TeamOutlined from '@ant-design/icons/TeamOutlined';
import UserOutlined from '@ant-design/icons/UserOutlined';
import { BankOutlined, CommentOutlined, FileOutlined, MessageOutlined } from '@ant-design/icons';
import useAuth from 'hooks/useAuth';
import { getNgoById } from 'api/ngos.service';
import { Typography } from '@mui/material';

// ==============================|| PROFILE - ACCOUNT ||============================== //

export default function AccountProfile() {
  const { pathname } = useLocation();
  const { newNgoId } = useParams();
  const { user } = useAuth();

  let selectedTab = 0;
  let breadcrumbTitle = '';
  let breadcrumbHeading = '';
  const [ngoDetails, setNgoDetails] = useState(null);

  const fetchNGODetails = async (ngo_id) => {
    try {
      const ngoRecords = await getNgoById(ngo_id);
      setNgoDetails(ngoRecords);
    } catch (error) {
      console.error(error);
    }
  };

  useEffect(() => {
    fetchNGODetails(newNgoId);
  }, [user]);

  switch (pathname) {
    case `/masters/ngos/edit/basic/${newNgoId}`:
      breadcrumbTitle = 'Basic Information';
      breadcrumbHeading = 'Basic Information';
      selectedTab = 1;
      break;
    case `/masters/ngos/edit/documents/${newNgoId}`:
      breadcrumbTitle = 'Documents';
      breadcrumbHeading = 'Documents';
      selectedTab = 3;
      break;
    case `/masters/ngos/edit/personal/${newNgoId}`:
      breadcrumbTitle = 'About NGO';
      breadcrumbHeading = 'About NGO';
      selectedTab = 2;
      break;

    case `/masters/ngos/edit/add-comments/${newNgoId}`:
      breadcrumbTitle = 'NGO Verification';
      breadcrumbHeading = 'NGO Verification';
      selectedTab = 6;
      break;
    case `/masters/ngos/edit/socials/${newNgoId}`:
      breadcrumbTitle = 'Socials';
      breadcrumbHeading = 'Socials';
      selectedTab = 4;
      break;

    case `/masters/ngos/edit/bank-accounts/${newNgoId}`:
      breadcrumbTitle = 'Bank Accounts';
      breadcrumbHeading = 'Bank Accounts';
      selectedTab = 5;
      break;

    case `/masters/ngos/edit/ngoprofile/${newNgoId}`:
    default:
      breadcrumbTitle = 'Profile';
      breadcrumbHeading = 'Profile';
      selectedTab = 0;
  }

  const [value, setValue] = useState(selectedTab);

  const handleChange = (event, newValue) => {
    setValue(newValue);
  };

  let breadcrumbLinks = [
    { title: 'Manage', to: `/masters/ngos/` },
    { title: 'NGO', to: `/masters/ngos/edit/ngoprofile/${newNgoId}` },
    { title: breadcrumbTitle }
  ];
  if (selectedTab === 0) {
    breadcrumbLinks = [
      { title: 'Manage', to: `/masters/ngos/` },
      { title: 'NGO', to: `/masters/ngos/edit/ngoprofile/${newNgoId}` },
      { title: breadcrumbTitle }
    ];
  }

  useEffect(() => {
    if (pathname === `/masters/ngos/edit/ngoprofile/${newNgoId}`) {
      setValue(0);
    } else {
      setValue(selectedTab);
    }
  }, [pathname]);
  return (
    <>
      <Breadcrumbs custom heading={breadcrumbHeading} links={breadcrumbLinks} />
      <MainCard border={false} boxShadow>
        <Box
          sx={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            backgroundColor: '#f5f5f5', // Light gray background
            padding: '12px 16px',
            borderRadius: '8px',
            boxShadow: '0px 2px 6px rgba(0, 0, 0, 0.1)', // Subtle shadow
            border: '1px solid #ddd',
            mb: 2
          }}
        >
          <Typography variant="h6" sx={{ fontWeight: 'bold', color: 'primary' }}>
            NGO Name: <span style={{ color: '#1976d2' }}>{ngoDetails?.name || 'N/A'}</span>
          </Typography>
          <Typography variant="h6" sx={{ fontWeight: 'bold', color: '#333' }}>
            Source: <span style={{ color: '#d32f2f' }}>{ngoDetails?.source || 'N/A'}</span>
          </Typography>
        </Box>

        <Box sx={{ borderBottom: 1, borderColor: 'divider', width: '100%' }}>
          <Tabs
            className="ngo-tabs"
            value={value}
            onChange={handleChange}
            variant="scrollable"
            scrollButtons="auto"
            aria-label="account profile tab"
          >
            <Tab
              label="Profile"
              component={Link}
              to={`/masters/ngos/edit/ngoprofile/${newNgoId}`}
              icon={<UserOutlined />}
              iconPosition="start"
            />
            <Tab
              label="Basic Information"
              component={Link}
              to={`/masters/ngos/edit/basic/${newNgoId}`}
              icon={<UserOutlined />}
              iconPosition="start"
            />
            <Tab
              label="About NGO"
              component={Link}
              to={`/masters/ngos/edit/personal/${newNgoId}`}
              icon={<FileTextOutlined />}
              iconPosition="start"
            />

            <Tab
              label="Documents"
              component={Link}
              to={`/masters/ngos/edit/documents/${newNgoId}`}
              icon={<FileOutlined />}
              iconPosition="start"
            />
            <Tab
              label="Socials"
              component={Link}
              to={`/masters/ngos/edit/socials/${newNgoId}`}
              icon={<MessageOutlined />}
              iconPosition="start"
            />

            <Tab
              label="Bank Details"
              component={Link}
              to={`/masters/ngos/edit/bank-accounts/${newNgoId}`}
              icon={<BankOutlined />}
              iconPosition="start"
            />

            {user.roleInfo.name.startsWith('DR') && (
              <Tab
                label="NGO Verification"
                component={Link}
                to={`/masters/ngos/edit/add-comments/${newNgoId}`}
                icon={<CommentOutlined />}
                iconPosition="start"
              />
            )}
          </Tabs>
        </Box>
        <Box sx={{ mt: 2.5 }}>
          <Outlet />
        </Box>
      </MainCard>
    </>
  );
}
