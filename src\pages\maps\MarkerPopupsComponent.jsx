import React, { useEffect, useState } from 'react';
import { useTheme } from '@mui/material/styles';
import MainCard from 'components/MainCard';
import MapContainerStyled from 'components/third-party/map/MapContainerStyled';
import MarkersPopups from 'sections/maps/MarkersPopups';
import axios from 'axios';
import { ThemeMode } from 'config';
import { getData } from 'api/map.service';

const MAPBOX_THEMES = {
  light: 'mapbox://styles/mapbox/light-v10',
  dark: 'mapbox://styles/mapbox/dark-v10',
  streets: 'mapbox://styles/mapbox/streets-v11'
};

const mapConfiguration = {
  mapboxAccessToken: import.meta.env.VITE_APP_MAPBOX_ACCESS_TOKEN,
  minZoom: 1
};

export default function MarkerPopupsComponent({ headerName, url }) {
  const theme = useTheme();
  const [ngoData, setNgoData] = useState([]);

  useEffect(() => {
    const fetchData = async () => {
      try {
        const response = await getData(url);
        const filteredData = response.filter((ngo) => ngo.latitude && ngo.longitude);
        const transformedData = filteredData.map((ngo) => ({
          latlng: [parseFloat(ngo.latitude), parseFloat(ngo.longitude)],
          name: ngo.name
        }));
        setNgoData(transformedData);
      } catch (error) {
        console.error('Error fetching NGO data:', error);
      }
    };

    fetchData();
  }, [url]);

  return (
    <MainCard title={headerName}>
      <MapContainerStyled>
        {ngoData && (
          <MarkersPopups
            {...mapConfiguration}
            data={ngoData}
            mapStyle={theme.palette.mode === ThemeMode.DARK ? MAPBOX_THEMES.streets : MAPBOX_THEMES.streets}
          />
        )}
      </MapContainerStyled>
    </MainCard>
  );
}
