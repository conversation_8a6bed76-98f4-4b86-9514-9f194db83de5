import React, { useEffect, useState } from 'react';
import axios from 'axios';
import CustomerTable from 'sections/apps/customer/CustomerTable';
import {
  Button,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Grid,
  Stack,
  Typography,
  FormHelperText,
  CircularProgress,
  Box
} from '@mui/material';
import { toast, ToastContainer } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import TableActions from 'components/TableActions';
import useAuth from 'hooks/useAuth';
import {
  addCollectionService,
  deleteCollectionService,
  fetchCollectionsService,
  updateCollectionService
} from './apis/collections.service';
import dayjs from 'dayjs';
import * as yup from 'yup';
import { Formik } from 'formik';
import { getConvertedFileName, getUserPermissions } from 'utils/permissionUtils';
import UploadSingleFile from 'components/third-party/dropzone/SingleFile';
import CustomProductTable from 'sections/apps/customer/CustomProductTable';

export default function CollectionsTable() {
  const { user } = useAuth();
  const [collections, setCollections] = useState([]);
  const [openDialog, setOpenDialog] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [currentCollection, setCurrentCollection] = useState({
    name: '',
    slug: '',
    description: '',
    status: 'Active'
  });

  // Role-based access
  const permissions = getUserPermissions(user);
  const canAdd = permissions?.Add?.Collections || false;
  const canEdit = permissions?.Edit?.Collections || false;
  const canDelete = permissions?.Delete?.Collections || false;

  useEffect(() => {
    fetchCollections();
  }, []);

  const fetchCollections = async () => {
    setIsLoading(true);
    try {
      const data = await fetchCollectionsService();
      setCollections(data);
    } catch (error) {
      console.error('Failed to fetch collections:', error);
      toast.error('Failed to fetch collections');
    } finally {
      setIsLoading(false);
    }
  };

  const handleAddOrEdit = async (values) => {
    let formData = new FormData();

    if (values.files && values.files.length > 0) {
      const convertedFileName = getConvertedFileName(values.files[0].name);
      if (isEditing) {
        delete values.imageName;
      }
      formData.append('imageName', convertedFileName);
      formData.append('file', values.files[0], convertedFileName);
    }

    Object.keys(values).forEach((key) => {
      if (key !== 'files') {
        formData.append(key, values[key]);
      }
    });
    try {
      if (isEditing) {
        const response = await updateCollectionService(values.id, formData);
        toast.success('Collection updated successfully!');
      } else {
        await addCollectionService(formData);
        toast.success('Collection added successfully!');
      }
      setOpenDialog(false);
      fetchCollections();
    } catch (error) {
      console.error('Failed to save collection:', error);
      toast.error(error.response?.data?.message || 'Failed to save collection');
    }
  };

  const handleDelete = async (id) => {
    if (!canDelete) {
      toast.error('You do not have permission to delete collections.');
      return;
    }
    const confirmDelete = window.confirm('Are you sure you want to delete this collection?');
    if (confirmDelete) {
      try {
        await deleteCollectionService(id);

        toast.success('Collection deleted successfully!');
        fetchCollections();
      } catch (error) {
        console.error('Failed to delete collection:', error);
        toast.error('Failed to delete collection');
      }
    }
  };

  const openAddDialog = () => {
    if (!canAdd) {
      toast.error('You do not have permission to Add Collections.');
      return;
    }
    setCurrentCollection({ id: 0, name: '', slug: '', description: '', status: 'Active' });
    setOpenDialog(true);
    setIsEditing(false);
  };

  const openEditDialog = (collection) => {
    if (!canEdit) {
      toast.error('You do not have permission to edit collections.');
      return;
    }
    setCurrentCollection(collection);
    setIsEditing(true);
    setOpenDialog(true);
  };

  const columns = [
    {
      accessorKey: 'name',
      header: 'Name',
      showByDefault: true,
      cell: ({ cell }) => {
        return (
          <Button variant="text" onClick={() => openEditDialog(cell.row.original)}>
            {cell.row.original.name}
          </Button>
        );
      }
    },
    { accessorKey: 'description', header: 'Description', showByDefault: true },
    { accessorKey: 'status', header: 'Status', showByDefault: true },
    {
      accessorKey: 'createdAt',
      header: 'Created At',
      showByDefault: true,
      cell: (cell) => {
        const formattedDate = dayjs(cell.row.original.createdAt).format('DD-MM-YYYY hh:mm A');
        return <span>{formattedDate}</span>;
      }
    },
    {
      accessorKey: 'updatedAt',
      header: 'Updated At',
      showByDefault: true,
      cell: (cell) => {
        const formattedDate = dayjs(cell.row.original.updatedAt).format('DD-MM-YYYY hh:mm A');
        return <span>{formattedDate}</span>;
      }
    },
    {
      id: 'actions',
      header: 'Action',
      cell: (cell) => {
        return <TableActions handleEditClick={openEditDialog} cell={cell} handleDeleteClick={handleDelete} />;
      }
    }
  ];

  return (
    <Box>
      {/* <CustomerTable data={collections} columns={columns} modalToggler={openAddDialog} category={'Collection'} /> */}

      {isLoading ? (
        <Box
          sx={{
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'center',
            textAlign: 'center'
          }}
        >
          <CircularProgress size={40} color="primary" />
          <Box sx={{ marginTop: 2, fontSize: '16px', color: 'text.secondary' }}>Loading your collection...</Box>
        </Box>
      ) : (
        <CustomProductTable data={collections} columns={columns} modalToggler={openAddDialog} category="Collection" statusList={[]} />
      )}

      <Dialog
        open={openDialog}
        onClose={(event, reason) => {
          if (reason !== 'backdropClick' && reason !== 'escapeKeyDown') {
            setOpenDialog(false);
          }
        }}
        maxWidth="50%"
        PaperProps={{
          sx: {
            top: 0,
            position: 'absolute',
            borderRadius: '16px',
            width: '100%',
            maxWidth: '600px'
          }
        }}
      >
        <Formik
          initialValues={currentCollection}
          validationSchema={yup.object().shape({
            name: yup.string().max(150, 'Name must be at most 150 characters long').required('Name is required'),
            description: yup.string().required('Description is required'),
            status: yup.string().required('Please select a status')
          })}
          onSubmit={async (values) => {
            await handleAddOrEdit(values);
          }}
        >
          {({ values, handleSubmit, setFieldValue, handleChange, handleBlur, touched, errors }) => (
            <form autoComplete="off" onSubmit={handleSubmit}>
              <DialogTitle>{isEditing ? 'Edit Collection' : 'Add Collection'}</DialogTitle>
              <DialogContent>
                <Grid container spacing={1}>
                  <Grid item xs={12}>
                    <TextField
                      autoFocus
                      margin="dense"
                      label="Name"
                      type="text"
                      fullWidth
                      required
                      variant="outlined"
                      name="name"
                      onChange={handleChange}
                      onBlur={handleBlur}
                      value={values.name}
                      error={Boolean(touched.name && errors.name)}
                      helperText={touched.name && errors.name}
                    />
                  </Grid>

                  <Grid item xs={12}>
                    <TextField
                      margin="dense"
                      label="Description"
                      type="text"
                      fullWidth
                      variant="outlined"
                      required
                      name="description"
                      onChange={handleChange}
                      onBlur={handleBlur}
                      value={values.description}
                      error={Boolean(touched.description && errors.description)}
                      helperText={touched.description && errors.description}
                    />
                  </Grid>

                  <Grid item xs={12}>
                    <FormControl required fullWidth margin="dense" variant="outlined">
                      <InputLabel>Status</InputLabel>
                      <Select value={values.status} onChange={handleChange} label="Status" name="status">
                        <MenuItem value="Active">Active</MenuItem>
                        <MenuItem value="Inactive">Inactive</MenuItem>
                      </Select>
                    </FormControl>
                  </Grid>

                  <Grid item xs={12}>
                    <Stack spacing={1.5} alignItems="center">
                      <UploadSingleFile setFieldValue={setFieldValue} file={values.files} error={touched.files && !!errors.files} />
                    </Stack>
                    {touched.files && errors.files && <FormHelperText error>{errors.files}</FormHelperText>}
                  </Grid>
                </Grid>
              </DialogContent>
              <DialogActions>
                <Button onClick={() => setOpenDialog(false)} color="primary">
                  Cancel
                </Button>
                <Button type="submit" color="primary">
                  {isEditing ? 'Update' : 'Add'}
                </Button>
              </DialogActions>
            </form>
          )}
        </Formik>
      </Dialog>

      <ToastContainer />
    </Box>
  );
}
