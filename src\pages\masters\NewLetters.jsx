import React, { useEffect, useState } from 'react';
import {
  Button,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  TextField,
  Grid
} from '@mui/material';
import { toast, ToastContainer } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import { Formik } from 'formik';
import * as yup from 'yup';
import CustomerTable from 'sections/apps/customer/CustomerTable';
import TableActions from 'components/TableActions';
import { fetchNewslettersService, addNewsletterService, updateNewsletterService, deleteNewsletterService } from 'api/newsletters.service';
import dayjs from 'dayjs';

export default function NewslettersTable() {
  const [newsletters, setNewsletters] = useState([]);
  const [openDialog, setOpenDialog] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [currentNewsletter, setCurrentNewsletter] = useState({
    email: '',
    description: ''
  });

  useEffect(() => {
    fetchNewsletters();
  }, []);

  const fetchNewsletters = async () => {
    try {
      const data = await fetchNewslettersService();
      setNewsletters(data);
    } catch (error) {
      console.error('Failed to fetch newsletters:', error);
      toast.error('Failed to fetch newsletters');
    }
  };

  const handleAddOrEdit = async (values) => {
    try {
      if (isEditing) {
        await updateNewsletterService(values.id, values);
        toast.success('Newsletter updated successfully!');
      } else {
        await addNewsletterService(values);
        toast.success('Newsletter added successfully!');
      }
      setOpenDialog(false);
      fetchNewsletters();
    } catch (error) {
      console.error('Failed to save newsletter:', error);
      toast.error(error.response?.data?.message || 'Failed to save newsletter');
    }
  };

  const handleDelete = async (id) => {
    const confirmDelete = window.confirm('Are you sure you want to delete this newsletter?');
    if (confirmDelete) {
      try {
        await deleteNewsletterService(id);
        toast.success('Newsletter deleted successfully!');
        fetchNewsletters();
      } catch (error) {
        console.error('Failed to delete newsletter:', error);
        toast.error('Failed to delete newsletter');
      }
    }
  };

  const openAddDialog = () => {
    setCurrentNewsletter({ email: '', description: '' });
    setOpenDialog(true);
    setIsEditing(false);
  };

  const openEditDialog = (newsletter) => {
    setCurrentNewsletter(newsletter);
    setIsEditing(true);
    setOpenDialog(true);
  };

  const columns = [
    {
      accessorKey: 'email',
      header: 'Email',
      cell: ({ cell }) => (
        <Button variant="text" onClick={() => openEditDialog(cell.row.original)}>
          {cell.row.original.email}
        </Button>
      )
    },
    {
      accessorKey: 'description',
      header: 'Description'
    },
    {
      accessorKey: 'createdAt',
      header: 'Created At',
      cell: (cell) => {
        const formattedDate = dayjs(cell.row.original.createdAt).format('DD-MM-YYYY hh:mm A');
        return <span>{formattedDate}</span>;
      }
    },
    {
      accessorKey: 'updatedAt',
      header: 'Updated At',
      cell: (cell) => {
        const formattedDate = dayjs(cell.row.original.updatedAt).format('DD-MM-YYYY hh:mm A');
        return <span>{formattedDate}</span>;
      }
    },
    {
      id: 'actions',
      header: 'Action',
      cell: (cell) => (
        <TableActions handleEditClick={openEditDialog} cell={cell} handleDeleteClick={handleDelete} />
      )
    }
  ];

  return (
    <div>
      <CustomerTable data={newsletters} columns={columns} modalToggler={openAddDialog} category="Newsletter" />

      <Dialog
        open={openDialog}
        onClose={(event, reason) => {
          if (reason !== 'backdropClick' && reason !== 'escapeKeyDown') {
            setOpenDialog(false);
          }
        }}
        maxWidth="50%"
        PaperProps={{
          sx: {
            top: 0,
            position: 'absolute',
            borderRadius: '16px',
            width: '100%',
            maxWidth: '600px'
          }
        }}
      >
        <Formik
          initialValues={currentNewsletter}
          validationSchema={yup.object().shape({
            email: yup.string().email('Invalid email address').required('Email is required'),
          })}
          onSubmit={async (values) => {
            await handleAddOrEdit(values);
          }}
        >
          {({ values, handleSubmit, handleChange, handleBlur, touched, errors }) => (
            <form autoComplete="off" onSubmit={handleSubmit}>
              <DialogTitle>{isEditing ? 'Edit Newsletter' : 'Add Newsletter'}</DialogTitle>
              <DialogContent>
                <Grid container spacing={1}>
                  <Grid item xs={12}>
                    <TextField
                      autoFocus
                      margin="dense"
                      label="Email"
                      type="email"
                      fullWidth
                      variant="outlined"
                      name="email"
                      onChange={handleChange}
                      onBlur={handleBlur}
                      value={values.email}
                      error={Boolean(touched.email && errors.email)}
                      helperText={touched.email && errors.email}
                    />
                  </Grid>
                  <Grid item xs={12}>
                    <TextField
                      margin="dense"
                      label="Description"
                      type="text"
                      fullWidth
                      variant="outlined"
                      name="description"
                      onChange={handleChange}
                      onBlur={handleBlur}
                      value={values.description}
                      error={Boolean(touched.description && errors.description)}
                      helperText={touched.description && errors.description}
                      multiline
                      rows={4}
                    />
                  </Grid>
                </Grid>
              </DialogContent>
              <DialogActions>
                <Button onClick={() => setOpenDialog(false)} color="primary">
                  Cancel
                </Button>
                <Button type="submit" color="primary">
                  {isEditing ? 'Update' : 'Add'}
                </Button>
              </DialogActions>
            </form>
          )}
        </Formik>
      </Dialog>

      <ToastContainer />
    </div>
  );
}
