import axiosServices from 'utils/axios_node';

const API_BASE_URL = `${import.meta.env.VITE_APP_APPLICATION_API_URL}/api`;

export const fetchFaqService = async () => {
  const response = await axiosServices.get(`${API_BASE_URL}/faqs`);
  return response.data;
};

export const addFaqService = async (faq) => {
  const response = await axiosServices.post(`${API_BASE_URL}/faqs`, faq);
  return response.data;
};

export const updateFaqService = async (id, faq) => {
  const response = await axiosServices.put(`${API_BASE_URL}/faqs/${id}`, faq);
  return response.data;
};

export const deleteFaqService = async (id) => {
  const response = await axiosServices.delete(`${API_BASE_URL}/faqs/${id}`);
  return response.data;
};
