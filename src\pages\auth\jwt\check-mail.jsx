import { Link, useSearchParams } from 'react-router-dom';

// material-ui
import Button from '@mui/material/Button';
import Grid from '@mui/material/Grid';
import Typography from '@mui/material/Typography';
import Box from '@mui/material/Box';

// project import
import useAuth from 'hooks/useAuth';
import { useTheme } from '@mui/system';
import AnimateButton from 'components/@extended/AnimateButton';
import AuthWrapper from 'sections/auth/AuthWrapper';
import ImageSlider from 'components/ImageSlider';


// ================================|| JWT - CHECK MAIL ||================================ //

export default function CheckMail() {
  const { isLoggedIn } = useAuth();
  const theme = useTheme();

  const [searchParams] = useSearchParams();
  const auth = searchParams.get('auth'); // get auth and set route based on that

  return (
    <Box sx={{ display: 'flex', minHeight: '100vh' }}>
      {/* Left side - Image Slider */}
      <ImageSlider />

      {/* Right side - Registration Form */}
      <Box sx={{ flex: 1, display: 'flex', flexDirection: 'column', bgcolor: theme.palette.primary.main }}>
    <AuthWrapper>
      <Grid container spacing={3}>
        <Grid item xs={12}>
          <Box sx={{ mb: { xs: -0.5, sm: 0.5 } }}>
            <Typography variant="h3" style={{color:"#fff"}}>Hi, Check Your Mail</Typography>
            <Typography color="primary" sx={{ mb: 0.5, mt: 1.25, color:"#fff" }}>
              We have sent a password recover instructions to your email. Do not forgot to check SPAM box.
            </Typography>
          </Box>
        </Grid>
        <Grid item xs={12}>
          <AnimateButton>
            <Button
              component={Link}
              to={isLoggedIn ? '/auth/login' : auth ? `/${auth}/login?auth=jwt` : '/login'}
              disableElevation
              fullWidth
              size="large"
              type="submit"
              variant="contained"
                  sx={{ bgcolor: '#4D4D4D' }}
            >
              Sign in
            </Button>
          </AnimateButton>
        </Grid>
      </Grid>
    </AuthWrapper>
    </Box>
    </Box>
  );
}
