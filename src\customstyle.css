.ngo-tabs a {
  color: #4d4d4d !important;
}

.ngo-link {
  text-decoration: none;
}

.ngo-link:hover {
  text-decoration: underline;
}

.text-color-grey {
  color: #a4a4a4 !important;
}

.text-align-left {
  text-align: left;
}

.rdw-link-modal-input {
  margin-top: 0 !important;
}

.rdw-link-modal > span :first-child {
  background-color: #ffae5f;
  color: #fff;
}

.sticky-column {
  position: sticky;
  right: 0;
  background: white;
  z-index: 10;
  box-shadow: -2px 0 5px rgba(0, 0, 0, 0.1);
}

table > tbody > tr > td > button {
  text-align: left !important;
  justify-content: flex-start !important;
}

.change_svg > svg {
  fill: #a9a9a9;
  /* color:#333 */
}

.wrap-text {
  white-space: normal; /* Allow text to wrap */
  word-wrap: break-word; /* Break words when they exceed the column width */
  word-break: break-word;
}

.MuiTableCell-root {
  max-width: 150px; /* Set max width of column */
  overflow-wrap: break-word; /* Wrap the text after max width is exceeded */
  word-wrap: break-word; /* Ensure the text breaks to a new line */
  white-space: normal;
}

.MuiTableCell-head {
  font-weight: 500;
}

.table-document-width {
  max-width: 150px;
}

.MuiAutocomplete-hasPopupIcon.MuiAutocomplete-hasClearIcon.css-1vnpv6f-MuiAutocomplete-root .MuiOutlinedInput-root {
  padding-right: 0;
}

.bouncing-balls {
  display: flex;
  gap: 10px;
}

.ball {
  width: 12px;
  height: 12px;
  background-color: #ffae5f;
  border-radius: 50%;
  animation: bounce 1.2s infinite ease-in-out;
}

.ball:nth-child(1) {
  animation-delay: 0s;
}

.ball:nth-child(2) {
  animation-delay: 0.2s;
}

.ball:nth-child(3) {
  animation-delay: 0.4s;
}

.ball:nth-child(4) {
  animation-delay: 0.6s;
}

@keyframes bounce {
  0%,
  80%,
  100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-15px);
  }
}
