import React, { useEffect, useState } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { IndeterminateCheckbox } from 'components/third-party/react-table';

import {
  Button,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  TextField,
  Stack,
  Tab,
  Tabs,
  Divider,
  Box,
  Chip,
  CircularProgress,
  Rating,
  Tooltip,
  IconButton,
  Typography
} from '@mui/material';
import Autocomplete from '@mui/material/Autocomplete';
import { getDocumentUploadStatus, getUserPermissions } from 'utils/permissionUtils';
import { toast, ToastContainer } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import { API_BASE_URL } from 'api/categories.service';
import { NGO_STATUS_LIST } from 'utils/statusconstans';
import TableActions from 'components/TableActions';
import MainCard from 'components/MainCard';
import { Link } from 'react-router-dom';
import CustomTableCheckBox from 'sections/apps/customer/CustomTableCheckBox';
import useAuth from 'hooks/useAuth';
import {
  assignAdminToNgos,
  assignfeaturedToNgos,
  assignGradeToNgo,
  deleteMultipleNgos,
  deleteNgo,
  getAdmins,
  getAllNgos,
  getGrades,
  getNgos,
  inactiveNgo
} from 'api/ngos.service';
import { deleteAdminService, fetchAdminsService, fetchStates, updateAdminService } from '../apis/portal-users.service';
import { DeleteOutlined, MenuUnfoldOutlined, StarFilled, StarOutlined, StopOutlined } from '@ant-design/icons';
import dayjs from 'dayjs';
import { updateUsingPatchNGO } from 'sections/apps/profiles/profile.service';
import { editProducts, getProducts } from '../products/product.service';
import { getAllCampaigns, getCampaigns, patchCampaign } from 'api/campaigns.service';
import { addAssignJournals } from '../apis/notification.service';
import { getFilteredNGO } from './ngo.service';
import CustomCircularLoaded from 'components/CustomCircularLoaded';

export default function NgoTable() {
  const { user } = useAuth();
  const roleInfo = JSON.parse(localStorage.getItem('user'))?.roleInfo || {};
  const [ngos, setNgos] = useState([]);
  const [gradeList, setGradeList] = useState([]);
  const [isEdit, SetIsEdit] = useState([]);
  const [totalCount, setTotalCount] = useState(0);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [rowSelection, setRowSelection] = useState({});
  const [sorting, setSorting] = useState([{ id: 'id', desc: true }]);

  const [statusCounts, setStatusCounts] = useState({});
  const [loading, setLoading] = useState(false);
  const navigate = useNavigate();

  const [assignDialogOpen, setAssignDialogOpen] = useState(false);
  const [featuredDialogOpen, setFeaturedDialogOpen] = useState(false);
  const [admins, setAdmins] = useState([]);
  const [selectedAdmin, setSelectedAdmin] = useState(null);
  const [selectedNgoId, setselectedNgoId] = useState(null);
  const [featureMode, setFeatureMode] = useState('add');

  //grades
  const [assignGradeDialogOpen, setAssignGradeDialogOpen] = useState(false);
  const [grades, setGrades] = useState([]);
  const [selectedGrade, setSelectedGrade] = useState(null);
  const [rating, setRating] = useState(1);

  const [states, setStates] = useState([]);
  const [portalUsers, setPortalUsers] = useState([]);

  //filter based states
  const [filters, setFilters] = useState({
    source: '',
    state: '',
    assignee_id: '',
    ngo_status: ''
  });

  const [filteredTotalCount, setFilteredTotalCount] = useState(totalCount);
  const [filteredCurrentPage, setFilteredCurrentPage] = useState(currentPage);
  const [filteredPageSize, setFilteredPageSize] = useState(pageSize);
  const [globalFilter, setGlobalFilter] = useState('');
  const [isFilterLoading, setFilterIsLoading] = useState(false);
  const [searchFilteredData, setFilteredData] = useState();

  //role based access
  const permissions = getUserPermissions(user);
  const canAdd = permissions?.Add?.NGOS || false;
  const canEdit = permissions?.Edit?.NGOS || false;
  const canDelete = permissions?.Delete?.NGOS || false;
  const [searchParams] = useSearchParams();
  let alertStatus = searchParams.get('alertStatus') ? searchParams.get('alertStatus') : null;
  const statusList = NGO_STATUS_LIST;

  useEffect(() => {
    fetchGrades();
    fetchAdmins();
    fetchStatesAndUsers();
  }, []);

  const [appliedFilters, setAppliedFilters] = useState({ ...filters, globalFilter });
  const [tempSelected, setTempSelected] = useState(filters.source ? filters.source.split(',') : []);

  useEffect(() => {
    fetchNgos(alertStatus ? alertStatus : statusList[tabValue]);
  }, [currentPage, pageSize]); // Only triggers on page changes

  useEffect(() => {
    const newFilters = { ...filters, globalFilter };

    if (JSON.stringify(appliedFilters) !== JSON.stringify(newFilters)) {
      setCurrentPage(1);
      setAppliedFilters(newFilters);

      fetchNgos(alertStatus ? alertStatus : statusList[tabValue]);
      return;
    }
  }, [filters, globalFilter]);

  const applyFilters = async (searchTerm) => {
    const hasFilters = Object.values(filters).some((value) => value);
    if (!hasFilters && !searchTerm) {
      setFilteredData(ngos);
      setFilteredTotalCount(totalCount);
      setFilteredCurrentPage(currentPage);
      setFilteredPageSize(pageSize);
      return;
    }

    // const query = Object.entries(filters)
    //   .filter(([_, value]) => value)
    //   .map(([key, value]) => `${encodeURIComponent(key)}=${encodeURIComponent(value)}`)
    //   .join('&');
    const query = [
      ...Object.entries(filters)
        .filter(([_, value]) => value)
        .map(([key, value]) => `${encodeURIComponent(key)}=${encodeURIComponent(value)}`),
      user?.roleInfo?.name === 'DR_Staff' ? `portalUserId=${encodeURIComponent(user?.id)}` : null
    ]
      .filter(Boolean) // Remove null values
      .join('&');
    try {
      setFilterIsLoading(true);
      const response = await getFilteredNGO(
        query,
        filteredCurrentPage,
        filteredPageSize,
        searchTerm,
        user?.roleInfo?.name == 'Dr_Staff' ? user?.id : null
      );
      setFilteredData(response.ngos);
      //   setTotalCount(response.totalCount);
      setFilteredTotalCount(response.totalCount);
    } catch (error) {
      console.error('Error applying filters:', error);
    } finally {
      setFilterIsLoading(false);
    }
  };

  function TabPanel({ children, value, index, ...other }) {
    return (
      <div
        role="tabpanel"
        hidden={value !== index}
        id={`product-details-tabpanel-${index}`}
        aria-labelledby={`product-details-tab-${index}`}
        {...other}
      >
        {value === index && <Box>{children}</Box>}
      </div>
    );
  }

  const fetchGrades = async () => {
    try {
      const response = await getGrades();
      setGrades(response);
    } catch (error) {
      console.error('Failed to fetch grades:', error);
      toast.error('Failed to fetch grades');
    }
  };

  const fetchStatesAndUsers = async () => {
    try {
      const response = await fetchStates();
      setStates(response);
    } catch (error) {
      console.error('Error fetching filter options:', error);
    }
  };

  const openAssignGradeDialog = () => {
    fetchGrades();
    setAssignGradeDialogOpen(true);
  };
  const fetchNgos = async (status = 'All', isTabChanged = 'no') => {
    setLoading(true);
    if (alertStatus) {
      setTabValue(statusList.findIndex((st) => st === alertStatus));
    }

    let newFilters = { ...filters, source: '', state: '', assignee_id: '', ngo_status: '' };
    newFilters = isTabChanged === 'yes' ? newFilters : filters;
    try {
      const params = {
        page: isTabChanged === 'yes' ? 1 : currentPage,
        limit: pageSize,
        status: status,

        ...newFilters,
        search: isTabChanged === 'yes' ? '' : globalFilter
      };

      if (roleInfo.name === 'DR_Staff') {
        params.assignee_id = user.id;
      }

      const { ngos, totalCount, statusCounts } = await getNgos(params);

      setNgos(ngos);
      setStatusCounts(statusCounts);
      setTotalCount(totalCount);
    } catch (error) {
      console.error('Failed to fetch NGOs:', error);
      toast.error('Failed to fetch data');
    } finally {
      setLoading(false);
    }
  };
  const fetchAdmins = async () => {
    try {
      const response = await getAdmins();
      const sortedAdmins = response.sort((a, b) => a.fullname.localeCompare(b.fullname));
      setAdmins(sortedAdmins);
      setPortalUsers(sortedAdmins);
    } catch (error) {
      console.error('Failed to fetch admins:', error);
      toast.error('Failed to fetch admins');
    }
  };

  const openAssignDialog = () => {
    if (Object.keys(rowSelection).length === 0) {
      toast.error('Please select at least one NGO to assign.');
      return;
    }
    setAssignDialogOpen(true);
  };
  const openFeaturedDialog = () => {
    setFeaturedDialogOpen(true);
  };

  const handleAssignSubmit = async () => {
    if (!selectedAdmin) {
      toast.error('Please choose the appropriate DR staff to assign.');
      return;
    }

    const selectedNgoIds = Object.keys(rowSelection).map((id) => parseInt(id, 10));
    if (selectedNgoIds.length === 0) {
      toast.error('Please select at least one NGO to assign.');
      return;
    }
    try {
      const journalResponse = await addAssignJournals(4232, selectedAdmin.id, selectedNgoIds);

      const response = await assignAdminToNgos(selectedAdmin.id, selectedNgoIds);
      if (response) {
        toast.success('Staff assigned successfully!');
        setAssignDialogOpen(false);
        setRowSelection({});
        fetchNgos(statusList[tabValue]);
        setSelectedAdmin(null);
      }
    } catch (error) {
      console.error('Failed to assign staff:', error);
      toast.error(error?.response?.data?.message || 'Failed to assign staff');
    }
  };

  const handleMultipleNgoDelete = async () => {
    const selectedNgoIds = Object.keys(rowSelection).map((id) => parseInt(id, 10));
    if (selectedNgoIds.length === 0) {
      toast.error('Please select an NGO(s) to delete');
      return;
    }

    const confirmed = window.confirm(`Are you sure you want to delete the selected NGO(s)?
    This action is irreversible, and all related data will be permanently removed.`);
    if (!confirmed) return;

    try {
      const { status } = await deleteMultipleNgos(selectedNgoIds);
      setRowSelection({});
      if (status) {
        toast.success('NGOs deleted successfully');
      } else {
        toast.error('Failed to delete ngos, please try again later!');
      }

      fetchNgos(statusList[tabValue]);
    } catch (error) {
      console.error('Failed to delete selected NGOs:', error);
      toast.error(error?.response?.data?.message || 'Failed to delete selected NGOs');
    }
  };

  const handleFeaturedSubmit = async () => {
    if (!selectedNgoId) {
      toast.error('No NGO selected!');
      return;
    }
    try {
      const response = await assignfeaturedToNgos(selectedNgoId, featureMode);
      if (response.statusText === 'OK') {
        featureMode === 'add' ? toast.success('NGO listed as featured') : toast.success('NGO unmarked as featured');
        setFeaturedDialogOpen(false);
        fetchNgos(statusList[tabValue]);
      }
    } catch (error) {
      console.error('Failed to mark ngo as featured :', error);
      toast.error('Failed to mark ngo as featured');
    }
  };
  const handleAssignGradeSubmit = async () => {
    if (!selectedGrade) {
      toast.error('Please select a grade to assign');
      return;
    }

    const selectedNgoIds = Object.keys(rowSelection).map((id) => parseInt(id, 10));
    if (selectedNgoIds.length === 0) {
      toast.error('Please select NGO to assign grades');
      return;
    }
    if (selectedNgoIds.length > 1) {
      toast.error('Please select One NGO to assign grades');
      return;
    }

    try {
      const response = await assignGradeToNgo(selectedNgoIds[0], selectedGrade, rating);

      if (response.status === 200) {
        toast.success('Grades assigned successfully!');
        setAssignGradeDialogOpen(false);
        setRowSelection({});
        fetchNgos(statusList[tabValue]);
        setSelectedGrade(null);
        setRating(1);
      }
    } catch (error) {
      console.error('Failed to assign grades:', error);
      toast.error('Failed to assign grades');
    }
  };

  const handleAddOrEdit = async () => {
    navigate('add/basic');
  };

  const handleDelete = async (id) => {
    if (!canDelete) {
      toast.error('You do not have permission to delete NGOS.');
      return;
    }
    const confirmDelete = window.confirm(
      'Are you sure you want to delete this NGO and its related products, campaigns, and staffmembers and every other information will be deleted permanently. This action cannot be undone.'
    );
    if (confirmDelete) {
      try {
        await deleteNgo(id);
        toast.success('NGO deleted successfully!');
        fetchNgos();
      } catch (error) {
        console.error('Failed to delete NGO:', error);
        toast.error('Failed to delete NGO');
      }
    }
  };

  const handleSetInactive = async (ngoId) => {
    try {
      const confirmation = window.confirm(
        'Are you sure you want to set the NGO and its related products, campaigns, and staff as inactive? This action cannot be undone.'
      );
      if (!confirmation) {
        return;
      }

      if (ngoId) {
        const response = await inactiveNgo(ngoId);
        if (response.status) {
          await updateUsingPatchNGO(ngoId, { ngo_status: 'Inactive' }, 'NgoTable');
          fetchNgos(statusList[tabValue]);
          toast.success('The NGO, along with its related staff members, products, and campaigns, has been successfully set to inactive.');
        }
      }
    } catch (error) {
      toast.error('Failed to set the NGO and its related entities as inactive. Please try again.');
      console.error('Error setting entities to inactive:', error);
    }
  };

  const openAddDialog = () => {
    if (!canAdd) {
      toast.error('You do not have permission to Add NGOS.');
      return;
    }
    setCurrentNgo({
      fullname: '',
      email: '',
      password: '',
      role_id: 2,
      point_of_contact_mobile_number: '',
      current_address: '',
      status: 'Active',
      darpan_id: '',
      pan: '',
      type_of_ngo: '',
      website_url: '',
      date_of_establishment: null,
      claimed_ngo: 'No'
    });
    setIsEditing(false);
    setOpenDialog(true);
  };
  const handleEditClick = (ngo) => {
    navigate(`/masters/ngos/edit/ngoprofile/${ngo.id}`);
  };

  const columns = [
    {
      id: 'id',
      header: ({ table }) => (
        <IndeterminateCheckbox
          style={{ color: '#b9b9b9' }}
          {...{
            checked: table.getIsAllRowsSelected(),
            indeterminate: table.getIsSomeRowsSelected(),
            onChange: table.getToggleAllRowsSelectedHandler()
          }}
        />
      ),
      cell: ({ row }) => (
        <IndeterminateCheckbox
          style={{ color: '#b9b9b9' }}
          {...{
            checked: row.getIsSelected(),
            disabled: !row.getCanSelect(),
            indeterminate: row.getIsSomeSelected(),
            onChange: row.getToggleSelectedHandler()
          }}
        />
      )
    },
    {
      accessorKey: 'assigned_on',
      header: 'Date Created',
      showByDefault: true,
      cell: (cell) => {
        const assignedDate = cell.row.original.source === "Internet" ? cell.row.original.assigned_on : cell.row.original.createdAt;
        const formattedDate = assignedDate ? dayjs(assignedDate).format('DD-MM-YYYY') : "-";
        return <span>{formattedDate}</span>;
      }
    },
    {
      accessorKey: 'updatedAt',
      header: 'Last Updated',
      showByDefault: true,
      cell: (cell) => {
        const assignedDate = cell.row.original.source === "Internet" ? cell.row.original.assigned_on : cell.row.original.updatedAt;
        const formattedDate = assignedDate ? dayjs(assignedDate).format('DD-MM-YYYY') : '-';
        return <span>{formattedDate}</span>;
      }
    },

    {
      accessorKey: 'name',
      header: 'Ngo Name',
      showByDefault: true,
      cell: ({ cell }) => {
        return (
          <Link target="_blank" className="ngo-link" to={`/masters/ngos/edit/ngoprofile/${cell.row.original.id}`}>
            {`${cell.row.original.name}`}
          </Link>
        );
      }
    },
    { accessorKey: 'ngo_type', header: 'NGO Type', showByDefault: true },
    { accessorKey: 'ngo_status', header: 'Status', showByDefault: true },
    { accessorKey: 'source', header: 'Source', showByDefault: true },
    {
      accessorKey: 'documents_uploaded',
      header: 'Docs',
      showByDefault: true,
      cell: ({ row }) => {
        const uploadedDocs = row.original.uploadedDocs || 0;
        const totalMandatoryDocs = row.original.totalMandatoryDocs || 0;

        return <span>{`${uploadedDocs}/${totalMandatoryDocs}`}</span>;
      },
      sortingFn: (rowA, rowB, columnId) => {
        const a = rowA.original.uploadedDocs || 0;
        const b = rowB.original.uploadedDocs || 0;
        return a - b;
      }
    },

    {
      accessorKey: 'assignee_id',
      header: 'Assigned To',
      showByDefault: true,
      cell: ({ cell }) => {
        const assigneeId = cell.getValue();

        if (!assigneeId) {
          return 'Unassigned';
        }
        const admin =
          admins &&
          admins.find((admin) => {
            return admin.id === assigneeId;
          });

        return admin ? admin.fullname : 'Unassigned';
      }
    },
    { accessorKey: 'darpan_id', header: 'Darpan ID', showByDefault: true },

    { accessorKey: 'email', header: 'Email', showByDefault: true },
    { accessorKey: 'point_of_contact_mobile_number', header: 'Mobile', showByDefault: true },
    { accessorKey: 'nr_city_name', header: 'City', showByDefault: true },

    { accessorKey: 'state', header: 'State', showByDefault: true },

    { accessorKey: 'pan', header: 'PAN', showByDefault: false },
    { accessorKey: 'type_of_ngo', header: 'Type of NGO', showByDefault: false },
    { accessorKey: 'website_url', header: 'Website URL', showByDefault: false },
    { accessorKey: 'point_of_contact_name', header: 'Contact Name', showByDefault: false },
    { accessorKey: 'registered_address', header: 'Registered Address', showByDefault: false },
    { accessorKey: 'current_address', header: 'Current Address', showByDefault: false },
    { accessorKey: 'date_of_establishment', header: 'Established Date', showByDefault: false },
    { accessorKey: 'latitude', header: 'Latitude', showByDefault: false },
    { accessorKey: 'longitude', header: 'Longitude', showByDefault: false },
    { accessorKey: 'pincode', header: 'Pincode', showByDefault: false },
    { accessorKey: 'place_name', header: 'Place Name', showByDefault: false },
    { accessorKey: 'claimed_ngo', header: 'Claimed NGO', showByDefault: false },
    { accessorKey: 'custom_address', header: 'Custom Address', showByDefault: false },
    { accessorKey: 'physical_evidence', header: 'Physical Evidence', showByDefault: false },
    { accessorKey: 'documents_verified', header: 'Documents Verified', showByDefault: false },
    { accessorKey: 'status', header: 'Status', showByDefault: false },
    { accessorKey: 'darpan_last_modified', header: 'Darpan Modified', showByDefault: false },
    { accessorKey: 'grade', header: 'Grade', showByDefault: false },
    { accessorKey: 'rating', header: 'Rating', showByDefault: false },

    { accessorKey: 'country', header: 'Country', showByDefault: false },
    { accessorKey: 'instagram_url', header: 'Instagram', showByDefault: false },
    { accessorKey: 'facebook_url', header: 'Facebook', showByDefault: false },
    { accessorKey: 'youtube_url', header: 'YouTube', showByDefault: false },
    { accessorKey: 'twitter_url', header: 'Twitter', showByDefault: false },

    {
      id: 'actions',
      header: 'Actions',
      cell: (cell) => {
        const ngo = cell.row.original;
        const isFeatured = ngo.isfeatured === 'yes';
        return (
          <Box display="flex" flexDirection="row">
            <TableActions handleEditClick={handleEditClick} cell={cell} handleDeleteClick={handleDelete} showDelete={false} />
            <Tooltip title="Make Inactive">
              <IconButton
                color="error"
                size="medium"
                onClick={() => {
                  setselectedNgoId(ngo.id);
                  handleSetInactive(ngo.id);
                }}
                style={{ marginLeft: '8px' }}
              >
                <StopOutlined />
              </IconButton>
            </Tooltip>
            {
              user?.roleInfo?.name == 'DR_Management' &&
                (user?.email == '<EMAIL>' || user?.email == '<EMAIL>') && (
                  <Tooltip title="Delete NGO">
                    <IconButton
                      color="error"
                      size="medium"
                      onClick={() => {
                        handleDelete(ngo.id);
                      }}
                      style={{ marginLeft: '8px' }}
                    >
                      <DeleteOutlined />
                    </IconButton>
                  </Tooltip>
                )
              // )}
            }
            {tabValue === 4 &&
              (isFeatured ? (
                <Tooltip title="Remove as Featured">
                  <IconButton
                    color="primary"
                    size="medium"
                    onClick={() => {
                      setFeaturedDialogOpen(true);
                      setselectedNgoId(ngo.id);
                      setFeatureMode('remove');
                    }}
                    style={{ marginLeft: '8px' }}
                  >
                    <StarFilled />
                  </IconButton>
                </Tooltip>
              ) : (
                <Tooltip title="Mark as Featured">
                  <IconButton
                    color="secondary"
                    size="medium"
                    onClick={() => {
                      setFeaturedDialogOpen(true);
                      setselectedNgoId(ngo.id);
                      setFeatureMode('add');
                    }}
                    style={{ marginLeft: '8px' }}
                  >
                    <StarOutlined />
                  </IconButton>
                </Tooltip>
              ))}
          </Box>
        );
      }
    }
  ];

  const verifiedColumns = [
    {
      id: 'select',
      header: ({ table }) => (
        <IndeterminateCheckbox
          {...{
            checked: table.getIsAllRowsSelected(),
            indeterminate: table.getIsSomeRowsSelected(),
            onChange: table.getToggleAllRowsSelectedHandler()
          }}
        />
      ),
      showByDefault: true,
      cell: ({ row }) => (
        <IndeterminateCheckbox
          {...{
            checked: row.getIsSelected(),
            disabled: !row.getCanSelect(),
            indeterminate: row.getIsSomeSelected(),
            onChange: row.getToggleSelectedHandler()
          }}
        />
      )
    },
    {
      accessorKey: 'name',
      header: 'Name',
      showByDefault: true,
      cell: ({ cell }) => {
        return (
          <Link target="_blank" className="ngo-link" to={`/masters/ngos/edit/ngoprofile/${cell.row.original.id}`}>
            {cell.row.original.name}
          </Link>
        );
      }
    },

    { accessorKey: 'ngo_status', header: 'Status', showByDefault: true },
    { accessorKey: 'email', header: 'Email', showByDefault: true },
    { accessorKey: 'point_of_contact_mobile_number', header: 'Contact Mobile', showByDefault: true },
    { accessorKey: 'nr_city_name', header: 'City', showByDefault: true },
    { accessorKey: 'state', header: 'State', showByDefault: true },
    {
      accessorKey: 'verified_on',
      header: 'Verified On',
      showByDefault: true,
      cell: (cell) => {
        const formattedDate = dayjs(cell.row.original.verified_on).format('DD-MM-YYYY');
        return <span>{formattedDate}</span>;
      }
    },
    {
      accessorKey: 'grade',
      header: 'Grade',
      showByDefault: true,
      cell: ({ cell }) => {
        const gradeId = cell.getValue();
        if (!gradeId) return 'Not Assigned';

        const gradeName =
          grades.find((grade) => {
            return grade.id == gradeId;
          }) || 'Unknown';
        return gradeName?.name;
      }
    },
    {
      accessorKey: 'rating',
      header: 'Rating',
      showByDefault: true,
      cell: ({ cell }) => {
        const rating = cell.getValue();
        if (!rating) return 'No ratings';
        return `${rating}⭐`;
      }
    },
    {
      id: 'actions',
      header: 'Actions',
      cell: (cell) => {
        const ngo = cell.row.original;
        const isFeatured = ngo.isfeatured === 'yes';
        return (
          <Box display="flex" flexDirection="row">
            <TableActions handleEditClick={handleEditClick} cell={cell} handleDeleteClick={handleDelete} />
            <Tooltip title="Make Inactive">
              <IconButton
                color="error"
                size="medium"
                onClick={() => {
                  setselectedNgoId(ngo.id);
                  handleSetInactive(ngo.id);
                }}
                style={{ marginLeft: '8px' }}
              >
                <StopOutlined />
              </IconButton>
            </Tooltip>
            {tabValue &&
              tabValue === 4 &&
              (isFeatured ? (
                <Tooltip title="Remove as Featured">
                  <IconButton
                    color="primary"
                    size="medium"
                    onClick={() => {
                      setFeaturedDialogOpen(true);
                      setselectedNgoId(ngo.id);
                      setFeatureMode('remove');
                    }}
                    style={{ marginLeft: '8px' }}
                  >
                    <StarFilled />
                  </IconButton>
                </Tooltip>
              ) : (
                <Tooltip title="Mark as Featured">
                  <IconButton
                    color="secondary"
                    size="medium"
                    onClick={() => {
                      setFeaturedDialogOpen(true);
                      setselectedNgoId(ngo.id);
                      setFeatureMode('add');
                    }}
                    style={{ marginLeft: '8px' }}
                  >
                    <StarOutlined />
                  </IconButton>
                </Tooltip>
              ))}
          </Box>
        );
      }
    }
  ];
  const [tabValue, setTabValue] = useState(0);

  const handleTabChange = (event, newValue) => {
    setGlobalFilter('');
    setFilters({
      source: '',
      state: '',
      assignee_id: '',
      ngo_status: ''
    });
    setRowSelection({});
    alertStatus = null;
    setTabValue(newValue);
    setCurrentPage(1);
    const selectedStatus = statusList[newValue];
    fetchNgos(selectedStatus, 'yes');
  };

  const handleCurrentPageChange = (newPage) => {
    setCurrentPage(newPage);
    setFilteredCurrentPage(newPage);
  };

  const handlePageSizeChange = (newPageSize) => {
    setPageSize(newPageSize);
    setFilteredPageSize(newPageSize);
  };

  const handleRefresh = () => {
    fetchNgos(statusList[tabValue]);
  };

  //for multiple select

  return (
    <div>
      {/* <MainCard>
        <Stack spacing={3}> */}
      {/* Sticky Header: Tabs and Buttons */}
      <Box
        sx={{
          position: 'sticky',
          top: 0,
          //   backgroundColor: 'white',
          zIndex: 1000,
          paddingBottom: 1
        }}
      >
        <Box
          sx={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center'
            // mb: 1
          }}
        >
          <Tabs
            style={{ width: '50rem' }}
            value={tabValue}
            indicatorColor="primary"
            onChange={handleTabChange}
            aria-label="product description tabs example"
            variant="scrollable"
          >
            {statusList.map((status) => {
              return status === 'All' ? (
                <Tab component={Link} to="#" label={status} />
              ) : (
                <Tab
                  component={Link}
                  iconPosition="end"
                  to="#"
                  label={status}
                  icon={<Chip label={statusCounts[status] || '0'} color="primary" variant="light" size="small" />}
                />
              );
            })}
          </Tabs>

          {user?.roleInfo?.name === 'DR_Management' && (
            <Box sx={{ ml: 'auto', display: 'flex', gap: 1 }}>
              <Button variant="contained" size="small" color="primary" sx={{ minWidth: '6rem' }} onClick={openAssignDialog}>
                Assign
              </Button>
              <Button variant="contained" sx={{ minWidth: '6rem' }} size="small" color="primary" onClick={openAssignGradeDialog}>
                Grade
              </Button>
              {(user?.email == '<EMAIL>' ||
                user?.email == '<EMAIL>' ||
                user?.email == '<EMAIL>') && (
                <Button variant="contained" sx={{ minWidth: '6rem' }} size="small" color="primary" onClick={handleMultipleNgoDelete}>
                  Delete
                </Button>
              )}
            </Box>
          )}
        </Box>
      </Box>

      {/* Scrollable Content */}
      <Box sx={{ overflowY: 'auto', maxHeight: '100vh' }}>
        {statusList.map((sl, index) => {
          return (
            <TabPanel value={tabValue} index={index} key={index}>
              {loading ? (
                <Box
                  sx={{
                    display: 'flex',
                    flexDirection: 'column',
                    justifyContent: 'center',
                    alignItems: 'center'
                  }}
                >
                  <CustomCircularLoaded open={loading} />
                  <Typography sx={{ mt: 2 }}>Loading NGO Information....</Typography>
                </Box>
              ) : (
                <CustomTableCheckBox
                  data={ngos}
                  columns={sl === 'Verified' ? verifiedColumns : columns}
                  modalToggler={handleAddOrEdit}
                  category={'NGO'}
                  statusList={statusList}
                  rowSelection={rowSelection}
                  setRowSelection={setRowSelection}
                  openAssignDialog={openAssignDialog}
                  openFeaturedDialog={openFeaturedDialog}
                  tabValue={tabValue}
                  roleName={roleInfo.name}
                  openAssignGradeDialog={openAssignGradeDialog}
                  totalCount={totalCount}
                  currentPage={currentPage}
                  pageSize={pageSize}
                  onPageChange={handleCurrentPageChange}
                  onPageSizeChange={handlePageSizeChange}
                  filters={filters}
                  setFilters={setFilters}
                  globalFilter={globalFilter}
                  setGlobalFilter={setGlobalFilter}
                  portalUsers={portalUsers}
                  states={states}
                  setFilterIsLoading={setFilterIsLoading}
                  isFilterLoading={isFilterLoading}
                  applyFilters={applyFilters}
                  filteredPageSize={pageSize}
                  filteredCurrentPage={currentPage}
                  filteredTotalCount={totalCount}
                  searchFilteredData={ngos}
                  tempSelected={tempSelected}
                  setTempSelected={setTempSelected}
                  handleRefresh={handleRefresh}
                  sorting={sorting}
                  setSorting={setSorting}
                />
              )}
            </TabPanel>
          );
        })}
      </Box>
      {/* </Stack>
      </MainCard> */}
      <Dialog
        open={assignDialogOpen}
        onClose={() => setAssignDialogOpen(false)}
        maxWidth="50%"
        PaperProps={{
          sx: {
            top: 0,
            position: 'absolute',
            borderRadius: '16px',
            width: '80%',
            maxWidth: '500px'
          }
        }}
      >
        <DialogTitle>Assign</DialogTitle>
        <DialogContent>
          <Autocomplete
            options={admins}
            getOptionLabel={(option) => option.fullname || option.email}
            onChange={(event, value) => setSelectedAdmin(value)}
            renderInput={(params) => <TextField {...params} label="Select DR Staff" />}
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setAssignDialogOpen(false)}>Cancel</Button>
          <Button variant="contained" color="primary" onClick={handleAssignSubmit}>
            Assign
          </Button>
        </DialogActions>
      </Dialog>
      <Dialog
        open={featuredDialogOpen}
        onClose={() => setFeaturedDialogOpen(false)}
        PaperProps={{
          sx: {
            borderRadius: '16px'
          }
        }}
      >
        <DialogTitle>Mark Featured</DialogTitle>
        <DialogContent>Do you want {featureMode === 'add' ? 'mark' : 'remove'} selected NGOs as featured?</DialogContent>
        <DialogActions>
          <Button onClick={() => setFeaturedDialogOpen(false)}>Cancel</Button>
          <Button variant="contained" color="primary" onClick={handleFeaturedSubmit}>
            Okay
          </Button>
        </DialogActions>
      </Dialog>
      <Dialog
        open={assignGradeDialogOpen}
        onClose={(event, reason) => {
          if (reason !== 'backdropClick' && reason !== 'escapeKeyDown') {
            setAssignGradeDialogOpen(false);
          }
        }}
        maxWidth="50%"
        PaperProps={{
          sx: {
            top: 0,
            position: 'absolute',
            borderRadius: '16px',
            width: '100%',
            maxWidth: '600px'
          }
        }}
      >
        <DialogTitle>Assign Grades and Ratings</DialogTitle>
        <DialogContent>
          <Autocomplete
            options={grades}
            sx={{ padding: '10px', fontSize: 20 }}
            getOptionLabel={(option) => option.name}
            onChange={(event, value) => setSelectedGrade(value.name)}
            renderInput={(params) => <TextField {...params} label="Select Grade" />}
          />
          <Stack direction="row" alignItems="center" spacing={2} sx={{ mt: 2, marginLeft: 2, fontSize: 20 }}>
            <Box sx={{ marginLeft: '50px' }}>Rating:</Box>
            <Rating name="grade-rating" value={rating} precision={0.5} onChange={(event, newValue) => setRating(newValue)} />
          </Stack>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setAssignGradeDialogOpen(false)}>Cancel</Button>
          <Button variant="contained" color="primary" onClick={handleAssignGradeSubmit}>
            Assign
          </Button>
        </DialogActions>
      </Dialog>
      <ToastContainer />
    </div>
  );
}
