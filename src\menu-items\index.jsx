// project import
import applications from './applications';
import widget from './widget';
import formsTables from './forms-tables';
import samplePage from './sample-page';
import chartsMap from './charts-map';
import other from './other';
import pages from './pages';
import masters from './masters';
import manage from './manage';
import channel from './channel';
import { getSessionStorageItem } from 'utils/permissionUtils';

// ==============================|| MENU ITEMS ||============================== //

const user = getSessionStorageItem('user');
let menuItems = {
  items: [manage]
};
if (user?.roleInfo?.name?.trim() !== 'NGO_Management' && user?.roleInfo?.name !== 'NGO_Staff') {
  menuItems.items.push(masters);
}
if (user?.roleInfo?.name?.trim() == 'NGO_Management' || user?.roleInfo?.name == 'NGO_Staff') {
  menuItems.items.push(channel);
}
// menuItems = {
//   items: [manage,masters]
// };

export default menuItems;
