import axios from 'axios';

const axiosServices = axios.create({ baseURL: import.meta.env.VITE_APP_APPLICATION_API_URL || 'http://localhost:3010/' });

// ==============================|| AXIOS - FOR MOCK SERVICES ||============================== //

axiosServices.interceptors.request.use(
  async (config) => {
    const accessToken = localStorage.getItem('serviceToken');
    const userInfo = localStorage.getItem('user') ? JSON.parse(localStorage.getItem('user')) : null;
    if (accessToken) {
      config.headers['Authorization'] = `Bearer ${accessToken}`;
      config.headers['isadmin'] = userInfo?.roleInfo?.name == 'DR_Staff' || userInfo?.roleInfo?.name == 'DR_Management' ? 'yes' : 'no';
      config.headers['userid'] = userInfo?.id;
    }
    return config;
  },
  (error) => {
    return error;
  }
);

axiosServices.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error?.response?.status === 401 && !window.location.href.includes('/login')) {
      localStorage.removeItem('serviceToken');
      window.location = '/';
    }
    return Promise.reject((error.response && error.response.data) || 'Wrong Services');
  }
);

export default axiosServices;

export const fetcher = async (args) => {
  const [url, config] = Array.isArray(args) ? args : [args];

  const res = await axiosServices.get(url, { ...config });
  return res.data;
};

export const fetcherPost = async (args) => {
  const [url, config] = Array.isArray(args) ? args : [args];

  const res = await axiosServices.post(url, { ...config });

  return res.data;
};
