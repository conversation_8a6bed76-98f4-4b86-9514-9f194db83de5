import { useCallback, useEffect, useState } from 'react';
import { Link as RouterLink, useNavigate, useSearchParams } from 'react-router-dom';

// material-ui
import Box from '@mui/material/Box';
import Button from '@mui/material/Button';
import FormControl from '@mui/material/FormControl';
import FormHelperText from '@mui/material/FormHelperText';
import Grid from '@mui/material/Grid';
import Link from '@mui/material/Link';
import InputAdornment from '@mui/material/InputAdornment';
import InputLabel from '@mui/material/InputLabel';
import OutlinedInput from '@mui/material/OutlinedInput';
import Stack from '@mui/material/Stack';
import Typography from '@mui/material/Typography';

// third party
import * as Yup from 'yup';
import { Formik, Form } from 'formik';

// project import
import IconButton from 'components/@extended/IconButton';
import AnimateButton from 'components/@extended/AnimateButton';

import useAuth from 'hooks/useAuth';
import useScriptRef from 'hooks/useScriptRef';
import { openSnackbar } from 'api/snackbar';
import { strengthColor, strengthIndicator } from 'utils/password-strength';

// assets
import EyeOutlined from '@ant-design/icons/EyeOutlined';
import EyeInvisibleOutlined from '@ant-design/icons/EyeInvisibleOutlined';
import { getNGOSBySearchTerm } from 'api/campaigns.service';
import { Autocomplete, TextField } from '@mui/material';

// ============================|| JWT - REGISTER ||============================ //

export default function AuthClaimNgo() {
  const { register } = useAuth();
  const scriptedRef = useScriptRef();
  const navigate = useNavigate();
  const [ngos, setNgos] = useState([]);
  const [loading, setLoading] = useState(false);
  const [selectedNgo, setSelectedNgo] = useState(null);

  const [level, setLevel] = useState();
  const [showPassword, setShowPassword] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');

  const handleClickShowPassword = () => {
    setShowPassword(!showPassword);
  };

  useEffect(() => {
    if (searchTerm && searchTerm.length > 3) {
      fetchNgos(searchTerm);
    }
  }, [searchTerm]);

  const handleMouseDownPassword = (event) => {
    event.preventDefault();
  };

  const changePassword = (value) => {
    const temp = strengthIndicator(value);
    setLevel(strengthColor(temp));
  };

  const fetchNgos = useCallback(async (query) => {
    if (query.length < 3) return;
    setLoading(true);
    try {
      const response = await getNGOSBySearchTerm(query, null);
      setNgos(response);
      setSelectedNgo(null);
    } catch (error) {
      console.error('Error fetching NGOs:', error);
    } finally {
      setLoading(false);
    }
  }, []);

  const [searchParams] = useSearchParams();
  const auth = searchParams.get('auth'); // get auth and set route based on that

  useEffect(() => {
    changePassword('');
  }, []);

  return (
    <>
      <Formik
        initialValues={{
          name: '',
          point_of_contact_name: '',
          point_of_contact_mobile_number: '',
          email: '',
          password: '',
          pan: '',
          role_id: '12'
        }}
        validationSchema={Yup.object().shape({
          name: Yup.string().max(255).required('NGO Name is required')
          //   point_of_contact_name: Yup.string().max(255).required('Point Of Contact Name is required'),
          //   email: Yup.string().email('Must be a valid email').max(255).required('Point of Contact Email is required'),
          //   point_of_contact_mobile_number: Yup.string().min(10).max(10).required('Mobile Number is required'),
          //   pan: Yup.string().min(10).max(10).required('Pan Number is required'),
          //   password: Yup.string()
          //     .required('Password is required')
          //     .test('no-leading-trailing-whitespace', 'Password cannot start or end with spaces', (value) => value === value.trim())
          //     .max(10, 'Password must be less than 10 characters')
        })}
        onSubmit={async (values, { setErrors, setStatus, setSubmitting }) => {
          try {
            values.email = values.email.trim();
            values.ngo_status = 'New';
            values.last_status = 'New';
            const response = await register(values);
            if (response?.status) {
              // if (scriptedRef.current) {
              setStatus({ success: true });
              setSubmitting(false);
              openSnackbar({
                open: true,
                message: 'Your registration has been successfully completed!',
                variant: 'alert',

                alert: {
                  color: 'success'
                }
              });

              setTimeout(() => {
                navigate('/login', { replace: true });
              }, 1500);
            }
          } catch (err) {
            openSnackbar({
              open: true,
              message: err?.response?.data?.message,
              variant: 'alert',

              alert: {
                color: 'danger'
              }
            });
            console.error(err);
            setStatus({ success: false });
            setErrors({ submit: err.message });
            setSubmitting(false);
          }
        }}
      >
        {({ errors, handleBlur, handleChange, handleSubmit, isSubmitting, touched, values, setFieldValue }) => (
          <Form noValidate onSubmit={handleSubmit} autoComplete="off">
            <Grid container spacing={3}>
              <Grid item xs={12} md={12}>
                <Stack spacing={1}>
                  <InputLabel htmlFor="name-signup">Enter NGO's legal name*</InputLabel>
                  {/* <OutlinedInput
                    id="name-login"
                    type="name"
                    value={values.name}
                    name="name"
                    onBlur={handleBlur}
                    onChange={handleChange}
                    placeholder=""
                    fullWidth
                    error={Boolean(touched.name && errors.name)}
                  /> */}
                  <Autocomplete
                    options={ngos || []}
                    name="name"
                    getOptionLabel={(option) => option.name}
                    value={ngos.find((cat) => cat.name === values.name) || null}
                    onInputChange={(event, newValue) => {
                      setSearchTerm(newValue);
                    }}
                    onChange={(event, newValue) => {
                      setSelectedNgo(newValue);
                      setFieldValue('name', newValue.name);
                    }}
                    renderInput={(params) => (
                      <TextField
                        {...params}
                        placeholder=""
                        error={touched.name && Boolean(errors.name)}
                        helperText={touched.name && errors.name}
                      />
                    )}
                  />
                </Stack>
                {/* {touched.name && errors.name && (
                  <FormHelperText error id="helper-text-name-signup">
                    {errors.name}
                  </FormHelperText>
                )} */}
              </Grid>
              <Grid item xs={12} md={12}>
                <Stack spacing={1}>
                  <InputLabel htmlFor="pan-signup">Registered Address</InputLabel>
                  <OutlinedInput
                    fullWidth
                    id="pan-signup"
                    value={selectedNgo?.registered_address}
                    name="pan"
                    onBlur={handleBlur}
                    onChange={handleChange}
                    placeholder="AXAAXXXXX"
                    inputProps={{}}
                    disabled={true}
                    rows={4}
                    multiline
                  />
                </Stack>
                {touched.pan && errors.pan && (
                  <FormHelperText error id="helper-text-pan-signup">
                    {errors.pan}
                  </FormHelperText>
                )}
              </Grid>
              <Grid item xs={12}>
                <Stack spacing={1}>
                  <InputLabel htmlFor="email-signup">Email Address*</InputLabel>
                  <OutlinedInput
                    fullWidth
                    error={Boolean(touched.email && errors.email)}
                    id="email-login"
                    type="email"
                    value={selectedNgo?.email}
                    name="email"
                    onBlur={handleBlur}
                    onChange={handleChange}
                    placeholder="<EMAIL>"
                    inputProps={{}}
                    disabled={true}
                  />
                </Stack>
                {touched.email && errors.email && (
                  <FormHelperText error id="helper-text-email-signup">
                    {errors.email}
                  </FormHelperText>
                )}
              </Grid>
              <Grid item xs={6}>
                <Stack spacing={1}>
                  <InputLabel htmlFor="mobile_number-signup">Mobile Number</InputLabel>
                  <OutlinedInput
                    fullWidth
                    error={Boolean(touched.point_of_contact_mobile_number && errors.point_of_contact_mobile_number)}
                    id="mobile_number-signup"
                    value={selectedNgo?.point_of_contact_mobile_number}
                    name="point_of_contact_mobile_number"
                    onBlur={handleBlur}
                    onChange={handleChange}
                    placeholder="1234512345"
                    inputProps={{}}
                    disabled={true}
                  />
                </Stack>
                {touched.point_of_contact_mobile_number && errors.point_of_contact_mobile_number && (
                  <FormHelperText error id="helper-text-mobile_number-signup">
                    {errors.point_of_contact_mobile_number}
                  </FormHelperText>
                )}
              </Grid>

              <Grid item xs={6}>
                <Stack spacing={1}>
                  <InputLabel htmlFor="point_of_contact_name-signup">Darpan ID*</InputLabel>
                  <OutlinedInput
                    fullWidth
                    error={Boolean(touched.point_of_contact_name && errors.point_of_contact_name)}
                    id="point_of_contact_name-signup"
                    type="point_of_contact_name"
                    value={selectedNgo?.darpan_id}
                    name="point_of_contact_name"
                    onBlur={handleBlur}
                    onChange={handleChange}
                    placeholder="John Doe"
                    inputProps={{}}
                    disabled={true}
                  />
                </Stack>
              </Grid>

              <Grid item xs={12}>
                <Typography variant="body2">
                  By Signing up, you agree to our &nbsp;
                  <Link variant="subtitle2" component={RouterLink} to="#">
                    Terms of Service
                  </Link>
                  &nbsp; and &nbsp;
                  <Link variant="subtitle2" component={RouterLink} to="#">
                    Privacy Policy
                  </Link>
                </Typography>
              </Grid>
              {errors.submit && (
                <Grid item xs={12}>
                  <FormHelperText error>{errors.submit}</FormHelperText>
                </Grid>
              )}
              <Grid item xs={12}>
                <AnimateButton>
                  <Button disableElevation disabled={isSubmitting} fullWidth size="large" type="submit" variant="contained" color="primary">
                    Claim Your NGO
                  </Button>
                </AnimateButton>
              </Grid>
            </Grid>
          </Form>
        )}
      </Formik>
    </>
  );
}
